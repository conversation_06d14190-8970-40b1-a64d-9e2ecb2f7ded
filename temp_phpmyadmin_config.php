<?php

$cfg['ThemeDefault'] = 'metro';

$i = 0;

$i++;
$cfg['Servers'][$i]['auth_type'] = 'cookie';   
$cfg['Servers'][$i]['host'] = 'localhost';     
$cfg['Servers'][$i]['connect_type'] = 'tcp';   
$cfg['Servers'][$i]['compress'] = false;       
$cfg['Servers'][$i]['AllowNoPassword'] = false;

$cfg['UploadDir'] = '';
$cfg['SaveDir'] = '';

include('config.inc_db.php');
include('config.inc_controluser.php');
include('config.inc_settings.php');

// Reverse proxy configuration for HTTPS support
$cfg['PmaAbsoluteUri'] = 'https://fastpanel.streamdb.online/phpmyadmin/';
$cfg['ForceSSL'] = false;

// Trust reverse proxy headers - comprehensive detection
if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
    $_SERVER['HTTPS'] = 'on';
    $_SERVER['SERVER_PORT'] = 443;
}

// Additional reverse proxy detection
if (isset($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on') {
    $_SERVER['HTTPS'] = 'on';
}

// Force HTTPS detection for reverse proxy
if (!empty($_SERVER['HTTP_HOST']) && $_SERVER['HTTP_HOST'] === 'fastpanel.streamdb.online') {
    $_SERVER['HTTPS'] = 'on';
    $_SERVER['SERVER_PORT'] = 443;
}

// Disable HTTPS mismatch warnings
$cfg['CheckConfigurationPermissions'] = false;

// Force HTTPS detection
$cfg['is_https'] = true;
