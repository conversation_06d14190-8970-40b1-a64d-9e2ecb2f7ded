/**
 * Setup Admin User for Testing
 * Creates an admin user in the database so you can test the new admin panel features
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'dbadmin_streamdb',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'streamdb_database',
  multipleStatements: true
};

// Use socket only on Linux/Unix systems
if (process.platform !== 'win32' && process.env.DB_SOCKET) {
  dbConfig.socketPath = process.env.DB_SOCKET;
  delete dbConfig.host;
  delete dbConfig.port;
}

async function setupAdminUser() {
  let connection;
  
  try {
    console.log('🔌 Connecting to database...');
    console.log('Database config:', {
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      database: dbConfig.database,
      socketPath: dbConfig.socketPath
    });
    
    // Create connection
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database successfully');
    
    // Check if admin_users table exists
    console.log('🔍 Checking if admin_users table exists...');
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'admin_users'"
    );
    
    if (tables.length === 0) {
      console.log('❌ admin_users table does not exist. Creating it...');
      
      // Create admin_users table
      await connection.execute(`
        CREATE TABLE admin_users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          email VARCHAR(100) UNIQUE NOT NULL,
          role ENUM('admin', 'moderator') DEFAULT 'admin',
          is_active BOOLEAN DEFAULT TRUE,
          failed_login_attempts INT DEFAULT 0,
          locked_until DATETIME NULL,
          last_login DATETIME NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      
      console.log('✅ admin_users table created successfully');
    } else {
      console.log('✅ admin_users table already exists');
    }
    
    // Check if admin user already exists
    console.log('🔍 Checking for existing admin users...');
    const [existingUsers] = await connection.execute(
      'SELECT COUNT(*) as count FROM admin_users'
    );
    
    if (existingUsers[0].count > 0) {
      console.log('ℹ️  Admin users already exist in the database:');
      const [users] = await connection.execute(
        'SELECT id, username, email, role, is_active, last_login, created_at FROM admin_users'
      );
      
      console.table(users);
      console.log('✅ Database setup is complete. You can use existing credentials to login.');
      return;
    }
    
    // Create admin user from environment variables
    const adminUsername = process.env.ADMIN_USERNAME || 'admin_4_adminpanel';
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'Ohno@U2foundme';
    
    console.log('👤 Creating admin user...');
    console.log('Username:', adminUsername);
    console.log('Email:', adminEmail);
    
    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(adminPassword, saltRounds);
    
    // Insert admin user
    const [result] = await connection.execute(
      `INSERT INTO admin_users (username, password_hash, email, role, is_active, created_at)
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [adminUsername, passwordHash, adminEmail, 'admin', 1]
    );
    
    console.log('✅ Admin user created successfully!');
    console.log('User ID:', result.insertId);
    console.log('');
    console.log('🔐 Login Credentials:');
    console.log('Username:', adminUsername);
    console.log('Password:', adminPassword);
    console.log('');
    console.log('🌐 You can now login to the admin panel at: https://streamdb.online/admin');
    
  } catch (error) {
    console.error('❌ Error setting up admin user:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Database connection refused. Make sure MySQL is running.');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 Access denied. Check your database credentials in .env file.');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 Database does not exist. Make sure the database name is correct.');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the setup
setupAdminUser();
