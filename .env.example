# StreamDB Authentication Configuration
# Copy this file to .env and update the values for your environment

# TMDB API Configuration (existing)
VITE_TMDB_API_KEY=your_tmdb_api_key_here
VITE_TMDB_BASE_URL=https://api.themoviedb.org/3
VITE_TMDB_IMAGE_BASE_URL=https://image.tmdb.org/t/p

# Authentication Configuration
# Note: These are for development only. In production, use server-side authentication
VITE_AUTH_ENCRYPTION_KEY=StreamDB_Auth_2024_Secure_Key_v1_Change_This_In_Production
VITE_AUTH_SESSION_TIMEOUT=86400000

# Security Configuration
VITE_ENABLE_SECURITY_LOGGING=true

# Development Settings
VITE_DEV_MODE=true
VITE_ENABLE_DEMO_CREDENTIALS=true

# Production Database Configuration (for future server-side implementation)
# These should NOT be prefixed with VITE_ as they should not be exposed to the client
# DATABASE_URL=your_database_connection_string
# JWT_SECRET=your_jwt_secret_key
# ADMIN_USERNAME=your_admin_username
# ADMIN_PASSWORD_HASH=your_hashed_admin_password
# SESSION_SECRET=your_session_secret_key
# CORS_ORIGIN=https://yourdomain.com
# API_BASE_URL=https://api.yourdomain.com
