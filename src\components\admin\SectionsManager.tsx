import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Plus, Edit, Trash2, Eye, Settings, ArrowUp, ArrowDown, 
  Folder, Film, Tv, Clock, Star, Calendar, Users, Home,
  Navigation, Palette, Hash, Type, Filter, Save, X
} from "lucide-react";
import apiService from "@/services/apiService";
import { ContentSection, Category, SectionFormData, sectionUtils } from "@/types/sections";

const ICON_OPTIONS = [
  { value: 'Folder', label: 'Folder', icon: Folder },
  { value: 'Film', label: 'Film', icon: Film },
  { value: 'Tv', label: 'TV', icon: Tv },
  { value: 'Clock', label: 'Clock', icon: Clock },
  { value: 'Star', label: 'Star', icon: Star },
  { value: 'Calendar', label: 'Calendar', icon: Calendar },
  { value: 'Users', label: 'Users', icon: Users },
  { value: 'Home', label: 'Home', icon: Home },
];

const COLOR_OPTIONS = [
  '#e11d48', '#3b82f6', '#f59e0b', '#10b981', '#8b5cf6', 
  '#ef4444', '#06b6d4', '#84cc16', '#f97316', '#ec4899'
];

const CONTENT_TYPE_OPTIONS = [
  { value: 'movie', label: 'Movies' },
  { value: 'series', label: 'Series' },
  { value: 'requested', label: 'Requested' },
];

export default function SectionsManager() {
  const { toast } = useToast();
  
  // State management
  const [sections, setSections] = useState<ContentSection[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSection, setEditingSection] = useState<ContentSection | null>(null);
  const [formData, setFormData] = useState<SectionFormData>(sectionUtils.getDefaultFormData());

  // Load sections and categories
  const loadData = async () => {
    setIsLoading(true);
    try {
      const [sectionsResult, categoriesResult] = await Promise.all([
        apiService.getSections({ include_categories: true }),
        apiService.getCategories()
      ]);

      if (sectionsResult.success) {
        setSections(sectionsResult.data);
      } else {
        toast({
          title: "Error",
          description: sectionsResult.message || "Failed to load sections",
          variant: "destructive",
        });
      }

      if (categoriesResult.success) {
        setCategories(categoriesResult.data);
      } else {
        toast({
          title: "Error",
          description: categoriesResult.message || "Failed to load categories",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to load data:', error);
      toast({
        title: "Error",
        description: "Failed to load sections and categories",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Form handlers
  const handleInputChange = (field: keyof SectionFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-generate slug from name
    if (field === 'name' && typeof value === 'string') {
      const slug = sectionUtils.generateSlug(value);
      setFormData(prev => ({ ...prev, slug }));
    }
  };

  const handleContentTypeToggle = (type: string) => {
    setFormData(prev => ({
      ...prev,
      content_types: prev.content_types.includes(type)
        ? prev.content_types.filter(t => t !== type)
        : [...prev.content_types, type]
    }));
  };

  const handleCategoryToggle = (categoryId: number) => {
    setFormData(prev => ({
      ...prev,
      category_ids: prev.category_ids.includes(categoryId)
        ? prev.category_ids.filter(id => id !== categoryId)
        : [...prev.category_ids, categoryId]
    }));
  };

  // CRUD operations
  const handleCreate = () => {
    setEditingSection(null);
    setFormData(sectionUtils.getDefaultFormData());
    setIsDialogOpen(true);
  };

  const handleEdit = (section: ContentSection) => {
    setEditingSection(section);
    setFormData({
      name: section.name,
      slug: section.slug,
      description: section.description,
      icon: section.icon,
      color: section.color,
      display_order: section.display_order,
      is_active: section.is_active,
      show_in_navigation: section.show_in_navigation,
      show_on_homepage: section.show_on_homepage,
      max_items_homepage: section.max_items_homepage,
      content_types: section.content_types,
      filter_rules: section.filter_rules,
      category_ids: section.categories?.map(c => c.id) || [],
    });
    setIsDialogOpen(true);
  };

  const handleSave = async () => {
    // Validate form
    const validation = sectionUtils.validateSectionForm(formData);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.errors.join(', '),
        variant: "destructive",
      });
      return;
    }

    try {
      let result;
      if (editingSection) {
        result = await apiService.updateSection(editingSection.id, formData);
      } else {
        result = await apiService.createSection(formData);
      }

      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        setIsDialogOpen(false);
        await loadData(); // Reload data
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to save section:', error);
      toast({
        title: "Error",
        description: "Failed to save section",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (section: ContentSection) => {
    if (!window.confirm(`Are you sure you want to delete "${section.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await apiService.deleteSection(section.id);

      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        await loadData(); // Reload data
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to delete section:', error);
      toast({
        title: "Error",
        description: "Failed to delete section",
        variant: "destructive",
      });
    }
  };

  const handleReorder = async (sectionId: number, direction: 'up' | 'down') => {
    const currentIndex = sections.findIndex(s => s.id === sectionId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= sections.length) return;

    // Create new order
    const reorderedSections = [...sections];
    [reorderedSections[currentIndex], reorderedSections[newIndex]] = 
    [reorderedSections[newIndex], reorderedSections[currentIndex]];

    // Update display_order values
    const updateData = reorderedSections.map((section, index) => ({
      id: section.id,
      display_order: index
    }));

    try {
      const result = await apiService.reorderSections(updateData);
      
      if (result.success) {
        setSections(reorderedSections.map((section, index) => ({
          ...section,
          display_order: index
        })));
        toast({
          title: "Success",
          description: "Section order updated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to reorder sections:', error);
      toast({
        title: "Error",
        description: "Failed to update section order",
        variant: "destructive",
      });
    }
  };

  const getIconComponent = (iconName: string) => {
    const iconOption = ICON_OPTIONS.find(option => option.value === iconName);
    return iconOption ? iconOption.icon : Folder;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading sections...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-primary">Content Sections</h2>
          <p className="text-muted-foreground">
            Manage dynamic content sections like Movies, Web Series, etc.
          </p>
        </div>
        
        <Button onClick={handleCreate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Section
        </Button>
      </div>

      {/* Sections Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {sections.slice(0, 4).map((section) => {
          const IconComponent = getIconComponent(section.icon);
          return (
            <Card key={section.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="p-2 rounded-lg"
                      style={{ backgroundColor: `${section.color}20`, color: section.color }}
                    >
                      <IconComponent className="h-4 w-4" />
                    </div>
                    <CardTitle className="text-sm">{section.name}</CardTitle>
                  </div>
                  <Badge variant={section.is_active ? "default" : "secondary"}>
                    {section.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Content:</span>
                    <span>{section.content_count || 0} items</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Navigation:</span>
                    <span>{section.show_in_navigation ? "Yes" : "No"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Homepage:</span>
                    <span>{section.show_on_homepage ? "Yes" : "No"}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Sections Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Sections</CardTitle>
          <CardDescription>
            Manage all content sections, their order, and settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Section</TableHead>
                  <TableHead>Content Types</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Display</TableHead>
                  <TableHead>Content Count</TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sections.map((section, index) => {
                  const IconComponent = getIconComponent(section.icon);
                  return (
                    <TableRow key={section.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div
                            className="p-2 rounded-lg"
                            style={{ backgroundColor: `${section.color}20`, color: section.color }}
                          >
                            <IconComponent className="h-4 w-4" />
                          </div>
                          <div>
                            <div className="font-medium">{section.name}</div>
                            <div className="text-sm text-muted-foreground">/{section.slug}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1 flex-wrap">
                          {section.content_types.map(type => (
                            <Badge key={type} variant="outline" className="text-xs">
                              {type}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={section.is_active ? "default" : "secondary"}>
                          {section.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {section.show_in_navigation && (
                            <Badge variant="outline" className="text-xs">
                              <Navigation className="h-3 w-3 mr-1" />
                              Nav
                            </Badge>
                          )}
                          {section.show_on_homepage && (
                            <Badge variant="outline" className="text-xs">
                              <Home className="h-3 w-3 mr-1" />
                              Home
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{section.content_count || 0}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReorder(section.id, 'up')}
                            disabled={index === 0}
                          >
                            <ArrowUp className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReorder(section.id, 'down')}
                            disabled={index === sections.length - 1}
                          >
                            <ArrowDown className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex gap-2 justify-end">
                          <Button variant="ghost" size="sm" onClick={() => handleEdit(section)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDelete(section)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingSection ? 'Edit Section' : 'Create New Section'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Section Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., Movies, Web Series"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug *</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  placeholder="e.g., movies, web-series"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Brief description of this section"
                rows={3}
              />
            </div>

            {/* Visual Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Icon</Label>
                <Select value={formData.icon} onValueChange={(value) => handleInputChange('icon', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ICON_OPTIONS.map(option => {
                      const IconComponent = option.icon;
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-4 w-4" />
                            {option.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Color</Label>
                <div className="flex gap-2 flex-wrap">
                  {COLOR_OPTIONS.map(color => (
                    <button
                      key={color}
                      type="button"
                      className={`w-8 h-8 rounded-lg border-2 ${
                        formData.color === color ? 'border-foreground' : 'border-transparent'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => handleInputChange('color', color)}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Content Types */}
            <div className="space-y-2">
              <Label>Content Types</Label>
              <div className="flex gap-2 flex-wrap">
                {CONTENT_TYPE_OPTIONS.map(option => (
                  <Button
                    key={option.value}
                    type="button"
                    variant={formData.content_types.includes(option.value) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleContentTypeToggle(option.value)}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Categories */}
            <div className="space-y-2">
              <Label>Associated Categories</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-lg p-3">
                {categories.map(category => (
                  <label key={category.id} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.category_ids.includes(category.id)}
                      onChange={() => handleCategoryToggle(category.id)}
                      className="rounded"
                    />
                    <span className="text-sm">{category.name}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Display Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="is_active">Active</Label>
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show_in_navigation">Show in Navigation</Label>
                  <Switch
                    id="show_in_navigation"
                    checked={formData.show_in_navigation}
                    onCheckedChange={(checked) => handleInputChange('show_in_navigation', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show_on_homepage">Show on Homepage</Label>
                  <Switch
                    id="show_on_homepage"
                    checked={formData.show_on_homepage}
                    onCheckedChange={(checked) => handleInputChange('show_on_homepage', checked)}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="display_order">Display Order</Label>
                  <Input
                    id="display_order"
                    type="number"
                    min="0"
                    value={formData.display_order}
                    onChange={(e) => handleInputChange('display_order', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_items_homepage">Max Items on Homepage</Label>
                  <Input
                    id="max_items_homepage"
                    type="number"
                    min="1"
                    max="100"
                    value={formData.max_items_homepage}
                    onChange={(e) => handleInputChange('max_items_homepage', parseInt(e.target.value) || 20)}
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                {editingSection ? 'Update Section' : 'Create Section'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
