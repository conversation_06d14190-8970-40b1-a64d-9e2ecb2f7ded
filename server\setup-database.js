#!/usr/bin/env node

/**
 * Database Setup and Verification Script
 * Run this on the production server to set up the database
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function setupDatabase() {
  console.log('🗄️  Setting up StreamDB Online Database...');
  
  try {
    const config = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4',
      timezone: '+00:00'
    };

    // Use socket for production
    if (process.env.DB_SOCKET) {
      config.socketPath = process.env.DB_SOCKET;
      console.log(`🔌 Using socket connection: ${process.env.DB_SOCKET}`);
    } else {
      config.host = process.env.DB_HOST || 'localhost';
      config.port = process.env.DB_PORT || 3306;
      console.log(`🔌 Using TCP connection: ${config.host}:${config.port}`);
    }

    const connection = await mysql.createConnection(config);
    console.log('✅ Database connection successful');
    
    // Check if tables exist
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 Database has ${tables.length} tables`);
    
    if (tables.length === 0) {
      console.log('⚠️  Database is empty. Please import the schema:');
      console.log('   1. Open phpMyAdmin in FastPanel');
      console.log('   2. Select your database');
      console.log('   3. Import the schema.sql file');
    } else {
      console.log('✅ Database schema appears to be imported');
    }
    
    await connection.end();
    console.log('🏁 Database setup check completed');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  }
}

setupDatabase();
