import { Episode, Season, MediaItem } from './media';

// Form data interface for content editing
export interface ContentFormData {
  title: string;
  type: string;
  category: string;
  section: string;
  tmdbId: string;
  year: string;
  genres: string[];
  languages: string[];
  description: string;
  posterUrl: string;
  thumbnailUrl: string;
  videoLinks: string;
  secureVideoLinks: string;
  quality: string[];
  tags: string;
  imdbRating: string;
  runtime: string;
  studio: string;
  audioTracks: string[];
  trailer: string;
  subtitleFile: File | null;
  subtitleUrl: string;
  isPublished: boolean;
  isFeatured: boolean;
  addToCarousel: boolean;
  totalSeasons: number;
  totalEpisodes: number;
}

// Episode form data for adding/editing episodes
export interface EpisodeFormData {
  title: string;
  season: number;
  episode: number;
  description: string;
  videoLink: string; // Raw video links (one per line)
  secureVideoLinks?: string; // Encoded video links for security
  runtime?: string;
  airDate?: string;
  thumbnailUrl?: string;
}

// Season form data for adding/editing seasons
export interface SeasonFormData {
  seasonNumber: number;
  title?: string;
  description?: string;
  posterUrl?: string;
}

// Content management action types
export type ContentAction = 
  | 'edit'
  | 'delete'
  | 'view'
  | 'add-episode'
  | 'add-season'
  | 'edit-episode'
  | 'edit-season'
  | 'delete-episode'
  | 'delete-season'
  | 'toggle-featured'
  | 'toggle-carousel'
  | 'change-status';

// Content status options
export type ContentStatus = 'Published' | 'Draft' | 'Archived';

// Sort options for content management
export type SortField = 'title' | 'type' | 'year' | 'status' | 'createdAt' | 'updatedAt';
export type SortOrder = 'asc' | 'desc';

// Filter options for content management
export interface ContentFilters {
  type?: string;
  status?: string;
  featured?: boolean;
  carousel?: boolean;
  genre?: string;
  year?: number;
  search?: string;
}

// Content management state
export interface ContentManagementState {
  content: MediaItem[];
  filteredContent: MediaItem[];
  selectedItems: string[];
  sortBy: SortField;
  sortOrder: SortOrder;
  filters: ContentFilters;
  isLoading: boolean;
  error?: string;
}

// Episode management state
export interface EpisodeManagementState {
  episodes: Episode[];
  selectedEpisode?: Episode;
  isAddingEpisode: boolean;
  isEditingEpisode: boolean;
  episodeFormData: EpisodeFormData;
}

// Season management state
export interface SeasonManagementState {
  seasons: Season[];
  selectedSeason?: Season;
  isAddingSeason: boolean;
  isEditingSeason: boolean;
  seasonFormData: SeasonFormData;
}

// Constants for form validation and options
export const CONTENT_TYPES = ['movie', 'series'] as const;
export const CONTENT_STATUSES: ContentStatus[] = ['Published', 'Draft', 'Archived'];
export const LANGUAGES = [
  "Hindi", "English", "Tamil", "Telugu", "Malayalam", "Korean", "Japanese", "Anime"
];
export const QUALITY_OPTIONS = ["HD", "WEB", "BluRay", "Cam", "HDTS", "HDTC"];
export const GENRE_OPTIONS = [
  "Action", "Adventure", "Animation", "Comedy", "Crime", "Documentary",
  "Drama", "Family", "Fantasy", "History", "Horror", "Music", "Mystery",
  "Romance", "Sci-Fi", "Thriller", "War", "Western", "TV Series"
];

export const CATEGORIES = [
  "Hindi Movies",
  "Hindi Web Series",
  "English Movies",
  "English Web Series",
  "Telugu Movies",
  "Telugu Web Series",
  "Tamil Movies",
  "Tamil Web Series",
  "Malayalam Movies",
  "Malayalam Web Series",
  "Korean Movies",
  "Korean Web Series",
  "Japanese Movies",
  "Japanese Web Series",
  "Anime",
  "Hindi Dubbed",
  "English Dubbed",
  "Animation"
];
