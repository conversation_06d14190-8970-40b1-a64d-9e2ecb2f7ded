# 🔧 StreamDB Automated Maintenance Installation Guide

## 📋 OVERVIEW

This guide provides step-by-step instructions for installing automated maintenance scripts on your two-server StreamDB infrastructure. The scripts will run every Thursday at midnight with proper coordination between servers.

### Architecture Overview
```
Backend Server (***********)          Reverse Proxy (*************)
- Maintenance: Thursday 00:00          - Maintenance: Thursday 00:30
- FastPanel, MySQL, PM2, StreamDB      - Nginx, SSL, Firewall
```

### Key Features
- ✅ **Service Protection**: Maintains all running services during updates
- ✅ **Automatic Scheduling**: Runs every Thursday at midnight
- ✅ **Rollback Capability**: Automatic service restoration if issues occur
- ✅ **Comprehensive Logging**: Detailed logs of all maintenance activities
- ✅ **Safety Measures**: Prevents disruption to your reverse proxy architecture

---

## 📁 REQUIRED FILES

Before starting, ensure you have these files:

1. **backend-maintenance.sh** - Backend server maintenance script
2. **reverse-proxy-maintenance.sh** - Reverse proxy maintenance script
3. **cron-configuration.sh** - Cron job setup script
4. **validation-scripts.sh** - Testing and validation script

---

## 🖥️ PHASE 1: BA<PERSON>KEND SERVER INSTALLATION (***********)

### Step 1.1: Connect to Backend Server
```bash
ssh root@***********
```

### Step 1.2: Install Prerequisites
```bash
# Update system
apt update

# Install required packages
apt install -y unattended-upgrades curl wget logrotate

# Verify PM2 is installed
pm2 --version

# If PM2 is not installed:
npm install -g pm2
```

### Step 1.3: Upload and Install Backend Maintenance Script
```bash
# Create directory for scripts
mkdir -p /usr/local/bin
mkdir -p /var/log/streamdb-maintenance
mkdir -p /var/backups/streamdb-maintenance

# Upload the backend maintenance script
# Method 1: Using SCP from your local machine
scp backend-maintenance.sh root@***********:/usr/local/bin/

# Method 2: Using FastPanel File Manager
# Upload to /usr/local/bin/ via FastPanel interface

# Set proper permissions
chmod +x /usr/local/bin/backend-maintenance.sh
chown root:root /usr/local/bin/backend-maintenance.sh
```

### Step 1.4: Configure Unattended Upgrades
```bash
# Configure unattended upgrades for security updates
cat > /etc/apt/apt.conf.d/50unattended-upgrades << 'EOF'
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
EOF

# Enable automatic updates
echo 'APT::Periodic::Update-Package-Lists "1";' > /etc/apt/apt.conf.d/20auto-upgrades
echo 'APT::Periodic::Unattended-Upgrade "1";' >> /etc/apt/apt.conf.d/20auto-upgrades
```

### Step 1.5: Test Backend Maintenance Script
```bash
# Upload validation script
scp validation-scripts.sh root@***********:/usr/local/bin/
chmod +x /usr/local/bin/validation-scripts.sh

# Run validation tests
/usr/local/bin/validation-scripts.sh --full-test

# Expected output: All validation tests should pass
```

### Step 1.6: Install Cron Job for Backend
```bash
# Upload cron configuration script
scp cron-configuration.sh root@***********:/usr/local/bin/
chmod +x /usr/local/bin/cron-configuration.sh

# Run cron configuration
/usr/local/bin/cron-configuration.sh

# Verify cron job installation
crontab -l
# Should show: 0 0 * * 4 /usr/local/bin/backend-maintenance.sh
```

---

## 🔄 PHASE 2: REVERSE PROXY SERVER INSTALLATION (*************)

### Step 2.1: Connect to Reverse Proxy Server
```bash
ssh root@*************
```

### Step 2.2: Install Prerequisites
```bash
# Update system
apt update

# Install required packages
apt install -y unattended-upgrades curl wget logrotate

# Verify Nginx is installed and running
systemctl status nginx
```

### Step 2.3: Upload and Install Reverse Proxy Maintenance Script
```bash
# Create directory for scripts
mkdir -p /usr/local/bin
mkdir -p /var/log/streamdb-maintenance
mkdir -p /var/backups/streamdb-maintenance

# Upload the reverse proxy maintenance script
scp reverse-proxy-maintenance.sh root@*************:/usr/local/bin/

# Set proper permissions
chmod +x /usr/local/bin/reverse-proxy-maintenance.sh
chown root:root /usr/local/bin/reverse-proxy-maintenance.sh
```

### Step 2.4: Configure Unattended Upgrades
```bash
# Configure unattended upgrades (same as backend)
cat > /etc/apt/apt.conf.d/50unattended-upgrades << 'EOF'
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
EOF

# Enable automatic updates
echo 'APT::Periodic::Update-Package-Lists "1";' > /etc/apt/apt.conf.d/20auto-upgrades
echo 'APT::Periodic::Unattended-Upgrade "1";' >> /etc/apt/apt.conf.d/20auto-upgrades
```

### Step 2.5: Test Reverse Proxy Maintenance Script
```bash
# Upload validation script
scp validation-scripts.sh root@*************:/usr/local/bin/
chmod +x /usr/local/bin/validation-scripts.sh

# Run validation tests
/usr/local/bin/validation-scripts.sh --full-test

# Expected output: All validation tests should pass
```

### Step 2.6: Install Cron Job for Reverse Proxy
```bash
# Upload cron configuration script
scp cron-configuration.sh root@*************:/usr/local/bin/
chmod +x /usr/local/bin/cron-configuration.sh

# Run cron configuration
/usr/local/bin/cron-configuration.sh

# Verify cron job installation
crontab -l
# Should show: 30 0 * * 4 /usr/local/bin/reverse-proxy-maintenance.sh
```

---

## ✅ PHASE 3: VERIFICATION AND TESTING

### Step 3.1: Verify Installation on Both Servers

**On Backend Server (***********):**
```bash
# Check script installation
ls -la /usr/local/bin/backend-maintenance.sh
ls -la /usr/local/bin/validation-scripts.sh

# Check directories
ls -la /var/log/streamdb-maintenance/
ls -la /var/backups/streamdb-maintenance/

# Check cron job
crontab -l | grep backend-maintenance

# Test script syntax
bash -n /usr/local/bin/backend-maintenance.sh
```

**On Reverse Proxy Server (*************):**
```bash
# Check script installation
ls -la /usr/local/bin/reverse-proxy-maintenance.sh
ls -la /usr/local/bin/validation-scripts.sh

# Check directories
ls -la /var/log/streamdb-maintenance/
ls -la /var/backups/streamdb-maintenance/

# Check cron job
crontab -l | grep reverse-proxy-maintenance

# Test script syntax
bash -n /usr/local/bin/reverse-proxy-maintenance.sh
```

### Step 3.2: Run Comprehensive Validation

**On both servers, run:**
```bash
/usr/local/bin/validation-scripts.sh --full-test
```

**Expected Results:**
- ✅ All prerequisites validated
- ✅ Service baseline captured
- ✅ Website connectivity tests passed
- ✅ Script syntax validation passed
- ✅ Dry run completed successfully

### Step 3.3: Test Manual Execution (Optional)

**⚠️ CAUTION: This will run actual maintenance. Only do this during a maintenance window.**

**On Backend Server:**
```bash
# Run actual maintenance test
/usr/local/bin/validation-scripts.sh --run-maintenance
```

**On Reverse Proxy Server (wait 5 minutes after backend):**
```bash
# Run actual maintenance test
/usr/local/bin/validation-scripts.sh --run-maintenance
```

---

## 📅 PHASE 4: SCHEDULING VERIFICATION

### Step 4.1: Verify Cron Schedule

**Check next scheduled maintenance:**
```bash
# On both servers
date -d "next thursday"
# This shows when the next Thursday is

# Check cron jobs are properly scheduled
grep -r "streamdb" /var/spool/cron/ /etc/cron.d/ 2>/dev/null
```

### Step 4.2: Verify Timing Coordination

**Maintenance Schedule:**
- **Backend Server**: Thursday 00:00 (midnight)
- **Reverse Proxy**: Thursday 00:30 (30 minutes later)

This ensures the backend server completes maintenance before the reverse proxy starts, maintaining service availability.

---

## 📊 PHASE 5: MONITORING AND LOGGING

### Step 5.1: Log Locations

**Backend Server Logs:**
```bash
# Cron execution log
tail -f /var/log/streamdb-maintenance/cron-backend.log

# Detailed maintenance logs
ls -la /var/log/streamdb-maintenance/backend-maintenance-*.log

# Latest maintenance log
tail -f /var/log/streamdb-maintenance/backend-maintenance-$(date +%Y%m%d)-*.log
```

**Reverse Proxy Server Logs:**
```bash
# Cron execution log
tail -f /var/log/streamdb-maintenance/cron-proxy.log

# Detailed maintenance logs
ls -la /var/log/streamdb-maintenance/proxy-maintenance-*.log

# Latest maintenance log
tail -f /var/log/streamdb-maintenance/proxy-maintenance-$(date +%Y%m%d)-*.log
```

### Step 5.2: Backup Locations

**Configuration Backups:**
```bash
# Backend server backups
ls -la /var/backups/streamdb-maintenance/config-backup-*.tar.gz
ls -la /var/backups/streamdb-maintenance/mysql-backup-*.sql.gz

# Reverse proxy backups
ls -la /var/backups/streamdb-maintenance/proxy-config-backup-*.tar.gz
```

### Step 5.3: Health Check Commands

**Quick Health Check:**
```bash
# Check all services are running
systemctl status nginx mysql fastpanel
pm2 status

# Check website connectivity
curl -I https://streamdb.online
curl -I https://fastpanel.streamdb.online
curl https://streamdb.online/api/health
```

---

## 🚨 TROUBLESHOOTING

### Issue 1: Script Permission Denied
```bash
# Fix permissions
chmod +x /usr/local/bin/backend-maintenance.sh
chmod +x /usr/local/bin/reverse-proxy-maintenance.sh
chown root:root /usr/local/bin/*maintenance.sh
```

### Issue 2: Cron Job Not Running
```bash
# Check cron service
systemctl status cron
systemctl restart cron

# Check cron logs
tail -f /var/log/cron.log
tail -f /var/log/syslog | grep CRON
```

### Issue 3: Validation Tests Failing
```bash
# Run individual tests
/usr/local/bin/validation-scripts.sh --prerequisites
/usr/local/bin/validation-scripts.sh --connectivity
/usr/local/bin/validation-scripts.sh --syntax
```

### Issue 4: Services Not Restarting After Maintenance
```bash
# Manually restart services
systemctl restart nginx mysql fastpanel
pm2 restart streamdb-online

# Check service logs
journalctl -u nginx -f
journalctl -u mysql -f
pm2 logs streamdb-online
```

---

## 📋 MAINTENANCE SCHEDULE SUMMARY

### Weekly Maintenance Timeline (Every Thursday)

**00:00** - Backend Server Maintenance Starts
- Security updates applied
- OS updates applied
- System cleanup performed
- Database backup created
- Services monitored and restored

**00:30** - Reverse Proxy Maintenance Starts
- Waits for backend completion
- Security updates applied
- OS updates applied
- Nginx optimization
- SSL certificate verification

**01:00** - Maintenance Complete (estimated)
- All services restored
- Health checks performed
- Logs generated

### What Gets Maintained

**Backend Server:**
- ✅ Security patches
- ✅ OS updates (safe only)
- ✅ Package cleanup
- ✅ Log rotation
- ✅ Database backup
- ✅ FastPanel protection
- ✅ PM2 process protection
- ✅ MySQL service protection

**Reverse Proxy Server:**
- ✅ Security patches
- ✅ OS updates (safe only)
- ✅ Package cleanup
- ✅ Log rotation
- ✅ Nginx optimization
- ✅ SSL certificate verification
- ✅ Firewall maintenance

---

## 🎯 FINAL VERIFICATION CHECKLIST

Before considering the installation complete, verify:

- [ ] Both maintenance scripts installed and executable
- [ ] Validation scripts pass all tests on both servers
- [ ] Cron jobs scheduled correctly (00:00 backend, 00:30 proxy)
- [ ] Log directories created with proper permissions
- [ ] Backup directories created
- [ ] Unattended upgrades configured
- [ ] Website connectivity working
- [ ] All services running normally

**Your automated maintenance system is now ready!** 🚀

The scripts will automatically maintain your StreamDB infrastructure every Thursday at midnight while preserving your reverse proxy architecture and all running services.

---

---

## 📖 SCRIPT DOCUMENTATION

### Backend Maintenance Script Features

**Security Measures:**
- Lock file prevents multiple instances
- Service state capture and restoration
- Configuration backup before changes
- Database backup before maintenance
- Rollback capability on failures

**Maintenance Tasks:**
- Security updates via unattended-upgrade
- Safe OS updates (no major version changes)
- Package cache cleanup
- Temporary file cleanup
- Log rotation and cleanup
- Old kernel removal

**Service Protection:**
- FastPanel service monitoring
- PM2 application monitoring
- MySQL service monitoring
- Nginx service monitoring
- Automatic service restoration

### Reverse Proxy Maintenance Script Features

**Security Measures:**
- Waits for backend maintenance completion
- Backend connectivity verification
- Nginx configuration validation
- SSL certificate verification
- Service state preservation

**Maintenance Tasks:**
- Security updates application
- System cleanup and optimization
- Nginx log management
- SSL certificate monitoring
- Firewall status verification

**Safety Features:**
- Configuration backup creation
- Service state capture/restore
- Health checks before/after maintenance
- Comprehensive error logging

### Validation Script Features

**Pre-Maintenance Checks:**
- Prerequisites validation
- Service baseline capture
- Website connectivity testing
- Script syntax validation
- Dry run simulation

**Post-Maintenance Validation:**
- Service state verification
- Website functionality testing
- Health check execution
- Comprehensive reporting

---

**Document Version:** 1.0
**Last Updated:** 2025-01-02
**For:** StreamDB.online Two-Server Infrastructure
