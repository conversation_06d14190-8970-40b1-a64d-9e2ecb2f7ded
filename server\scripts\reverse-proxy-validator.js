#!/usr/bin/env node

/**
 * Reverse Proxy Architecture Validator
 * Comprehensive validation of two-tier offshore VPS reverse proxy setup
 * Flow: Client → Cloudflare → ************* (Proxy) → *********** (Backend)
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const fs = require('fs');
const path = require('path');

require('dotenv').config({ path: path.join(__dirname, '../.env') });

class ReverseProxyValidator {
  constructor() {
    this.proxyServerIP = '*************';
    this.backendServerIP = '***********';
    this.domain = 'streamdb.online';
    this.results = {
      timestamp: new Date().toISOString(),
      architecture: {
        expected: 'Client → Cloudflare → ************* → ***********',
        proxyServer: this.proxyServerIP,
        backendServer: this.backendServerIP,
        domain: this.domain
      },
      tests: [],
      summary: { passed: 0, failed: 0, warnings: 0 }
    };
  }

  log(level, test, message, details = {}) {
    const result = { level, test, message, details, timestamp: new Date().toISOString() };
    this.results.tests.push(result);
    this.results.summary[level === 'PASS' ? 'passed' : level === 'FAIL' ? 'failed' : 'warnings']++;

    const colors = {
      PASS: '\x1b[32m✅',
      FAIL: '\x1b[31m❌',
      WARN: '\x1b[33m⚠️',
      INFO: '\x1b[34mℹ️'
    };

    console.log(`${colors[level] || colors.INFO} ${test}: ${message}\x1b[0m`);
    if (Object.keys(details).length > 0) {
      console.log(`   Details:`, details);
    }
  }

  async testDNSResolution() {
    this.log('INFO', 'DNS Resolution', 'Testing domain DNS resolution');

    try {
      // Test A record resolution
      const { stdout: digOutput } = await execAsync(`dig +short A ${this.domain}`);
      const resolvedIPs = digOutput.trim().split('\n').filter(ip => ip.length > 0);

      if (resolvedIPs.length === 0) {
        this.log('FAIL', 'DNS Resolution', 'Domain does not resolve to any IP');
        return false;
      }

      // Check if domain resolves to proxy server (expected)
      if (resolvedIPs.includes(this.proxyServerIP)) {
        this.log('PASS', 'DNS Resolution', 'Domain resolves to proxy server (correct)', {
          resolvedIPs: resolvedIPs,
          proxyServerIP: this.proxyServerIP
        });
      } else if (resolvedIPs.includes(this.backendServerIP)) {
        this.log('FAIL', 'DNS Resolution', 'Domain resolves directly to backend server (security risk)', {
          resolvedIPs: resolvedIPs,
          backendServerIP: this.backendServerIP,
          recommendation: 'Update DNS to point to proxy server only'
        });
        return false;
      } else {
        this.log('INFO', 'DNS Resolution', 'Domain resolves to other IPs (possibly Cloudflare)', {
          resolvedIPs: resolvedIPs,
          note: 'This is expected if using Cloudflare proxy'
        });
      }

      return true;
    } catch (error) {
      this.log('FAIL', 'DNS Resolution', 'DNS resolution test failed', { error: error.message });
      return false;
    }
  }

  async testCloudflareIntegration() {
    this.log('INFO', 'Cloudflare Integration', 'Testing Cloudflare proxy detection');

    try {
      // Test for Cloudflare headers
      const { stdout: curlOutput } = await execAsync(`curl -s -I https://${this.domain} | head -20`);
      
      const hasCloudflareHeaders = curlOutput.includes('cf-ray') || 
                                   curlOutput.includes('cloudflare') || 
                                   curlOutput.includes('CF-');

      if (hasCloudflareHeaders) {
        this.log('PASS', 'Cloudflare Integration', 'Cloudflare proxy detected', {
          evidence: 'Cloudflare headers found in response'
        });
      } else {
        this.log('WARN', 'Cloudflare Integration', 'Cloudflare headers not detected', {
          note: 'May not be using Cloudflare proxy or headers are stripped'
        });
      }

      // Test SSL certificate
      const sslTest = curlOutput.includes('HTTP/2 200') || curlOutput.includes('HTTPS');
      if (sslTest) {
        this.log('PASS', 'SSL Certificate', 'HTTPS is working correctly');
      } else {
        this.log('WARN', 'SSL Certificate', 'HTTPS may not be properly configured');
      }

      return true;
    } catch (error) {
      this.log('WARN', 'Cloudflare Integration', 'Could not test Cloudflare integration', {
        error: error.message
      });
      return true; // Don't fail the entire test for this
    }
  }

  async testProxyServerConnectivity() {
    this.log('INFO', 'Proxy Server', 'Testing proxy server connectivity');

    try {
      // Test if proxy server is reachable
      const { stdout: pingOutput } = await execAsync(`ping -c 3 ${this.proxyServerIP}`);
      
      if (pingOutput.includes('3 received')) {
        this.log('PASS', 'Proxy Server Ping', 'Proxy server is reachable', {
          proxyServerIP: this.proxyServerIP
        });
      } else {
        this.log('WARN', 'Proxy Server Ping', 'Proxy server ping test inconclusive', {
          note: 'Server may block ICMP or be behind firewall'
        });
      }

      // Test HTTP connectivity to proxy server
      try {
        const { stdout: httpTest } = await execAsync(`curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 http://${this.proxyServerIP}`);
        
        if (httpTest.trim() === '200' || httpTest.trim() === '301' || httpTest.trim() === '302') {
          this.log('PASS', 'Proxy Server HTTP', 'Proxy server responds to HTTP requests');
        } else {
          this.log('WARN', 'Proxy Server HTTP', 'Proxy server HTTP response unexpected', {
            responseCode: httpTest.trim()
          });
        }
      } catch (error) {
        this.log('WARN', 'Proxy Server HTTP', 'Could not test HTTP connectivity to proxy server');
      }

      return true;
    } catch (error) {
      this.log('WARN', 'Proxy Server', 'Proxy server connectivity test failed', {
        error: error.message
      });
      return true; // Don't fail for network tests
    }
  }

  async testBackendServerSecurity() {
    this.log('INFO', 'Backend Security', 'Testing backend server security and IP hiding');

    try {
      // Test 1: Check if backend server IP is hidden from external access
      const { stdout: externalIP } = await execAsync('curl -s -m 10 https://ipinfo.io/ip || curl -s -m 10 https://api.ipify.org || echo "unknown"');
      const detectedIP = externalIP.trim();

      if (detectedIP === this.backendServerIP) {
        this.log('FAIL', 'IP Hiding', 'Backend server IP is exposed to public internet', {
          exposedIP: detectedIP,
          recommendation: 'Configure firewall to block direct access to backend server',
          securityRisk: 'HIGH - Backend server can be accessed directly'
        });
      } else if (detectedIP === this.proxyServerIP) {
        this.log('PASS', 'IP Hiding', 'Proxy server IP detected (good)', {
          detectedIP: detectedIP
        });
      } else {
        this.log('INFO', 'IP Hiding', 'External IP detection result', {
          detectedIP: detectedIP,
          note: 'IP may be masked by Cloudflare or other proxy'
        });
      }

      // Test 2: Check open ports on backend server
      const { stdout: netstatOutput } = await execAsync('netstat -tlnp | grep LISTEN');
      const openPorts = [];
      
      netstatOutput.split('\n').forEach(line => {
        if (line.includes('0.0.0.0:') || line.includes(':::')) {
          const match = line.match(/:(\d+)\s/);
          if (match) openPorts.push(match[1]);
        }
      });

      const criticalPorts = openPorts.filter(port => 
        !['22', '80', '443'].includes(port) // Only these should be open to public
      );

      if (criticalPorts.length > 0) {
        this.log('WARN', 'Port Security', 'Additional ports exposed to public', {
          exposedPorts: criticalPorts,
          recommendation: 'Close unnecessary ports: ' + criticalPorts.join(', '),
          securityNote: 'These ports may compromise reverse proxy effectiveness'
        });
      } else {
        this.log('PASS', 'Port Security', 'Only essential ports (22, 80, 443) are exposed');
      }

      // Test 3: Check if backend services are accessible externally
      const backendPorts = ['3001', '9000']; // Node.js and webhook ports
      for (const port of backendPorts) {
        if (openPorts.includes(port)) {
          this.log('WARN', 'Service Exposure', `Backend service port ${port} is exposed`, {
            port: port,
            recommendation: `Restrict port ${port} to localhost or proxy server only`,
            securityRisk: 'Backend services should not be directly accessible'
          });
        } else {
          this.log('PASS', 'Service Exposure', `Backend service port ${port} is properly restricted`);
        }
      }

      return true;
    } catch (error) {
      this.log('WARN', 'Backend Security', 'Backend security test failed', {
        error: error.message
      });
      return true;
    }
  }

  async testProxyConfiguration() {
    this.log('INFO', 'Proxy Configuration', 'Testing reverse proxy configuration');

    try {
      // Test if application is configured for proxy environment
      const trustProxy = process.env.TRUST_PROXY;
      const frontendUrl = process.env.FRONTEND_URL;

      if (frontendUrl && frontendUrl.includes('https://')) {
        this.log('PASS', 'HTTPS Configuration', 'Application configured for HTTPS');
      } else {
        this.log('WARN', 'HTTPS Configuration', 'Application may not be configured for HTTPS');
      }

      // Check Express.js trust proxy setting
      if (trustProxy === 'true' || process.env.NODE_ENV === 'production') {
        this.log('PASS', 'Trust Proxy', 'Application configured to trust proxy headers');
      } else {
        this.log('WARN', 'Trust Proxy', 'Application may not trust proxy headers', {
          recommendation: 'Set TRUST_PROXY=true in .env file'
        });
      }

      // Test proxy headers handling
      const corsOrigin = process.env.CORS_ORIGIN;
      if (corsOrigin && corsOrigin.includes(this.domain)) {
        this.log('PASS', 'CORS Configuration', 'CORS configured for domain');
      } else {
        this.log('WARN', 'CORS Configuration', 'CORS may not be properly configured for domain');
      }

      return true;
    } catch (error) {
      this.log('FAIL', 'Proxy Configuration', 'Proxy configuration test failed', {
        error: error.message
      });
      return false;
    }
  }

  async testEndToEndConnectivity() {
    this.log('INFO', 'End-to-End Test', 'Testing complete proxy chain connectivity');

    try {
      // Test API endpoint through the full proxy chain
      const testEndpoints = [
        '/api/health',
        '/api/webhook/health'
      ];

      for (const endpoint of testEndpoints) {
        try {
          const { stdout: response } = await execAsync(`curl -s -o /dev/null -w "%{http_code}" https://${this.domain}${endpoint}`);
          
          if (response.trim() === '200') {
            this.log('PASS', 'End-to-End API', `${endpoint} accessible through proxy chain`);
          } else {
            this.log('WARN', 'End-to-End API', `${endpoint} returned ${response.trim()}`);
          }
        } catch (error) {
          this.log('WARN', 'End-to-End API', `Could not test ${endpoint}`, {
            error: error.message
          });
        }
      }

      return true;
    } catch (error) {
      this.log('FAIL', 'End-to-End Test', 'End-to-end connectivity test failed', {
        error: error.message
      });
      return false;
    }
  }

  async runAllTests() {
    console.log('\n🔍 Reverse Proxy Architecture Validator\n');
    console.log('🏗️ Expected Architecture:');
    console.log('   Client → Cloudflare → ************* (Proxy) → *********** (Backend)');
    console.log('=' .repeat(80));

    await this.testDNSResolution();
    await this.testCloudflareIntegration();
    await this.testProxyServerConnectivity();
    await this.testBackendServerSecurity();
    await this.testProxyConfiguration();
    await this.testEndToEndConnectivity();

    console.log('\n📊 Reverse Proxy Validation Summary:');
    console.log('=' .repeat(50));
    console.log(`✅ Passed: ${this.results.summary.passed}`);
    console.log(`❌ Failed: ${this.results.summary.failed}`);
    console.log(`⚠️  Warnings: ${this.results.summary.warnings}`);

    // Security recommendations
    const securityIssues = this.results.tests.filter(t => 
      t.level === 'FAIL' && (t.test.includes('Security') || t.test.includes('IP Hiding'))
    );

    if (securityIssues.length > 0) {
      console.log('\n🚨 Critical Security Issues:');
      securityIssues.forEach(issue => {
        console.log(`❌ ${issue.test}: ${issue.message}`);
        if (issue.details.recommendation) {
          console.log(`   💡 Fix: ${issue.details.recommendation}`);
        }
      });
    }

    // Save results
    const resultsFile = path.join(__dirname, '../logs/reverse-proxy-validation.json');
    try {
      fs.mkdirSync(path.dirname(resultsFile), { recursive: true });
      fs.writeFileSync(resultsFile, JSON.stringify(this.results, null, 2));
      console.log(`\n📄 Full results saved to: ${resultsFile}`);
    } catch (error) {
      console.log(`\n⚠️  Could not save results: ${error.message}`);
    }

    return this.results;
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new ReverseProxyValidator();
  validator.runAllTests().catch(console.error);
}

module.exports = ReverseProxyValidator;
