
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Mail, Copy, Clock } from "lucide-react";
import { toast } from "sonner";

export default function Contact() {
  const navigate = useNavigate();
  const email = "<EMAIL>";

  const handleCopyEmail = () => {
    navigator.clipboard.writeText(email);
    toast.success("Email address copied to clipboard!");
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />
      <main className="flex-1 flex flex-col items-center px-2 py-10">
        <h1
          className="text-3xl sm:text-4xl md:text-5xl font-bold mb-7 text-center uppercase"
          style={{ color: '#e6cb8e', textShadow: '0 0 10px #e6cb8e, 0 0 20px #e6cb8e60' }}
        >
          Contact Us
        </h1>
        <div className="max-w-3xl w-full bg-card rounded-2xl shadow-xl p-6 sm:p-8 mb-8 border border-border flex flex-col gap-8 items-center">
          
          <div className="text-center">
            <Mail className="w-16 h-16 mx-auto mb-4" style={{ color: '#e6cb8e' }} />
            <h2 className="text-2xl font-bold mb-2" style={{ color: '#e6cb8e' }}>Get in Touch</h2>
            <p className="text-muted-foreground text-lg">Have questions, suggestions, or need support? We'd love to hear from you!</p>
          </div>

          <div className="w-full bg-secondary/30 p-6 rounded-lg border border-border text-center">
            <div className="flex items-center justify-center gap-3 mb-3">
              <Mail className="w-5 h-5" style={{ color: '#e6cb8e' }}/>
              <p className="font-semibold">Email Address</p>
            </div>
            <a href={`mailto:${email}`} className="bg-background/70 p-4 rounded-md flex flex-col items-center gap-4 w-full group">
              <div className="w-full text-center">
                <span className="font-mono text-lg group-hover:opacity-80 break-all" style={{ color: '#e6cb8e' }}>{email}</span>
              </div>
              <Button 
                onClick={(e) => { e.preventDefault(); handleCopyEmail(); }} 
                size="sm" 
                className="bg-primary/20 text-primary border border-primary hover:bg-primary hover:text-primary-foreground w-full sm:w-48"
              >
                <Copy className="mr-2 h-4 w-4" />
                Copy Email
              </Button>
            </a>
            <p className="text-xs text-muted-foreground mt-3">Click the email to open your default mail client or copy the address</p>
          </div>

          <div className="flex items-center gap-3 text-muted-foreground">
            <Clock className="w-5 h-5" />
            <span>We typically respond within 24-48 hours</span>
          </div>

        </div>
        <Button
          variant="outline"
          onClick={() => navigate("/")}
          className="mb-2 text-lg border-primary text-primary hover:bg-primary hover:text-primary-foreground"
        >
          Back to Home
        </Button>
      </main>
      <Footer />
    </div>
  );
}
