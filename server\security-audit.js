#!/usr/bin/env node

/**
 * StreamDB Online - Security Audit Script
 * 
 * Performs comprehensive security checks:
 * 1. Environment variable validation
 * 2. File permission checks
 * 3. Database connection security
 * 4. Frontend credential exposure check
 * 5. Dependency vulnerability scan
 * 6. Configuration validation
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class SecurityAuditor {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.passed = [];
  }

  addIssue(category, message, severity = 'high') {
    this.issues.push({ category, message, severity });
  }

  addWarning(category, message) {
    this.warnings.push({ category, message });
  }

  addPassed(category, message) {
    this.passed.push({ category, message });
  }

  async checkFileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async checkFilePermissions(filePath, expectedMode) {
    try {
      const stats = await fs.stat(filePath);
      const actualMode = stats.mode & parseInt('777', 8);
      return actualMode === expectedMode;
    } catch {
      return false;
    }
  }

  async loadEnvFile() {
    try {
      const envContent = await fs.readFile('.env', 'utf8');
      const env = {};
      
      envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            env[key.trim()] = valueParts.join('=').trim();
          }
        }
      });
      
      return env;
    } catch (error) {
      throw new Error(`Failed to load .env file: ${error.message}`);
    }
  }

  async auditEnvironmentVariables() {
    log('\n🔍 Auditing Environment Variables...', 'cyan');
    
    const envExists = await this.checkFileExists('.env');
    if (!envExists) {
      this.addIssue('Environment', 'No .env file found', 'critical');
      return;
    }

    const env = await this.loadEnvFile();
    
    // Check required variables
    const required = [
      'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
      'JWT_SECRET', 'SESSION_SECRET', 'NODE_ENV'
    ];

    for (const key of required) {
      if (!env[key]) {
        this.addIssue('Environment', `Missing required variable: ${key}`, 'critical');
      } else {
        this.addPassed('Environment', `Required variable present: ${key}`);
      }
    }

    // Check secret strength
    if (env.JWT_SECRET && env.JWT_SECRET.length < 64) {
      this.addIssue('Security', 'JWT_SECRET is too short (minimum 64 characters)', 'high');
    } else if (env.JWT_SECRET) {
      this.addPassed('Security', 'JWT_SECRET has adequate length');
    }

    if (env.SESSION_SECRET && env.SESSION_SECRET.length < 64) {
      this.addIssue('Security', 'SESSION_SECRET is too short (minimum 64 characters)', 'high');
    } else if (env.SESSION_SECRET) {
      this.addPassed('Security', 'SESSION_SECRET has adequate length');
    }

    // Check for default/weak values
    const defaultSecrets = [
      'your_very_long_random_jwt_secret_key_here',
      'your_very_long_random_session_secret_key_here',
      'change_this_in_production'
    ];

    for (const secret of defaultSecrets) {
      if (env.JWT_SECRET && env.JWT_SECRET.includes(secret)) {
        this.addIssue('Security', 'JWT_SECRET contains default/placeholder text', 'critical');
      }
      if (env.SESSION_SECRET && env.SESSION_SECRET.includes(secret)) {
        this.addIssue('Security', 'SESSION_SECRET contains default/placeholder text', 'critical');
      }
    }

    // Check NODE_ENV
    if (env.NODE_ENV !== 'production') {
      this.addWarning('Configuration', `NODE_ENV is '${env.NODE_ENV}', should be 'production'`);
    } else {
      this.addPassed('Configuration', 'NODE_ENV is set to production');
    }

    // Check BCRYPT_ROUNDS
    const bcryptRounds = parseInt(env.BCRYPT_ROUNDS || '10');
    if (bcryptRounds < 12) {
      this.addWarning('Security', `BCRYPT_ROUNDS is ${bcryptRounds}, recommend 12 or higher`);
    } else {
      this.addPassed('Security', `BCRYPT_ROUNDS is adequately set to ${bcryptRounds}`);
    }
  }

  async auditFilePermissions() {
    log('\n🔒 Auditing File Permissions...', 'cyan');
    
    // Check .env file permissions
    const envExists = await this.checkFileExists('.env');
    if (envExists) {
      const hasSecurePerms = await this.checkFilePermissions('.env', 0o600);
      if (hasSecurePerms) {
        this.addPassed('Permissions', '.env file has secure permissions (600)');
      } else {
        this.addIssue('Permissions', '.env file does not have secure permissions (should be 600)', 'high');
      }
    }

    // Check upload directories
    const uploadDirs = ['uploads', 'uploads/images', 'uploads/videos', 'uploads/subtitles'];
    for (const dir of uploadDirs) {
      const exists = await this.checkFileExists(dir);
      if (exists) {
        this.addPassed('Directories', `Upload directory exists: ${dir}`);
      } else {
        this.addWarning('Directories', `Upload directory missing: ${dir}`);
      }
    }
  }

  async auditFrontendSecurity() {
    log('\n🌐 Auditing Frontend Security...', 'cyan');
    
    // Check for exposed credentials in frontend
    const frontendFiles = [
      '../src/config/auth.ts',
      '../.env',
      '../src/services/apiService.js'
    ];

    for (const file of frontendFiles) {
      const exists = await this.checkFileExists(file);
      if (exists) {
        try {
          const content = await fs.readFile(file, 'utf8');
          
          // Check for hardcoded credentials
          const credentialPatterns = [
            /password.*[:=]\s*['"`][^'"`]+['"`]/gi,
            /secret.*[:=]\s*['"`][^'"`]+['"`]/gi,
            /key.*[:=]\s*['"`][^'"`]+['"`]/gi,
            /token.*[:=]\s*['"`][^'"`]+['"`]/gi
          ];

          let foundCredentials = false;
          for (const pattern of credentialPatterns) {
            const matches = content.match(pattern);
            if (matches) {
              foundCredentials = true;
              this.addIssue('Frontend Security', 
                `Potential hardcoded credentials found in ${file}: ${matches[0].substring(0, 50)}...`, 
                'critical'
              );
            }
          }

          if (!foundCredentials) {
            this.addPassed('Frontend Security', `No hardcoded credentials found in ${file}`);
          }

          // Check for DEMO_CREDENTIALS in production
          if (content.includes('DEMO_CREDENTIALS') && content.includes('streamdb2024')) {
            this.addIssue('Frontend Security', 
              `Demo credentials found in ${file} - should be removed in production`, 
              'critical'
            );
          }

        } catch (error) {
          this.addWarning('Frontend Security', `Could not read ${file}: ${error.message}`);
        }
      }
    }
  }

  async auditDatabaseSecurity() {
    log('\n💾 Auditing Database Security...', 'cyan');
    
    try {
      const env = await this.loadEnvFile();
      
      // Check if using socket connection (more secure)
      if (env.DB_SOCKET) {
        this.addPassed('Database', 'Using socket connection (more secure than TCP)');
      } else if (env.DB_PORT === '3306') {
        this.addWarning('Database', 'Using TCP connection on port 3306 (consider socket connection)');
      }

      // Check database credentials strength
      if (env.DB_PASSWORD && env.DB_PASSWORD.length < 16) {
        this.addWarning('Database', 'Database password is shorter than 16 characters');
      } else if (env.DB_PASSWORD) {
        this.addPassed('Database', 'Database password has adequate length');
      }

      // Check for default database names/users
      const defaultValues = ['root', 'admin', 'test', 'demo'];
      if (defaultValues.includes(env.DB_USER)) {
        this.addWarning('Database', `Database user '${env.DB_USER}' appears to be a default value`);
      }

    } catch (error) {
      this.addIssue('Database', `Could not audit database configuration: ${error.message}`, 'medium');
    }
  }

  async auditDependencies() {
    log('\n📦 Auditing Dependencies...', 'cyan');
    
    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      
      // Check for known vulnerable packages (basic check)
      const potentiallyVulnerable = ['lodash', 'moment', 'request'];
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      for (const pkg of potentiallyVulnerable) {
        if (dependencies[pkg]) {
          this.addWarning('Dependencies', `Package '${pkg}' may have known vulnerabilities - run npm audit`);
        }
      }

      this.addPassed('Dependencies', 'Basic dependency check completed - run "npm audit" for detailed scan');

    } catch (error) {
      this.addWarning('Dependencies', `Could not read package.json: ${error.message}`);
    }
  }

  generateReport() {
    log('\n📊 Security Audit Report', 'bright');
    log('========================', 'bright');
    
    // Summary
    log(`\n📈 Summary:`, 'cyan');
    log(`✅ Passed: ${this.passed.length}`, 'green');
    log(`⚠️  Warnings: ${this.warnings.length}`, 'yellow');
    log(`❌ Issues: ${this.issues.length}`, 'red');

    // Critical issues
    const criticalIssues = this.issues.filter(i => i.severity === 'critical');
    if (criticalIssues.length > 0) {
      log(`\n🚨 CRITICAL ISSUES (${criticalIssues.length}):`, 'red');
      criticalIssues.forEach(issue => {
        log(`❌ [${issue.category}] ${issue.message}`, 'red');
      });
    }

    // High priority issues
    const highIssues = this.issues.filter(i => i.severity === 'high');
    if (highIssues.length > 0) {
      log(`\n⚠️  HIGH PRIORITY ISSUES (${highIssues.length}):`, 'yellow');
      highIssues.forEach(issue => {
        log(`⚠️  [${issue.category}] ${issue.message}`, 'yellow');
      });
    }

    // Warnings
    if (this.warnings.length > 0) {
      log(`\n⚠️  WARNINGS (${this.warnings.length}):`, 'yellow');
      this.warnings.forEach(warning => {
        log(`⚠️  [${warning.category}] ${warning.message}`, 'yellow');
      });
    }

    // Security score
    const totalChecks = this.passed.length + this.warnings.length + this.issues.length;
    const score = totalChecks > 0 ? Math.round((this.passed.length / totalChecks) * 100) : 0;
    
    log(`\n🎯 Security Score: ${score}%`, score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red');
    
    if (criticalIssues.length > 0) {
      log('\n🚨 IMMEDIATE ACTION REQUIRED: Fix critical issues before deployment!', 'red');
      return false;
    }
    
    if (score >= 80) {
      log('\n✅ Security audit passed! Ready for production.', 'green');
      return true;
    } else {
      log('\n⚠️  Security audit completed with warnings. Review issues before deployment.', 'yellow');
      return false;
    }
  }

  async runAudit() {
    log('🔐 StreamDB Online - Security Audit', 'bright');
    log('===================================', 'bright');
    
    try {
      await this.auditEnvironmentVariables();
      await this.auditFilePermissions();
      await this.auditFrontendSecurity();
      await this.auditDatabaseSecurity();
      await this.auditDependencies();
      
      return this.generateReport();
    } catch (error) {
      log(`\n❌ Audit failed: ${error.message}`, 'red');
      return false;
    }
  }
}

// Run audit if called directly
if (require.main === module) {
  const auditor = new SecurityAuditor();
  auditor.runAudit().then(passed => {
    process.exit(passed ? 0 : 1);
  }).catch(error => {
    console.error('Audit failed:', error);
    process.exit(1);
  });
}

module.exports = SecurityAuditor;
