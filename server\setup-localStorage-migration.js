/**
 * Setup script for localStorage migration to database
 * Creates all necessary tables for session management, tracking, and authentication
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+00:00',
  ssl: false
};

// Use socket connection for production (more secure)
if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
  dbConfig.socketPath = process.env.DB_SOCKET;
  console.log('Using MySQL socket connection for production');
} else {
  // Use TCP connection for development or when socket not available
  dbConfig.host = process.env.DB_HOST || 'localhost';
  dbConfig.port = process.env.DB_PORT || 3306;
  console.log(`Using MySQL TCP connection: ${dbConfig.host}:${dbConfig.port}`);
}

async function setupDatabase() {
  let connection;
  
  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected successfully');

    // Read the schema file
    const schemaPath = path.join(__dirname, '..', 'database', 'localStorage_migration_schema.sql');
    console.log('📖 Reading schema file:', schemaPath);
    
    const schemaSQL = await fs.readFile(schemaPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔄 Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.toLowerCase().includes('delimiter')) {
        // Skip DELIMITER statements as they're not needed in Node.js
        continue;
      }
      
      try {
        await connection.execute(statement);
        console.log(`✅ Statement ${i + 1}/${statements.length} executed successfully`);
      } catch (error) {
        if (error.code === 'ER_TABLE_EXISTS_ERROR' || error.code === 'ER_SP_ALREADY_EXISTS') {
          console.log(`⚠️  Statement ${i + 1}/${statements.length} skipped (already exists)`);
        } else {
          console.error(`❌ Error executing statement ${i + 1}:`, error.message);
          console.error('Statement:', statement.substring(0, 100) + '...');
          throw error;
        }
      }
    }

    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    const tables = [
      'user_sessions',
      'ad_blocker_tracking', 
      'login_attempts',
      'security_logs',
      'auth_tokens'
    ];

    for (const table of tables) {
      const [rows] = await connection.execute(`SHOW TABLES LIKE '${table}'`);
      if (rows.length > 0) {
        console.log(`✅ Table '${table}' exists`);
      } else {
        console.error(`❌ Table '${table}' was not created`);
      }
    }

    // Check if event scheduler is enabled
    const [schedulerRows] = await connection.execute("SHOW VARIABLES LIKE 'event_scheduler'");
    if (schedulerRows.length > 0) {
      const schedulerStatus = schedulerRows[0].Value;
      console.log(`📅 Event scheduler status: ${schedulerStatus}`);
      
      if (schedulerStatus !== 'ON') {
        console.log('⚠️  Event scheduler is not enabled. Daily cleanup may not run automatically.');
        console.log('   To enable: SET GLOBAL event_scheduler = ON;');
      }
    }

    console.log('🎉 localStorage migration setup completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('   - All database tables created');
    console.log('   - Session management ready');
    console.log('   - Ad blocker tracking ready');
    console.log('   - Login attempts tracking ready');
    console.log('   - Security logging ready');
    console.log('   - Auth token management ready');
    console.log('   - Automatic cleanup scheduled');
    console.log('');
    console.log('🚀 You can now restart your server to use database storage!');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run setup
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
