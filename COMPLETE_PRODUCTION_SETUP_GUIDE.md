# 🚀 COMPLETE PRODUCTION SETUP GUIDE

## 📍 **YOUR SETUP DETAILS**

**Your Server Path**: `/var/www/streamdb_onl_usr/data/www/streamdb.online`  
**Database**: `streamdb_database` (✅ Connected successfully)  
**Database User**: `dbadmin_streamdb`  
**Socket Path**: `/var/run/mysqld/mysqld.sock` (✅ Working)  

## 🎯 **WHAT WE'RE ACCOMPLISHING**

1. **✅ Clean Codebase**: Removed all dummy/test data
2. **🔒 Single Secure Admin**: One ultra-secure credential
3. **📁 Upload to Server**: Your existing path structure
4. **🔐 Database Integration**: Secure local connection
5. **🔄 GitHub Auto-Sync**: Automatic updates from repo

---

## 📋 **PHASE 1: CODEBASE CLEANUP (COMPLETED)**

### ✅ **What I've Cleaned:**
- **Removed all dummy data** from `src/data/movies.ts`
- **Kept only production-ready code**
- **Preserved all functionality**
- **Maintained mobile responsiveness**
- **No breaking changes**

### ✅ **Files Updated:**
- `src/data/movies.ts` - Now clean, database-ready
- `src/services/apiService.js` - Added for database integration
- All security features preserved

---

## 📋 **PHASE 2: UPLOAD CODEBASE TO YOUR SERVER**

### **Step 2.1: Prepare Your Local Codebase**

**On your local machine:**

```bash
# Navigate to your project directory
cd /path/to/your/Streaming_DB

# Create a clean build (if using build process)
npm run build

# Create a zip file of your entire codebase
zip -r streaming-website-production.zip . -x "node_modules/*" ".git/*" "*.log" ".env*"
```

### **Step 2.2: Upload via FastPanel File Manager**

1. **Login to FastPanel**
2. **Go to File Manager**
3. **Navigate to**: `/var/www/streamdb_onl_usr/data/www/streamdb.online`
4. **Upload your zip file**
5. **Extract the zip file** in the directory

### **Step 2.3: Alternative - Direct Upload Structure**

**Upload these directories to your server path:**

```
/var/www/streamdb_onl_usr/data/www/streamdb.online/
├── src/                    # Your frontend source code
├── public/                 # Public assets
├── server/                 # Backend API (we'll create this)
├── database/              # Database scripts
├── package.json           # Dependencies
├── index.html            # Main HTML file
├── vite.config.ts        # Build configuration
└── .gitignore            # Git ignore file
```

### **Step 2.4: Set Up Server Directory Structure**

**SSH into your server or use FastPanel terminal:**

```bash
# Navigate to your website directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Create server directory structure
mkdir -p server/{routes,config,middleware}
mkdir -p uploads/{images,videos,subtitles,temp}
mkdir -p database
mkdir -p logs

# Set proper permissions
chown -R www-data:www-data /var/www/streamdb_onl_usr/data/www/streamdb.online
chmod -R 755 /var/www/streamdb_onl_usr/data/www/streamdb.online
chmod -R 777 uploads/
```

---

## 📋 **PHASE 3: SINGLE SECURE ADMIN CREDENTIAL**

### **Step 3.1: Create Ultra-Secure Admin User**

**In your database (via phpMyAdmin or MySQL command):**

```sql
-- Create ONE secure admin user with ultra-security
INSERT INTO admin_users (
    username, 
    password_hash, 
    email, 
    role, 
    is_active,
    permissions,
    created_at
) VALUES (
    'streamdb_admin',  -- Your chosen username
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e',  -- Temporary hash
    '<EMAIL>',  -- Your email
    'admin',
    1,
    '["all"]',  -- Full permissions
    NOW()
);
```

### **Step 3.2: Generate Your Secure Password**

**On your server, generate a secure password hash:**

```bash
# Install bcrypt tool
npm install -g bcrypt-cli

# Generate secure hash for your password
bcrypt-cli "YourVerySecurePassword123!" 12

# Copy the generated hash and update the database
```

### **Step 3.3: Update Admin Password in Database**

```sql
-- Update with your secure password hash
UPDATE admin_users 
SET password_hash = 'your_generated_hash_here' 
WHERE username = 'streamdb_admin';
```

### **🔒 Security Features:**
- ✅ **Password encrypted** with bcrypt (12 rounds)
- ✅ **No credentials in source code**
- ✅ **JWT token authentication**
- ✅ **Session management**
- ✅ **Account lockout** after failed attempts
- ✅ **Security audit logs**

---

## 📋 **PHASE 4: DEPLOY BACKEND WITH SECURITY**

### **Step 4.1: Create Server Files**

**Navigate to your server directory:**

```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
```

### **Step 4.2: Create package.json**

```bash
nano package.json
```

**Copy this content:**

```json
{
  "name": "streamdb-server",
  "version": "1.0.0",
  "description": "Secure backend for StreamDB",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "mysql2": "^3.6.5",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.5",
    "express-validator": "^7.0.1",
    "multer": "^1.4.5-lts.1",
    "dotenv": "^16.3.1",
    "compression": "^1.7.4",
    "morgan": "^1.10.0",
    "express-session": "^1.17.3",
    "uuid": "^9.0.1",
    "sharp": "^0.33.0"
  }
}
```

### **Step 4.3: Create Secure Environment File**

```bash
nano .env
```

**Add your secure configuration:**

```env
# LOCAL DATABASE CONNECTION (ULTRA SECURE)
DB_HOST=localhost
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=streamdb_database
DB_USER=dbadmin_streamdb
DB_PASSWORD=your_actual_database_password_from_fastpanel

# ULTRA SECURE KEYS (Generate these)
JWT_SECRET=your_generated_64_char_jwt_secret
SESSION_SECRET=your_generated_64_char_session_secret

# SERVER CONFIGURATION
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://streamdb.online

# SECURITY SETTINGS
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=900000

# FILE UPLOAD SETTINGS
UPLOAD_DIR=../uploads
MAX_FILE_SIZE=10485760
```

### **Step 4.4: Secure the Environment File**

```bash
# Make .env file ultra-secure
chmod 600 .env
chown www-data:www-data .env

# Verify security
ls -la .env
# Should show: -rw------- 1 <USER> <GROUP>
```

### **Step 4.5: Create All Server Files**

**You'll need to create these files with the content from your local development:**

```bash
# Main server file
nano index.js
# Copy content from your local server/index.js

# Database configuration
nano config/database.js
# Copy content from your local server/config/database.js

# Authentication middleware
nano middleware/auth.js
# Copy content from your local server/middleware/auth.js

# API routes
nano routes/auth.js
nano routes/content.js
nano routes/categories.js
nano routes/upload.js
nano routes/admin.js
# Copy content from respective local files
```

### **Step 4.6: Install Dependencies & Start Server**

```bash
# Install all dependencies
npm install

# Install PM2 for process management
npm install -g pm2

# Start the server
pm2 start index.js --name streamdb-api

# Configure auto-start
pm2 save
pm2 startup

# Check status
pm2 status
```

---

## 📋 **PHASE 5: GITHUB AUTO-SYNC SETUP**

### **Step 5.1: Initialize Git Repository**

```bash
# Navigate to your website root
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Initialize git (if not already done)
git init

# Add your GitHub remote
git remote add origin https://github.com/yourusername/your-repo.git

# Create .gitignore
cat > .gitignore << EOF
# Environment files (NEVER commit)
.env
.env.*
server/.env

# Node modules
node_modules/
npm-debug.log*

# Logs
logs/
*.log

# Uploads
uploads/temp/
uploads/images/
uploads/videos/

# OS files
.DS_Store
Thumbs.db
EOF
```

### **Step 5.2: Create Auto-Deployment Script**

```bash
nano deploy.sh
```

**Add this content:**

```bash
#!/bin/bash
set -e

# Configuration
PROJECT_DIR="/var/www/streamdb_onl_usr/data/www/streamdb.online"
BACKUP_DIR="/var/backups/streamdb"
LOG_FILE="/var/log/streamdb-deploy.log"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

# Create backup
log_message "Creating backup..."
mkdir -p $BACKUP_DIR
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
cp -r $PROJECT_DIR $BACKUP_DIR/$BACKUP_NAME

# Pull latest changes
log_message "Pulling latest changes..."
cd $PROJECT_DIR
git fetch origin
git reset --hard origin/main

# Install dependencies if needed
if [ -f "package.json" ]; then
    log_message "Installing frontend dependencies..."
    npm install
fi

if [ -f "server/package.json" ]; then
    log_message "Installing server dependencies..."
    cd server
    npm install --production
    cd ..
fi

# Build frontend if needed
if [ -f "package.json" ] && grep -q "build" package.json; then
    log_message "Building frontend..."
    npm run build
fi

# Restart server
log_message "Restarting server..."
pm2 restart streamdb-api

# Set permissions
chown -R www-data:www-data $PROJECT_DIR
chmod -R 755 $PROJECT_DIR
chmod -R 777 $PROJECT_DIR/uploads
chmod 600 $PROJECT_DIR/server/.env

log_message "Deployment completed successfully!"
```

**Make script executable:**

```bash
chmod +x deploy.sh
```

### **Step 5.3: Create GitHub Webhook Handler**

```bash
nano webhook-handler.js
```

**Add webhook handler code** (from the deployment guide I created earlier)

### **Step 5.4: Configure GitHub Webhook**

1. **Go to your GitHub repository**
2. **Settings → Webhooks → Add webhook**
3. **Payload URL**: `https://streamdb.online:9000/webhook`
4. **Content type**: `application/json`
5. **Secret**: Generate a secure secret
6. **Events**: Just the push event
7. **Active**: ✅

---

## 🔒 **SECURITY VERIFICATION CHECKLIST**

### **Database Security:**
- [ ] Local socket connection (no external ports)
- [ ] Encrypted passwords (bcrypt 12 rounds)
- [ ] Single secure admin credential
- [ ] No credentials in source code

### **API Security:**
- [ ] JWT token authentication
- [ ] Rate limiting enabled
- [ ] Input validation on all endpoints
- [ ] CORS properly configured
- [ ] Helmet security headers

### **File Security:**
- [ ] .env file permissions 600
- [ ] Upload directory secured
- [ ] No sensitive files in git
- [ ] Proper file ownership

### **Embed Link Security:**
- [ ] All video links encrypted in database
- [ ] Links served through secure API
- [ ] No embed URLs in browser source
- [ ] Server-side decryption only

---

## 🎉 **FINAL VERIFICATION**

### **Test Database Connection:**
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
node test-connection.js
```

### **Test API Endpoints:**
```bash
curl http://localhost:3001/api/health
curl http://localhost:3001/api/categories
```

### **Test Admin Login:**
1. Go to `https://streamdb.online/admin/login`
2. Use your secure credentials
3. Verify admin panel access

### **Test GitHub Sync:**
1. Make a small change to your code
2. Push to GitHub
3. Verify automatic deployment

---

## 📞 **READY TO PROCEED?**

**Your setup will have:**
- ✅ **Ultra-secure single admin credential**
- ✅ **No credentials visible in source code**
- ✅ **Local database connection (maximum security)**
- ✅ **Automatic GitHub synchronization**
- ✅ **Production-ready with no dummy data**
- ✅ **Mobile responsive maintained**

**Which phase would you like to start with?**
