# Nginx Health Check Configuration
# To be included in your main nginx.conf

# Health check server block (port 8080)
server {
    listen 8080;
    server_name localhost;
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Nginx status endpoint  
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow *************;  # Your reverse proxy IP
        deny all;
    }
    
    # Basic server info
    location /server_info {
        access_log off;
        return 200 "Server: streamdb.online Backend\nStatus: Running\nTimestamp: $time_iso8601\n";
        add_header Content-Type text/plain;
    }
}
