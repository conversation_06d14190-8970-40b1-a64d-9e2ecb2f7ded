const fs = require('fs');
const path = require('path');

console.log('🔍 StreamDB Production Diagnostic Tool');
console.log('=====================================');

// Check current working directory
console.log('\n📁 Current Working Directory:');
console.log('CWD:', process.cwd());

// Check if we're in the server directory
const isInServerDir = process.cwd().endsWith('server');
console.log('In server directory:', isInServerDir);

// Check dist directory
const distPath = path.join(__dirname, '..', 'dist');
console.log('\n📦 Dist Directory Check:');
console.log('Expected dist path:', distPath);
console.log('Dist exists:', fs.existsSync(distPath));

if (fs.existsSync(distPath)) {
  const distContents = fs.readdirSync(distPath);
  console.log('Dist contents:', distContents);
  
  // Check if index.html exists in dist
  const indexPath = path.join(distPath, 'index.html');
  console.log('Index.html in dist:', fs.existsSync(indexPath));
  
  if (fs.existsSync(indexPath)) {
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    const hasBuiltAssets = indexContent.includes('/assets/');
    console.log('Index.html has built assets:', hasBuiltAssets);
    
    if (hasBuiltAssets) {
      console.log('✅ Built index.html is correct');
    } else {
      console.log('❌ Index.html appears to be development version');
    }
  }
} else {
  console.log('❌ Dist directory not found');
}

// Check root index.html (development version)
const rootIndexPath = path.join(__dirname, '..', 'index.html');
console.log('\n📄 Root Index.html Check:');
console.log('Root index.html exists:', fs.existsSync(rootIndexPath));

if (fs.existsSync(rootIndexPath)) {
  const rootIndexContent = fs.readFileSync(rootIndexPath, 'utf8');
  const isDevelopment = rootIndexContent.includes('/src/main.tsx');
  console.log('Root index.html is development version:', isDevelopment);
}

// Check package.json for build script
const packagePath = path.join(__dirname, '..', 'package.json');
console.log('\n📋 Package.json Check:');
console.log('Package.json exists:', fs.existsSync(packagePath));

if (fs.existsSync(packagePath)) {
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  console.log('Has build script:', !!packageContent.scripts?.build);
  console.log('Build script:', packageContent.scripts?.build);
}

// Check environment
console.log('\n🌍 Environment:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT);

// Check if server files exist
console.log('\n🖥️ Server Files:');
const serverFiles = ['index.js', 'package.json', '.env'];
serverFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  console.log(`${file}:`, fs.existsSync(filePath));
});

console.log('\n🔧 Recommendations:');
if (!fs.existsSync(distPath)) {
  console.log('❗ Run "npm run build" to create dist directory');
}

if (fs.existsSync(distPath) && fs.existsSync(path.join(distPath, 'index.html'))) {
  const indexContent = fs.readFileSync(path.join(distPath, 'index.html'), 'utf8');
  if (!indexContent.includes('/assets/')) {
    console.log('❗ Rebuild application - dist/index.html is not built version');
  }
}

console.log('✅ Diagnostic complete');
