const mysql = require('mysql2/promise');

async function testDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'stream_db_admin',
      password: 'Ohno@U2foundme',
      database: 'stream_db',
      socketPath: '/var/run/mysqld/mysqld.sock'
    });

    console.log('Connected to database');

    const query = `
      SELECT
        c.*,
        cat.name as category_name,
        cat.slug as category_slug,
        s.name as section_name,
        s.slug as section_slug
      FROM content c
      LEFT JOIN categories cat ON c.category = cat.slug
      LEFT JOIN content_sections s ON c.section_id = s.id
      ORDER BY c.created_at DESC
      LIMIT 1
    `;

    console.log('Executing query:', query);
    
    const result = await connection.execute(query);
    console.log('Raw result structure:', typeof result, Array.isArray(result));
    console.log('Result length:', result.length);
    console.log('First element type:', typeof result[0], Array.isArray(result[0]));
    if (result[0]) {
      console.log('First element length:', result[0].length);
      console.log('First row:', result[0][0]);
    }

    await connection.end();
  } catch (error) {
    console.error('Database test error:', error);
  }
}

testDatabase();
