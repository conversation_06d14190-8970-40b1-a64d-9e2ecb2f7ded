<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Responsiveness Test - Banner Layout</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #e6cb8e;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .breakpoint-test {
            margin-bottom: 30px;
            border: 2px solid #e6cb8e;
            padding: 15px;
            border-radius: 8px;
        }
        .test-frame {
            border: 1px solid #666;
            margin: 10px 0;
            overflow: hidden;
            background: white;
        }
        h1, h2 {
            color: #e6cb8e;
        }
        .instructions {
            background: rgba(230, 203, 142, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Mobile Responsiveness Test - Promotional Banners</h1>
        
        <div class="instructions">
            <h2>Testing Instructions:</h2>
            <p>This page helps test the banner layout at different breakpoints. Open the main website (http://localhost:8080/) and resize your browser window to match these breakpoints:</p>
            <ul>
                <li><strong>Mobile (320px-767px):</strong> Banners should stack vertically</li>
                <li><strong>Tablet (768px-1023px):</strong> Banners should be side-by-side with medium scaling</li>
                <li><strong>Desktop (1024px+):</strong> Banners should be side-by-side with smaller scaling</li>
            </ul>
        </div>

        <div class="breakpoint-test">
            <h2>📱 Mobile Portrait (320px)</h2>
            <p>Expected: Stacked layout, scale-95, proper spacing</p>
            <div class="test-frame" style="width: 320px; height: 600px;">
                <iframe src="http://localhost:8080/" width="320" height="600" style="border: none; transform: scale(1);"></iframe>
            </div>
        </div>

        <div class="breakpoint-test">
            <h2>📱 Mobile Landscape (480px)</h2>
            <p>Expected: Stacked layout, scale-95, proper spacing</p>
            <div class="test-frame" style="width: 480px; height: 400px;">
                <iframe src="http://localhost:8080/" width="480" height="400" style="border: none; transform: scale(1);"></iframe>
            </div>
        </div>

        <div class="breakpoint-test">
            <h2>📱 Tablet Portrait (768px)</h2>
            <p>Expected: Side-by-side layout starts, scale-90, medium gap</p>
            <div class="test-frame" style="width: 768px; height: 500px;">
                <iframe src="http://localhost:8080/" width="768" height="500" style="border: none; transform: scale(1);"></iframe>
            </div>
        </div>

        <div class="breakpoint-test">
            <h2>💻 Desktop (1024px)</h2>
            <p>Expected: Side-by-side layout, scale-85, larger gap</p>
            <div class="test-frame" style="width: 1024px; height: 600px;">
                <iframe src="http://localhost:8080/" width="1024" height="600" style="border: none; transform: scale(1);"></iframe>
            </div>
        </div>

        <div class="breakpoint-test">
            <h2>🖥️ Large Desktop (1200px)</h2>
            <p>Expected: Side-by-side layout, scale-85, optimal spacing</p>
            <div class="test-frame" style="width: 1200px; height: 700px;">
                <iframe src="http://localhost:8080/" width="1200" height="700" style="border: none; transform: scale(1);"></iframe>
            </div>
        </div>

        <div class="instructions">
            <h2>✅ Checklist:</h2>
            <ul>
                <li>[ ] Banners stack vertically on mobile (320px-767px)</li>
                <li>[ ] Banners display side-by-side on tablet+ (768px+)</li>
                <li>[ ] Text remains readable at all sizes</li>
                <li>[ ] Icons and buttons are properly sized</li>
                <li>[ ] Links work correctly (Telegram and Cloudflare WARP)</li>
                <li>[ ] Gradients and animations work smoothly</li>
                <li>[ ] No horizontal scrolling on mobile</li>
                <li>[ ] Proper spacing between banners</li>
                <li>[ ] Dark theme consistency maintained</li>
            </ul>
        </div>
    </div>
</body>
</html>
