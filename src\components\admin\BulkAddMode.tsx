import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, FileText, AlertTriangle, CheckCircle, X, 
  Edit, Trash2, Download, Plus, Loader2 
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { MediaItem } from '@/types/media';
import { parseCSV, generateSampleCSV, CSVParseResult } from '@/utils/csvParser';
import CSVUpload from './CSVUpload';

interface BulkAddModeProps {
  onBulkAdd: (items: Partial<MediaItem>[]) => Promise<void>;
  onClose: () => void;
}

type ProcessingStep = 'upload' | 'preview' | 'processing' | 'complete';

interface ProcessingStatus {
  step: ProcessingStep;
  progress: number;
  currentItem?: string;
  successCount: number;
  errorCount: number;
  errors: string[];
}

export default function BulkAddMode({ onBulkAdd, onClose }: BulkAddModeProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [parseResult, setParseResult] = useState<CSVParseResult | null>(null);
  const [editableData, setEditableData] = useState<Partial<MediaItem>[]>([]);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    step: 'upload',
    progress: 0,
    successCount: 0,
    errorCount: 0,
    errors: []
  });

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setParseResult(null);
    setEditableData([]);
    setProcessingStatus(prev => ({ ...prev, step: 'upload' }));
  }, []);

  const handleFileRemove = useCallback(() => {
    setSelectedFile(null);
    setParseResult(null);
    setEditableData([]);
    setProcessingStatus(prev => ({ ...prev, step: 'upload' }));
  }, []);

  const handleDownloadTemplate = useCallback(() => {
    const csvContent = generateSampleCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'bulk-add-template.csv';
    a.click();
    URL.revokeObjectURL(url);
    
    toast({
      title: "Template downloaded",
      description: "Sample CSV template has been downloaded successfully",
    });
  }, []);

  const handleProcessFile = useCallback(async () => {
    if (!selectedFile) return;

    setProcessingStatus(prev => ({ ...prev, step: 'processing', progress: 10 }));

    try {
      const text = await selectedFile.text();
      setProcessingStatus(prev => ({ ...prev, progress: 50 }));
      
      const result = parseCSV(text);
      setParseResult(result);
      setEditableData([...result.data]);
      
      setProcessingStatus(prev => ({ 
        ...prev, 
        step: 'preview', 
        progress: 100,
        errors: result.errors 
      }));

      if (result.errors.length > 0) {
        toast({
          title: "Parsing completed with errors",
          description: `${result.validRows} valid rows found, ${result.errors.length} errors detected`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "File processed successfully",
          description: `${result.validRows} items ready for import`,
        });
      }
    } catch (error) {
      setProcessingStatus(prev => ({ 
        ...prev, 
        step: 'upload',
        errors: [`Failed to process file: ${error instanceof Error ? error.message : 'Unknown error'}`]
      }));
      
      toast({
        title: "Processing failed",
        description: "Failed to process the CSV file. Please check the format and try again.",
        variant: "destructive",
      });
    }
  }, [selectedFile]);

  const handleBulkImport = useCallback(async () => {
    if (editableData.length === 0) return;

    setProcessingStatus(prev => ({ 
      ...prev, 
      step: 'processing', 
      progress: 0,
      successCount: 0,
      errorCount: 0,
      errors: []
    }));

    try {
      await onBulkAdd(editableData);
      
      setProcessingStatus(prev => ({ 
        ...prev, 
        step: 'complete',
        progress: 100,
        successCount: editableData.length
      }));

      toast({
        title: "Bulk import completed",
        description: `Successfully imported ${editableData.length} items`,
      });
    } catch (error) {
      setProcessingStatus(prev => ({ 
        ...prev, 
        step: 'preview',
        errorCount: editableData.length,
        errors: [`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
      }));

      toast({
        title: "Import failed",
        description: "Failed to import items. Please try again.",
        variant: "destructive",
      });
    }
  }, [editableData, onBulkAdd]);

  const handleRemoveItem = useCallback((index: number) => {
    setEditableData(prev => prev.filter((_, i) => i !== index));
  }, []);

  const renderUploadStep = () => (
    <div className="space-y-6">
      <CSVUpload
        onFileSelect={handleFileSelect}
        onFileRemove={handleFileRemove}
        selectedFile={selectedFile}
        isProcessing={processingStatus.step === 'processing'}
        onDownloadTemplate={handleDownloadTemplate}
      />
      
      {selectedFile && (
        <div className="flex justify-center">
          <Button 
            onClick={handleProcessFile}
            disabled={processingStatus.step === 'processing'}
            className="flex items-center gap-2"
          >
            {processingStatus.step === 'processing' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <FileText className="h-4 w-4" />
            )}
            Process File
          </Button>
        </div>
      )}
    </div>
  );

  const renderPreviewStep = () => {
    if (!parseResult) return null;

    return (
      <div className="space-y-6">
        {/* Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Valid Items</p>
                  <p className="text-2xl font-bold text-green-600">{editableData.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">Warnings</p>
                  <p className="text-2xl font-bold text-yellow-600">{parseResult.warnings.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <X className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm font-medium">Errors</p>
                  <p className="text-2xl font-bold text-red-600">{parseResult.errors.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Errors and Warnings */}
        {(parseResult.errors.length > 0 || parseResult.warnings.length > 0) && (
          <Tabs defaultValue="errors" className="w-full">
            <TabsList>
              <TabsTrigger value="errors">Errors ({parseResult.errors.length})</TabsTrigger>
              <TabsTrigger value="warnings">Warnings ({parseResult.warnings.length})</TabsTrigger>
            </TabsList>
            
            <TabsContent value="errors">
              {parseResult.errors.length > 0 ? (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      {parseResult.errors.slice(0, 5).map((error, index) => (
                        <div key={index} className="text-sm">{error}</div>
                      ))}
                      {parseResult.errors.length > 5 && (
                        <div className="text-sm font-medium">
                          ... and {parseResult.errors.length - 5} more errors
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              ) : (
                <p className="text-sm text-muted-foreground">No errors found</p>
              )}
            </TabsContent>
            
            <TabsContent value="warnings">
              {parseResult.warnings.length > 0 ? (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      {parseResult.warnings.slice(0, 5).map((warning, index) => (
                        <div key={index} className="text-sm">{warning}</div>
                      ))}
                      {parseResult.warnings.length > 5 && (
                        <div className="text-sm font-medium">
                          ... and {parseResult.warnings.length - 5} more warnings
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              ) : (
                <p className="text-sm text-muted-foreground">No warnings found</p>
              )}
            </TabsContent>
          </Tabs>
        )}

        {/* Preview Table */}
        {editableData.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Preview Data ({editableData.length} items)</span>
                <Button 
                  onClick={handleBulkImport}
                  disabled={editableData.length === 0}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Import All Items
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto max-h-96">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Year</TableHead>
                      <TableHead>Genres</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {editableData.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{item.title}</TableCell>
                        <TableCell>
                          <Badge variant={item.type === 'movie' ? 'default' : 'secondary'}>
                            {item.type === 'movie' ? 'Movie' : 'Web Series'}
                          </Badge>
                        </TableCell>
                        <TableCell>{item.year}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {item.genres?.slice(0, 2).map(genre => (
                              <Badge key={genre} variant="outline" className="text-xs">
                                {genre}
                              </Badge>
                            ))}
                            {item.genres && item.genres.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{item.genres.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={item.isPublished ? 'default' : 'secondary'}>
                            {item.isPublished ? 'Published' : 'Draft'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveItem(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderProcessingStep = () => (
    <div className="space-y-6 text-center">
      <div className="flex justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
      <div>
        <h3 className="text-lg font-medium">Processing Import...</h3>
        <p className="text-sm text-muted-foreground">
          {processingStatus.currentItem || 'Importing items to your content library'}
        </p>
      </div>
      <Progress value={processingStatus.progress} className="w-full max-w-md mx-auto" />
    </div>
  );

  const renderCompleteStep = () => (
    <div className="space-y-6 text-center">
      <div className="flex justify-center">
        <CheckCircle className="h-12 w-12 text-green-500" />
      </div>
      <div>
        <h3 className="text-lg font-medium">Import Completed!</h3>
        <p className="text-sm text-muted-foreground">
          Successfully imported {processingStatus.successCount} items
        </p>
      </div>
      <div className="flex justify-center gap-4">
        <Button onClick={onClose}>
          Close
        </Button>
        <Button 
          variant="outline" 
          onClick={() => {
            setSelectedFile(null);
            setParseResult(null);
            setEditableData([]);
            setProcessingStatus({
              step: 'upload',
              progress: 0,
              successCount: 0,
              errorCount: 0,
              errors: []
            });
          }}
        >
          Import More
        </Button>
      </div>
    </div>
  );

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Bulk Add Mode
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {processingStatus.step === 'upload' && renderUploadStep()}
        {processingStatus.step === 'preview' && renderPreviewStep()}
        {processingStatus.step === 'processing' && renderProcessingStep()}
        {processingStatus.step === 'complete' && renderCompleteStep()}
      </CardContent>
    </Card>
  );
}
