# Dynamic Content Sections

This feature allows administrators to create and manage custom content sections like "Movies", "Web Series", "Featured", etc. through the admin panel.

## Features

- **Dynamic Section Creation**: Create custom sections with names, colors, icons, and filtering rules
- **Homepage Integration**: Sections automatically appear on the homepage based on configuration
- **Content Assignment**: Assign content to specific sections during creation
- **Navigation Integration**: Sections can optionally appear in the main navigation
- **Flexible Filtering**: Each section can have custom filtering rules and content types
- **Drag & Drop Ordering**: Reorder sections using the admin interface

## Database Schema

### Tables Created

1. **content_sections**: Main sections configuration
2. **section_categories**: Many-to-many relationship between sections and categories
3. **section_content_types**: Custom content type definitions per section
4. **content.section_id**: Foreign key linking content to sections

### Migration

Run the migration to set up the database:

```bash
cd database
node run_migration.js
```

Or for a dry run:
```bash
node run_migration.js --dry-run
```

## Admin Panel Usage

### Accessing Section Management

1. Log into the admin panel
2. Navigate to the "Manage Sections" tab
3. View existing sections or create new ones

### Creating a New Section

1. Click "Create Section" button
2. Fill in the required fields:
   - **Section Name**: Display name (e.g., "Movies", "Web Series")
   - **URL Slug**: URL-friendly identifier (auto-generated from name)
   - **Description**: Brief description of the section
   - **Icon**: Choose from available icons
   - **Color**: Select theme color for the section
   - **Content Types**: Select which content types belong to this section
   - **Categories**: Associate relevant categories
   - **Display Settings**: Configure visibility and ordering

### Section Configuration Options

- **Active**: Whether the section is enabled
- **Show in Navigation**: Include in main site navigation
- **Show on Homepage**: Display on the homepage
- **Max Items on Homepage**: Number of items to show on homepage
- **Display Order**: Order in which sections appear

### Managing Existing Sections

- **Edit**: Click the edit button to modify section settings
- **Delete**: Remove sections (only if they contain no content)
- **Reorder**: Use up/down arrows to change display order

## Frontend Integration

### Homepage Display

Sections automatically appear on the homepage based on:
- `show_on_homepage = true`
- `is_active = true`
- `display_order` for sorting

### Dynamic Loading

The homepage uses the `useDynamicHomepage()` hook to:
1. Load active sections from the database
2. Fetch content for each section
3. Display sections with their configured styling
4. Handle loading states and errors

### Fallback Content

If dynamic loading fails, the system falls back to:
- Static "Movies" and "Web Series" sections
- Content filtered by type from the existing data

## API Endpoints

### Sections Management

- `GET /api/sections` - List all sections
- `GET /api/sections/:id` - Get single section
- `POST /api/sections` - Create new section
- `PUT /api/sections/:id` - Update section
- `DELETE /api/sections/:id` - Delete section
- `PUT /api/sections/reorder` - Reorder sections

### Section Content

- `GET /api/sections/:id/content` - Get content for a section

## Content Assignment

### During Content Creation

When adding new content through the admin panel:
1. Select the appropriate section from the dropdown
2. The content will be automatically assigned to that section
3. Section assignment affects where content appears on the homepage

### Bulk Assignment

Existing content is automatically assigned to sections based on type:
- Movies → "Movies" section
- Series → "Web Series" section  
- Requested → "Requested" section

## Customization

### Adding New Icons

Edit `src/components/admin/SectionsManager.tsx` and add to `ICON_OPTIONS`:

```typescript
const ICON_OPTIONS = [
  { value: 'NewIcon', label: 'New Icon', icon: NewIcon },
  // ... existing icons
];
```

### Adding New Colors

Edit the `COLOR_OPTIONS` array in the same file:

```typescript
const COLOR_OPTIONS = [
  '#your-color-hex',
  // ... existing colors
];
```

### Custom Filter Rules

Sections support custom filtering through the `filter_rules` JSON field:

```json
{
  "type": "movie",
  "is_featured": true,
  "year_min": 2020,
  "order_by": "created_at",
  "order": "desc"
}
```

## Troubleshooting

### Sections Not Appearing

1. Check section is active: `is_active = true`
2. Check homepage visibility: `show_on_homepage = true`
3. Verify content exists in the section
4. Check browser console for API errors

### Content Not Assigned to Sections

1. Verify content has `section_id` set
2. Run migration to auto-assign existing content
3. Check section configuration allows the content type

### Database Issues

1. Ensure migration was run successfully
2. Check foreign key constraints are in place
3. Verify user permissions for new tables

## Performance Considerations

- Sections are cached on the frontend
- Homepage loads sections in parallel
- Content is paginated per section
- Database indexes optimize section queries

## Security

- Section management requires moderator privileges
- All API endpoints are protected with authentication
- Input validation prevents malicious data
- SQL injection protection through parameterized queries
