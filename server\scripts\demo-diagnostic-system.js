#!/usr/bin/env node

/**
 * StreamDB Database Diagnostic System Demo
 * Comprehensive demonstration of all diagnostic and monitoring features
 * 
 * This script showcases:
 * - Database connectivity testing
 * - Schema validation for Admin Panel compatibility
 * - Error classification and solution recommendations
 * - Performance monitoring and analysis
 * - Real-time monitoring service
 * - API endpoint testing
 * 
 * Usage: node server/scripts/demo-diagnostic-system.js
 */

const DatabaseDiagnostic = require('../services/database-diagnostic');
const DatabaseMonitor = require('../services/database-monitor');
const DatabaseErrorClassifier = require('../services/database-error-classifier');
const db = require('../config/database');

class DiagnosticSystemDemo {
  constructor() {
    this.diagnostic = new DatabaseDiagnostic();
    this.classifier = new DatabaseErrorClassifier();
    this.monitor = null;
  }

  async runDemo() {
    console.log('\n🎯 StreamDB Database Diagnostic System Demo');
    console.log('=' .repeat(60));
    console.log('🏗️ Infrastructure: Two-tier offshore VPS with MySQL socket');
    console.log('🔒 Security: Local socket connection for maximum security');
    console.log('📊 Features: Health monitoring, schema validation, error classification');
    console.log('');

    try {
      await this.demonstrateBasicConnectivity();
      await this.demonstrateSchemaValidation();
      await this.demonstrateErrorClassification();
      await this.demonstratePerformanceMonitoring();
      await this.demonstrateEnhancedDatabaseConfig();
      await this.demonstrateMonitoringService();
      await this.showSystemSummary();
      
      console.log('\n🎉 Demo completed successfully!');
      console.log('📚 See DATABASE_DIAGNOSTIC_SYSTEM.md for complete documentation');
      
    } catch (error) {
      console.error('\n❌ Demo failed:', error.message);
      process.exit(1);
    }
  }

  async demonstrateBasicConnectivity() {
    console.log('\n🔌 1. DATABASE CONNECTIVITY TESTING');
    console.log('-' .repeat(40));
    
    console.log('Testing basic database connection...');
    const connected = await this.diagnostic.testDatabaseConnection();
    
    if (connected) {
      console.log('✅ Database connection successful');
      
      // Show connection details from diagnostic results
      const connectionTest = this.diagnostic.diagnosticResults.tests.find(
        test => test.test === 'Database Connection'
      );
      
      if (connectionTest && connectionTest.details) {
        console.log(`   MySQL Version: ${connectionTest.details.mysql_version}`);
        console.log(`   Connection Method: ${connectionTest.details.connection_method}`);
        console.log(`   Server Time: ${connectionTest.details.server_time}`);
      }
    } else {
      console.log('❌ Database connection failed');
      console.log('   Check the diagnostic recommendations below');
    }
  }

  async demonstrateSchemaValidation() {
    console.log('\n🏗️ 2. SCHEMA VALIDATION FOR ADMIN PANEL');
    console.log('-' .repeat(40));
    
    console.log('Validating database schema for Admin Panel compatibility...');
    const schemaValid = await this.diagnostic.validateDatabaseSchema();
    
    if (schemaValid) {
      console.log('✅ All required tables and fields exist for Admin Panel');
      
      // Show table validation details
      const schemaTests = this.diagnostic.diagnosticResults.tests.filter(
        test => test.test === 'Schema Validation'
      );
      
      console.log(`   Validated ${Object.keys(this.diagnostic.requiredSchema).length} core tables:`);
      Object.keys(this.diagnostic.requiredSchema).forEach(table => {
        console.log(`   - ${table}: Required for ${this.diagnostic.requiredSchema[table].description}`);
      });
    } else {
      console.log('⚠️  Schema validation found issues');
      console.log('   Check recommendations for required fixes');
    }
  }

  async demonstrateErrorClassification() {
    console.log('\n🚨 3. ERROR CLASSIFICATION AND SOLUTIONS');
    console.log('-' .repeat(40));
    
    console.log('Demonstrating error classification system...');
    
    // Test various error types
    const testErrors = [
      { code: 'ECONNREFUSED', description: 'MySQL server connection refused' },
      { code: 'ER_ACCESS_DENIED_ERROR', description: 'Invalid database credentials' },
      { code: 'ENOENT', description: 'MySQL socket file not found' },
      { code: 'ER_TOO_MANY_CONNECTIONS', description: 'Connection limit exceeded' }
    ];

    testErrors.forEach(({ code, description }) => {
      const classification = this.classifier.classifyError({ code });
      console.log(`\n   Error: ${code} (${description})`);
      console.log(`   Category: ${classification.category}`);
      console.log(`   Severity: ${classification.severity}`);
      console.log(`   Solution: ${classification.solutions[0]}`);
    });

    // Show error categories
    console.log('\n   Available Error Categories:');
    const categories = this.classifier.getErrorCategories();
    categories.slice(0, 5).forEach(cat => {
      console.log(`   - ${cat.key}: ${cat.description}`);
    });
  }

  async demonstratePerformanceMonitoring() {
    console.log('\n⚡ 4. PERFORMANCE MONITORING');
    console.log('-' .repeat(40));
    
    console.log('Checking database performance metrics...');
    const performanceHealthy = await this.diagnostic.checkDatabasePerformance();
    
    console.log('Testing connection pool health...');
    const poolHealthy = await this.diagnostic.monitorConnectionPool();
    
    if (performanceHealthy && poolHealthy) {
      console.log('✅ Database performance is healthy');
    } else {
      console.log('⚠️  Performance issues detected');
    }
    
    // Demonstrate performance analysis
    console.log('\nPerformance Analysis Example:');
    const mockMetrics = {
      averageQueryTime: 1200,  // Above threshold
      connectionUsage: 85,     // Above threshold
      averageLockWaitTime: 2   // Below threshold
    };
    
    const analysis = this.classifier.analyzePerformanceMetrics(mockMetrics);
    console.log(`   Issues Found: ${analysis.issues.length}`);
    analysis.issues.forEach(issue => {
      console.log(`   - ${issue.type}: ${issue.description}`);
    });
  }

  async demonstrateEnhancedDatabaseConfig() {
    console.log('\n🔧 5. ENHANCED DATABASE CONFIGURATION');
    console.log('-' .repeat(40));
    
    console.log('Testing enhanced database health check...');
    const healthStatus = await db.healthCheck();
    
    if (healthStatus.healthy) {
      console.log('✅ Enhanced health check passed');
      console.log(`   Response Time: ${healthStatus.responseTime}ms`);
      console.log(`   Database: ${healthStatus.database}`);
      console.log(`   Connection: ${healthStatus.connectionMethod}`);
      console.log(`   Core Tables: ${healthStatus.coreTablesFound}/3 found`);
    } else {
      console.log('❌ Enhanced health check failed');
      if (healthStatus.classification) {
        console.log(`   Error Category: ${healthStatus.classification.category}`);
        console.log(`   Severity: ${healthStatus.classification.severity}`);
        console.log(`   Solution: ${healthStatus.classification.primarySolution}`);
      }
    }
  }

  async demonstrateMonitoringService() {
    console.log('\n📊 6. REAL-TIME MONITORING SERVICE');
    console.log('-' .repeat(40));
    
    console.log('Starting monitoring service for demonstration...');
    
    this.monitor = new DatabaseMonitor({
      healthCheckInterval: 3000,    // 3 seconds for demo
      performanceCheckInterval: 6000, // 6 seconds for demo
      enableLogging: false,         // Disable file logging for demo
      alertThreshold: 2             // Lower threshold for demo
    });

    // Set up event handlers
    let healthChecks = 0;
    const maxChecks = 3;

    this.monitor.on('healthCheck', (data) => {
      healthChecks++;
      console.log(`   Health Check ${healthChecks}: ${data.isHealthy ? 'PASS' : 'FAIL'} (${data.responseTime}ms)`);
      
      if (healthChecks >= maxChecks) {
        this.stopMonitoringDemo();
      }
    });

    this.monitor.on('alert', (alert) => {
      console.log(`   🚨 Alert: ${alert.type} - ${alert.message}`);
    });

    this.monitor.on('recovery', (recovery) => {
      console.log(`   ✅ Recovery: After ${recovery.previousFailures} failures`);
    });

    await this.monitor.start();
    console.log('   Monitoring service started (will run 3 health checks)...');
    
    // Wait for demo to complete
    return new Promise((resolve) => {
      this.monitorDemoResolve = resolve;
      
      // Timeout after 15 seconds
      setTimeout(() => {
        if (this.monitor && this.monitor.isRunning) {
          this.stopMonitoringDemo();
        }
      }, 15000);
    });
  }

  async stopMonitoringDemo() {
    if (this.monitor && this.monitor.isRunning) {
      const finalStatus = this.monitor.getStatus();
      await this.monitor.stop();
      
      console.log('   Monitoring service stopped');
      console.log(`   Total Checks: ${finalStatus.metrics.totalChecks}`);
      console.log(`   Success Rate: ${finalStatus.metrics.successRate}%`);
      console.log(`   Average Response Time: ${finalStatus.metrics.averageResponseTime}ms`);
      
      if (this.monitorDemoResolve) {
        this.monitorDemoResolve();
      }
    }
  }

  async showSystemSummary() {
    console.log('\n📋 7. COMPREHENSIVE DIAGNOSTIC SUMMARY');
    console.log('-' .repeat(40));
    
    console.log('Running full diagnostic suite...');
    const results = await this.diagnostic.runComprehensiveDiagnostic();
    
    console.log('\nDiagnostic Results Summary:');
    console.log(`✅ Tests Passed: ${results.summary.passed}`);
    console.log(`❌ Tests Failed: ${results.summary.failed}`);
    console.log(`⚠️  Warnings: ${results.summary.warnings}`);
    console.log(`🚨 Critical Issues: ${results.summary.critical}`);
    
    if (results.recommendations.length > 0) {
      console.log('\nTop Recommendations:');
      results.recommendations.slice(0, 3).forEach((rec, index) => {
        console.log(`${index + 1}. [${rec.priority}] ${rec.issue}`);
        console.log(`   Solution: ${rec.solution}`);
      });
    } else {
      console.log('\n🎉 No issues found - database is healthy!');
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n⚠️  Demo interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n⚠️  Demo terminated');
  process.exit(0);
});

// Run the demo
if (require.main === module) {
  const demo = new DiagnosticSystemDemo();
  demo.runDemo().catch(error => {
    console.error('\n💥 Demo crashed:', error);
    process.exit(1);
  });
}

module.exports = DiagnosticSystemDemo;
