
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { ListChecks, Send, Timer } from "lucide-react";

export default function DMCA() {
  const navigate = useNavigate();
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />
      <main className="flex-1 flex flex-col items-center px-2 py-10">
        <h1
          className="text-3xl sm:text-4xl md:text-5xl font-bold mb-7 text-center uppercase"
          style={{ color: '#e6cb8e', textShadow: '0 0 10px #e6cb8e, 0 0 20px #e6cb8e60' }}
        >
          DMCA
        </h1>
        <div className="max-w-3xl w-full bg-card rounded-2xl shadow-xl p-6 sm:p-8 mb-8 border border-border">
          <p className="text-foreground text-base sm:text-lg mb-6 leading-relaxed">
            StreamDB is in compliance with 17 U.S.C. § 512 and the Digital Millennium Copyright Act (“DMCA”). It is our policy to respond to any infringement notices and take appropriate actions under the Digital Millennium Copyright Act (“DMCA”) and other applicable intellectual property laws.
          </p>
          <p className="text-foreground text-base sm:text-lg mb-8 leading-relaxed">
            If your copyrighted material has been posted on StreamDB or if links to your copyrighted material are returned through our search engine and you want this material removed, you must provide a written communication that details the information listed in the following section. Please be aware that you will be liable for damages (including costs and attorneys’ fees) if you misrepresent information listed on our site that is infringing on your copyrights. We suggest that you first contact an attorney for legal assistance on this matter.
          </p>

          <div className="space-y-8">
            <div className="p-6 rounded-lg bg-secondary/30 border border-border">
              <div className="flex items-center gap-3 mb-4">
                <ListChecks className="w-6 h-6" style={{ color: "#e6cb8e" }} />
                <h2 className="text-xl font-bold" style={{ color: "#e6cb8e" }}>Required Elements for Copyright Infringement Claims</h2>
              </div>
              <p className="text-muted-foreground mb-4">The following elements must be included in your copyright infringement claim:</p>
              <ul className="list-disc list-inside space-y-2 text-foreground">
                <li>Provide evidence of the authorized person to act on behalf of the owner of an exclusive right that is allegedly infringed.</li>
                <li>Provide sufficient contact information so that we may contact you. You must also include a valid email address.</li>
                <li>You must identify in sufficient detail the copyrighted work claimed to have been infringed and including at least one search term under which the material appears in StreamDB search results.</li>
                <li>A statement that the complaining party has a good faith belief that use of the material in the manner complained of is not authorized by the copyright owner, its agent, or the law.</li>
                <li>A statement that the information in the notification is accurate, and under penalty of perjury, that the complaining party is authorized to act on behalf of the owner of an exclusive right that is allegedly infringed.</li>
                <li>Must be signed by the authorized person to act on behalf of the owner of an exclusive right that is allegedly infringed.</li>
              </ul>
            </div>
            
            <div className="p-6 rounded-lg bg-secondary/30 border border-border">
              <div className="flex items-center gap-3 mb-4">
                <Send className="w-6 h-6" style={{ color: "#e6cb8e" }} />
                <h2 className="text-xl font-bold" style={{ color: "#e6cb8e" }}>Submit Your DMCA Notice</h2>
              </div>
              <p className="text-muted-foreground mb-4">Send the written infringement notice to the following email:</p>
              <div className="bg-background p-3 rounded-md text-center">
                <span className="font-semibold text-lg" style={{ color: "#e6cb8e" }}><EMAIL></span>
              </div>
            </div>

            <div className="p-6 rounded-lg bg-secondary/30 border border-border">
              <div className="flex items-center gap-3 mb-4">
                <Timer className="w-6 h-6" style={{ color: "#e6cb8e" }} />
                <h2 className="text-xl font-bold" style={{ color: "#e6cb8e" }}>Processing Information</h2>
              </div>
              <p className="text-foreground mb-3"><span className="font-semibold" style={{ color: "#e6cb8e" }}>Processing Time:</span> Please allow 1-3 business days to remove content from our index.</p>
              <p className="text-foreground mb-3"><span className="font-semibold" style={{ color: "#e6cb8e" }}>Response Policy:</span> You will not receive any email response of acknowledgement of content removed.</p>
              <p className="text-foreground"><span className="font-semibold" style={{ color: "#e6cb8e" }}>Important Notice:</span> Please note that sending your complaint to other parties will not expedite your request and may result in a delayed response due the complaint not being properly filed.</p>
            </div>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={() => navigate("/")}
          className="mb-2 text-lg border-primary text-primary hover:bg-primary hover:text-primary-foreground"
        >
          Back to Home
        </Button>
      </main>
      <Footer />
    </div>
  );
}
