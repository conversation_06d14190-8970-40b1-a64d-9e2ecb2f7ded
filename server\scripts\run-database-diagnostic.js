#!/usr/bin/env node

/**
 * StreamDB Database Diagnostic Runner
 * Command-line interface for running comprehensive database diagnostics
 * 
 * Usage:
 *   node server/scripts/run-database-diagnostic.js [options]
 * 
 * Options:
 *   --quick          Run quick diagnostic (connection + schema only)
 *   --full           Run full comprehensive diagnostic (default)
 *   --schema-only    Run only schema validation
 *   --performance    Run only performance checks
 *   --json           Output results in JSON format
 *   --save-report    Save detailed report to file
 *   --help           Show this help message
 * 
 * Examples:
 *   node server/scripts/run-database-diagnostic.js --quick
 *   node server/scripts/run-database-diagnostic.js --schema-only --json
 *   node server/scripts/run-database-diagnostic.js --full --save-report
 */

const DatabaseDiagnostic = require('../services/database-diagnostic');
const path = require('path');
const fs = require('fs').promises;

class DiagnosticRunner {
  constructor() {
    this.args = process.argv.slice(2);
    this.options = this.parseArguments();
    this.diagnostic = new DatabaseDiagnostic();
  }

  parseArguments() {
    const options = {
      mode: 'full',
      outputFormat: 'console',
      saveReport: false,
      showHelp: false
    };

    for (const arg of this.args) {
      switch (arg) {
        case '--quick':
          options.mode = 'quick';
          break;
        case '--full':
          options.mode = 'full';
          break;
        case '--schema-only':
          options.mode = 'schema';
          break;
        case '--performance':
          options.mode = 'performance';
          break;
        case '--json':
          options.outputFormat = 'json';
          break;
        case '--save-report':
          options.saveReport = true;
          break;
        case '--help':
        case '-h':
          options.showHelp = true;
          break;
        default:
          if (arg.startsWith('--')) {
            console.warn(`Unknown option: ${arg}`);
          }
      }
    }

    return options;
  }

  showHelp() {
    console.log(`
StreamDB Database Diagnostic Tool
=================================

Usage: node server/scripts/run-database-diagnostic.js [options]

Options:
  --quick          Run quick diagnostic (connection + schema only)
  --full           Run full comprehensive diagnostic (default)
  --schema-only    Run only schema validation
  --performance    Run only performance checks
  --json           Output results in JSON format
  --save-report    Save detailed report to file
  --help, -h       Show this help message

Examples:
  node server/scripts/run-database-diagnostic.js --quick
  node server/scripts/run-database-diagnostic.js --schema-only --json
  node server/scripts/run-database-diagnostic.js --full --save-report

Description:
  This tool performs comprehensive diagnostics on your StreamDB MySQL database,
  including connection health, schema validation, performance monitoring, and
  error detection. It provides actionable recommendations for any issues found.

Safety:
  All diagnostic operations are read-only and will not modify your database.
  The tool is safe to run on production systems.
`);
  }

  async runDiagnostic() {
    if (this.options.showHelp) {
      this.showHelp();
      return;
    }

    try {
      let results;

      switch (this.options.mode) {
        case 'quick':
          results = await this.runQuickDiagnostic();
          break;
        case 'schema':
          results = await this.runSchemaOnlyDiagnostic();
          break;
        case 'performance':
          results = await this.runPerformanceOnlyDiagnostic();
          break;
        case 'full':
        default:
          results = await this.diagnostic.runComprehensiveDiagnostic();
          break;
      }

      // Output results
      if (this.options.outputFormat === 'json') {
        console.log(JSON.stringify(results, null, 2));
      }

      // Save report if requested
      if (this.options.saveReport) {
        await this.saveReport(results);
      }

      // Exit with appropriate code
      const hasErrors = results.summary.failed > 0 || results.summary.critical > 0;
      process.exit(hasErrors ? 1 : 0);

    } catch (error) {
      console.error('❌ Diagnostic runner failed:', error.message);
      if (this.options.outputFormat === 'json') {
        console.log(JSON.stringify({
          error: true,
          message: error.message,
          timestamp: new Date().toISOString()
        }, null, 2));
      }
      process.exit(1);
    }
  }

  async runQuickDiagnostic() {
    console.log('\n🚀 Running Quick Database Diagnostic...\n');
    
    await this.diagnostic.testDatabaseConnection();
    await this.diagnostic.validateDatabaseSchema();
    
    await this.diagnostic.generateDiagnosticReport();
    return this.diagnostic.diagnosticResults;
  }

  async runSchemaOnlyDiagnostic() {
    console.log('\n🏗️ Running Schema Validation Only...\n');
    
    // Test connection first
    const connected = await this.diagnostic.testDatabaseConnection();
    if (!connected) {
      console.log('❌ Cannot validate schema without database connection');
      return this.diagnostic.diagnosticResults;
    }
    
    await this.diagnostic.validateDatabaseSchema();
    await this.diagnostic.generateDiagnosticReport();
    return this.diagnostic.diagnosticResults;
  }

  async runPerformanceOnlyDiagnostic() {
    console.log('\n⚡ Running Performance Diagnostic Only...\n');
    
    // Test connection first
    const connected = await this.diagnostic.testDatabaseConnection();
    if (!connected) {
      console.log('❌ Cannot check performance without database connection');
      return this.diagnostic.diagnosticResults;
    }
    
    await this.diagnostic.monitorConnectionPool();
    await this.diagnostic.checkDatabasePerformance();
    await this.diagnostic.generateDiagnosticReport();
    return this.diagnostic.diagnosticResults;
  }

  async saveReport(results) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const reportPath = path.join(__dirname, '../logs', `database-diagnostic-${timestamp}.json`);
      
      // Ensure logs directory exists
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      
      // Save detailed report
      await fs.writeFile(reportPath, JSON.stringify(results, null, 2));
      console.log(`\n📄 Detailed report saved to: ${reportPath}`);
      
      // Also save a summary report
      const summaryPath = path.join(__dirname, '../logs', `database-diagnostic-summary-${timestamp}.txt`);
      const summaryContent = this.generateTextSummary(results);
      await fs.writeFile(summaryPath, summaryContent);
      console.log(`📄 Summary report saved to: ${summaryPath}`);
      
    } catch (error) {
      console.error(`⚠️  Could not save report: ${error.message}`);
    }
  }

  generateTextSummary(results) {
    const lines = [];
    lines.push('StreamDB Database Diagnostic Summary');
    lines.push('=' .repeat(40));
    lines.push(`Timestamp: ${results.timestamp}`);
    lines.push(`Environment: ${results.environment}`);
    lines.push('');
    
    lines.push('Test Results:');
    lines.push(`  ✅ Passed: ${results.summary.passed}`);
    lines.push(`  ❌ Failed: ${results.summary.failed}`);
    lines.push(`  ⚠️  Warnings: ${results.summary.warnings}`);
    lines.push(`  🚨 Critical: ${results.summary.critical}`);
    lines.push('');
    
    if (results.recommendations.length > 0) {
      lines.push('Recommendations:');
      results.recommendations.forEach((rec, index) => {
        lines.push(`  ${index + 1}. [${rec.priority}] ${rec.issue}`);
        lines.push(`     Solution: ${rec.solution}`);
        lines.push('');
      });
    }
    
    lines.push('Detailed Test Results:');
    results.tests.forEach(test => {
      const icon = test.level === 'PASS' ? '✅' : 
                   test.level === 'FAIL' ? '❌' : 
                   test.level === 'WARN' ? '⚠️' : 
                   test.level === 'CRITICAL' ? '🚨' : 'ℹ️';
      lines.push(`  ${icon} ${test.test}: ${test.message}`);
    });
    
    return lines.join('\n');
  }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  console.log('\n⚠️  Diagnostic interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Diagnostic terminated');
  process.exit(1);
});

// Run the diagnostic
const runner = new DiagnosticRunner();
runner.runDiagnostic().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
