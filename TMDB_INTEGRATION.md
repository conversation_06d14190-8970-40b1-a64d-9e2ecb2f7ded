# TMDB API Integration Documentation

## Overview

This document outlines the comprehensive TMDB (The Movie Database) API integration implemented in the streaming database website. The integration provides automatic content data fetching, search functionality, and comprehensive metadata retrieval.

## Features Implemented

### ✅ 1. Environment Configuration
- **API Key Setup**: Secure environment variable configuration
- **Base URLs**: Configurable TMDB API and image base URLs
- **TypeScript Support**: Proper typing for environment variables

### ✅ 2. Core TMDB Service (`src/services/tmdbService.ts`)
- **Rate Limiting**: Built-in rate limiting (4 requests/second)
- **Error Handling**: Comprehensive error handling with custom TMDBError class
- **Image URL Building**: Automatic image URL generation with multiple sizes
- **Content Fetching**: Support for both movies and TV shows
- **Auto-Detection**: Automatic content type detection
- **Search Functionality**: Full-text search across TMDB database

### ✅ 3. Data Transformation
- **Comprehensive Mapping**: Maps TMDB data to internal MediaItem format
- **Cast & Crew**: Extracts cast, crew, directors, writers, and producers
- **Metadata**: Includes ratings, runtime, genres, languages, studios
- **Images**: Poster, thumbnail, and cover image URLs
- **Trailers**: YouTube trailer extraction

### ✅ 4. Admin Panel Integration
- **Real API Calls**: Replaced all placeholder functions with real TMDB API calls
- **TMDB ID Validation**: Client-side validation for TMDB IDs
- **Auto-Population**: Automatic form field population from TMDB data
- **Visual Feedback**: Loading states, success/error messages
- **Data Preview**: Visual preview of fetched TMDB data

### ✅ 5. Search Dialog (`src/components/admin/TMDBSearchDialog.tsx`)
- **Interactive Search**: Real-time search with visual results
- **Content Type Filtering**: Filter by movies, TV shows, or all
- **Rich Results**: Display with posters, ratings, release dates
- **One-Click Selection**: Direct selection and auto-population

### ✅ 6. Testing Suite (`src/utils/tmdbTestUtils.ts`)
- **Comprehensive Tests**: Full test coverage for all TMDB functionality
- **Error Testing**: Validation of error handling scenarios
- **Performance Monitoring**: Request timing and success rate tracking
- **Real Data Testing**: Tests with actual TMDB content

## API Configuration

### Environment Variables
```env
VITE_TMDB_API_KEY=d42e51fef0cd194377f0df77218b08cb
VITE_TMDB_BASE_URL=https://api.themoviedb.org/3
VITE_TMDB_IMAGE_BASE_URL=https://image.tmdb.org/t/p
```

### Rate Limiting
- **Limit**: 4 requests per second (within TMDB's 40 requests per 10 seconds limit)
- **Implementation**: Automatic delay between requests
- **Monitoring**: Built-in request timing

## Usage Examples

### 1. Fetch Movie Data
```typescript
import { getComprehensiveContentData } from '@/services/tmdbService';

const result = await getComprehensiveContentData('27205', 'movie'); // Inception
if (result.success) {
  console.log(result.data); // Transformed data ready for form
}
```

### 2. Search Content
```typescript
import { searchContent } from '@/services/tmdbService';

const results = await searchContent('Breaking Bad', 'tv');
console.log(results.results); // Array of search results
```

### 3. Build Image URLs
```typescript
import { buildImageUrl } from '@/services/tmdbService';

const posterUrl = buildImageUrl('/poster.jpg', 'poster', 'large');
const backdropUrl = buildImageUrl('/backdrop.jpg', 'backdrop', 'original');
```

## Data Mapping

### TMDB → MediaItem Transformation
```typescript
{
  title: string;           // movie.title || tv.name
  description: string;     // overview
  year: string;           // release_date || first_air_date (year)
  genres: string[];       // genres[].name
  posterUrl: string;      // poster_path (large size)
  thumbnailUrl: string;   // poster_path (medium size)
  coverImage: string;     // backdrop_path (original size)
  imdbRating: string;     // vote_average
  runtime: string;        // runtime || episode_run_time[0]
  studio: string;         // production_companies[0].name
  languages: string[];    // spoken_languages[].english_name
  trailer: string;        // YouTube trailer URL
  cast: string[];         // credits.cast[].name (top 10)
  crew: string[];         // credits.crew[].name + job (top 5)
  director: string;       // crew member with job "Director"
  writers: string[];      // crew members with writing jobs
  producers: string[];    // crew members with producer jobs
}
```

## Error Handling

### TMDBError Class
```typescript
class TMDBError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  )
}
```

### Common Error Scenarios
- **404**: Content not found
- **401**: Invalid API key
- **429**: Rate limit exceeded
- **Network**: Connection issues
- **Validation**: Invalid TMDB ID format

## Testing

### Run TMDB Tests
```typescript
// In browser console or admin panel
import { runComprehensiveTMDBTests } from '@/utils/tmdbTestUtils';

const results = await runComprehensiveTMDBTests();
console.log(results.summary);
```

### Test Categories
1. **TMDB ID Validation**: Format validation tests
2. **Movie Data Fetching**: Real movie data retrieval
3. **TV Show Data Fetching**: Real TV show data retrieval
4. **Error Handling**: 404, validation, and network error tests
5. **Image URL Building**: URL generation tests
6. **Search Functionality**: Search query tests

## Admin Panel Features

### TMDB ID Section
- **Manual Entry**: Direct TMDB ID input
- **Search Button**: Opens TMDB search dialog
- **Fetch Button**: Retrieves data for entered ID
- **Validation**: Real-time ID format validation
- **Status Display**: Success/error feedback

### Auto-Population
- **Basic Info**: Title, type, year, description
- **Metadata**: Rating, runtime, studio, genres
- **Images**: Poster, thumbnail, cover images
- **Additional**: Languages, trailer, cast/crew info

### Visual Feedback
- **Loading States**: Spinner animations during API calls
- **Success Messages**: Confirmation of successful data fetch
- **Error Messages**: Detailed error descriptions
- **Data Preview**: Visual display of fetched TMDB data

## Security Considerations

### API Key Protection
- **Environment Variables**: API key stored in environment variables
- **Client-Side**: Key is exposed in client-side code (standard for public APIs)
- **Rate Limiting**: Built-in protection against excessive requests

### Data Validation
- **Input Validation**: TMDB ID format validation
- **Response Validation**: Comprehensive response data validation
- **Error Boundaries**: Graceful error handling and recovery

## Performance Optimizations

### Request Optimization
- **Rate Limiting**: Prevents API quota exhaustion
- **Caching**: Browser-level HTTP caching
- **Batch Requests**: Single request with multiple data appends

### Image Optimization
- **Multiple Sizes**: Different image sizes for different use cases
- **Lazy Loading**: Images loaded on demand
- **Fallback Handling**: Graceful handling of missing images

## Future Enhancements

### Potential Improvements
1. **Caching Layer**: Local storage caching for frequently accessed data
2. **Offline Support**: Service worker for offline functionality
3. **Advanced Search**: Filters, sorting, pagination
4. **Bulk Operations**: Batch processing for multiple items
5. **Image Optimization**: WebP format support, responsive images

### API Extensions
1. **Season/Episode Data**: Detailed TV show episode information
2. **Person Data**: Cast and crew detailed information
3. **Collection Data**: Movie collection information
4. **Recommendation Engine**: Related content suggestions

## Troubleshooting

### Common Issues
1. **API Key Invalid**: Check environment variable configuration
2. **Rate Limit Exceeded**: Reduce request frequency
3. **Network Errors**: Check internet connection and TMDB status
4. **Invalid TMDB ID**: Verify ID format and existence

### Debug Tools
1. **Browser Console**: Check for error messages and API responses
2. **Network Tab**: Monitor API requests and responses
3. **TMDB Test Suite**: Run comprehensive tests to identify issues
4. **Admin Panel Diagnostics**: Built-in testing buttons

## Conclusion

The TMDB integration provides a comprehensive solution for automatic content data fetching and management. With robust error handling, comprehensive testing, and user-friendly interfaces, it significantly enhances the admin panel's functionality and user experience.

All placeholder functions have been replaced with real API implementations, providing production-ready TMDB integration for the streaming database website.
