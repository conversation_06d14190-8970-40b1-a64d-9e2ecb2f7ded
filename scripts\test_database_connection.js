/**
 * Test Database Connection
 * Quick script to verify database connectivity and show current status
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'dbadmin_streamdb',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'streamdb_database',
  socketPath: process.env.DB_SOCKET || '/var/run/mysqld/mysqld.sock',
};

async function testDatabaseConnection() {
  let connection;
  
  try {
    console.log('🔄 Testing database connection...');
    console.log(`   Host: ${dbConfig.host || 'socket'}`);
    console.log(`   Database: ${dbConfig.database}`);
    console.log(`   User: ${dbConfig.user}`);
    console.log(`   Socket: ${dbConfig.socketPath || 'N/A'}`);
    console.log('');

    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection successful!');

    // Test basic query
    const [result] = await connection.execute('SELECT NOW() as current_time, DATABASE() as database_name');
    console.log(`✅ Current time: ${result[0].current_time}`);
    console.log(`✅ Database name: ${result[0].database_name}`);

    // Check important tables
    console.log('\n🔍 Checking important tables...');
    
    const tables = [
      'admin_users',
      'content',
      'content_sections',
      'categories',
      'section_categories'
    ];

    for (const table of tables) {
      try {
        const [tableCheck] = await connection.execute(`
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() AND table_name = ?
        `, [table]);
        
        if (tableCheck[0].count > 0) {
          const [rowCount] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
          console.log(`✅ ${table}: exists (${rowCount[0].count} rows)`);
        } else {
          console.log(`❌ ${table}: missing`);
        }
      } catch (error) {
        console.log(`❌ ${table}: error - ${error.message}`);
      }
    }

    // Check admin users specifically
    console.log('\n👤 Checking admin users...');
    try {
      const [adminUsers] = await connection.execute(`
        SELECT id, username, email, role, is_active, created_at 
        FROM admin_users 
        ORDER BY created_at DESC
      `);
      
      if (adminUsers.length > 0) {
        console.log(`✅ Found ${adminUsers.length} admin user(s):`);
        adminUsers.forEach(user => {
          console.log(`   - ${user.username} (${user.email}) - ${user.role} - ${user.is_active ? 'Active' : 'Inactive'}`);
        });
      } else {
        console.log('⚠️  No admin users found');
        console.log('   Run: node scripts/setup_admin_for_testing.js');
      }
    } catch (error) {
      console.log('❌ admin_users table not found or error:', error.message);
      console.log('   Run: node scripts/setup_admin_for_testing.js');
    }

    // Check content sections
    console.log('\n📁 Checking content sections...');
    try {
      const [sections] = await connection.execute(`
        SELECT id, name, slug, is_active, show_on_homepage 
        FROM content_sections 
        ORDER BY display_order
      `);
      
      if (sections.length > 0) {
        console.log(`✅ Found ${sections.length} content section(s):`);
        sections.forEach(section => {
          console.log(`   - ${section.name} (${section.slug}) - ${section.is_active ? 'Active' : 'Inactive'} - ${section.show_on_homepage ? 'On Homepage' : 'Hidden'}`);
        });
      } else {
        console.log('⚠️  No content sections found');
        console.log('   Run: cd database && node run_migration.js');
      }
    } catch (error) {
      console.log('❌ content_sections table not found or error:', error.message);
      console.log('   Run: cd database && node run_migration.js');
    }

    console.log('\n🎯 Next Steps:');
    
    // Determine what needs to be done
    const [adminCheck] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name = 'admin_users'
    `);
    
    const [sectionsCheck] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name = 'content_sections'
    `);

    if (adminCheck[0].count === 0 || sectionsCheck[0].count === 0) {
      console.log('1. Run setup script: node scripts/setup_admin_for_testing.js');
    } else {
      console.log('1. ✅ Database is ready for testing');
    }
    
    console.log('2. Start server: npm run dev (or pm2 restart index)');
    console.log('3. Visit admin panel: http://localhost:5173/admin');
    console.log('4. Login with credentials from .env file');

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Connection refused. Possible issues:');
      console.error('  - MySQL server is not running');
      console.error('  - Wrong host/port configuration');
      console.error('  - Firewall blocking connection');
      console.error('\n🔧 Try these commands:');
      console.error('  sudo systemctl status mysql');
      console.error('  sudo systemctl start mysql');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n💡 Access denied. Possible issues:');
      console.error('  - Wrong username/password in .env');
      console.error('  - User doesn\'t have database permissions');
      console.error('\n🔧 Check your .env file:');
      console.error(`  DB_USER=${dbConfig.user}`);
      console.error(`  DB_PASSWORD=${dbConfig.password ? '[SET]' : '[NOT SET]'}`);
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('\n💡 Database not found. Possible issues:');
      console.error(`  - Database '${dbConfig.database}' doesn't exist`);
      console.error('  - Wrong database name in .env');
      console.error('\n🔧 Create database:');
      console.error(`  CREATE DATABASE ${dbConfig.database};`);
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Database Connection Test');
  console.log('');
  console.log('This script will:');
  console.log('1. Test database connectivity');
  console.log('2. Check for required tables');
  console.log('3. Show admin users and content sections');
  console.log('4. Provide next steps for testing');
  console.log('');
  console.log('Usage: node test_database_connection.js');
  process.exit(0);
}

// Run the test
console.log('🧪 Database Connection Test');
console.log('===========================');
testDatabaseConnection();
