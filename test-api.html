<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <div id="results"></div>
    
    <script>
        async function testAPI() {
            const results = document.getElementById('results');
            
            try {
                // Test sections API
                const sectionsResponse = await fetch('/api/sections?active_only=true');
                const sectionsData = await sectionsResponse.json();
                results.innerHTML += `<p>Sections API: ${sectionsData.success ? 'SUCCESS' : 'FAILED'}</p>`;
                
                // Test section content API
                const contentResponse = await fetch('/api/sections/1/content');
                const contentData = await contentResponse.json();
                results.innerHTML += `<p>Section Content API: ${contentData.success ? 'SUCCESS' : 'FAILED'}</p>`;
                
                // Test auth setup
                const authResponse = await fetch('/api/auth/setup-status');
                const authData = await authResponse.json();
                results.innerHTML += `<p>Auth Setup API: ${authData.success ? 'SUCCESS' : 'FAILED'}</p>`;
                
            } catch (error) {
                results.innerHTML += `<p>Error: ${error.message}</p>`;
            }
        }
        
        testAPI();
    </script>
</body>
</html>
