#!/bin/bash

# ============================================================================
# StreamDB Maintenance Cron Job Configuration Script
# Purpose: Configure automated maintenance scheduling for both servers
# ============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_SCRIPT_PATH="/usr/local/bin/backend-maintenance.sh"
PROXY_SCRIPT_PATH="/usr/local/bin/reverse-proxy-maintenance.sh"
CRON_LOG_DIR="/var/log/streamdb-maintenance"

print_header() {
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

detect_server_type() {
    local hostname=$(hostname)
    local ip=$(hostname -I | awk '{print $1}')
    
    if [[ "$hostname" == "backend1maindb" ]] || [[ "$ip" == "***********" ]]; then
        echo "backend"
    elif [[ "$hostname" == "backend2ndrevproxy" ]] || [[ "$ip" == "*************" ]]; then
        echo "proxy"
    else
        log_warn "Could not automatically detect server type"
        echo "unknown"
    fi
}

install_backend_cron() {
    print_section "Installing Backend Server Cron Job"
    
    # Create cron job for backend server (Thursday 00:00)
    local cron_entry="0 0 * * 4 $BACKEND_SCRIPT_PATH >> $CRON_LOG_DIR/cron-backend.log 2>&1"
    
    # Add to root crontab
    (crontab -l 2>/dev/null | grep -v "$BACKEND_SCRIPT_PATH"; echo "$cron_entry") | crontab -
    
    log_info "Backend maintenance cron job installed:"
    log_info "Schedule: Every Thursday at 00:00 (midnight)"
    log_info "Command: $BACKEND_SCRIPT_PATH"
    log_info "Log: $CRON_LOG_DIR/cron-backend.log"
}

install_proxy_cron() {
    print_section "Installing Reverse Proxy Server Cron Job"
    
    # Create cron job for proxy server (Thursday 00:30 - 30 minutes after backend)
    local cron_entry="30 0 * * 4 $PROXY_SCRIPT_PATH >> $CRON_LOG_DIR/cron-proxy.log 2>&1"
    
    # Add to root crontab
    (crontab -l 2>/dev/null | grep -v "$PROXY_SCRIPT_PATH"; echo "$cron_entry") | crontab -
    
    log_info "Reverse proxy maintenance cron job installed:"
    log_info "Schedule: Every Thursday at 00:30 (30 minutes after backend)"
    log_info "Command: $PROXY_SCRIPT_PATH"
    log_info "Log: $CRON_LOG_DIR/cron-proxy.log"
}

create_cron_log_rotation() {
    print_section "Setting Up Log Rotation for Cron Logs"
    
    cat > /etc/logrotate.d/streamdb-maintenance << 'EOF'
/var/log/streamdb-maintenance/cron-*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        # No need to restart services for cron logs
    endscript
}

/var/log/streamdb-maintenance/*-maintenance-*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF
    
    log_info "Log rotation configured for maintenance logs"
}

setup_cron_environment() {
    print_section "Setting Up Cron Environment"
    
    # Ensure log directory exists
    mkdir -p "$CRON_LOG_DIR"
    chmod 755 "$CRON_LOG_DIR"
    
    # Create environment file for cron jobs
    cat > /etc/cron.d/streamdb-maintenance-env << 'EOF'
# Environment variables for StreamDB maintenance scripts
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=""

# Backend server maintenance (Thursday 00:00)
0 0 * * 4 root /usr/local/bin/backend-maintenance.sh >> /var/log/streamdb-maintenance/cron-backend.log 2>&1

# Reverse proxy server maintenance (Thursday 00:30)
30 0 * * 4 root /usr/local/bin/reverse-proxy-maintenance.sh >> /var/log/streamdb-maintenance/cron-proxy.log 2>&1
EOF
    
    log_info "Cron environment file created: /etc/cron.d/streamdb-maintenance-env"
}

verify_cron_installation() {
    print_section "Verifying Cron Installation"
    
    # Check if cron service is running
    if systemctl is-active --quiet cron; then
        log_info "Cron service is running"
    else
        log_error "Cron service is not running"
        return 1
    fi
    
    # Display current crontab
    echo -e "\n${BLUE}Current root crontab:${NC}"
    crontab -l 2>/dev/null || echo "No crontab for root"
    
    # Display cron.d file
    if [[ -f "/etc/cron.d/streamdb-maintenance-env" ]]; then
        echo -e "\n${BLUE}StreamDB maintenance cron.d file:${NC}"
        cat /etc/cron.d/streamdb-maintenance-env
    fi
    
    # Check next scheduled runs
    echo -e "\n${BLUE}Next scheduled maintenance runs:${NC}"
    local next_thursday=$(date -d "next thursday" '+%Y-%m-%d')
    log_info "Next backend maintenance: $next_thursday 00:00"
    log_info "Next proxy maintenance: $next_thursday 00:30"
}

show_manual_test_commands() {
    print_section "Manual Testing Commands"
    
    local server_type=$(detect_server_type)
    
    echo -e "${BLUE}To manually test the maintenance scripts:${NC}"
    echo ""
    
    if [[ "$server_type" == "backend" ]]; then
        echo "Backend server test:"
        echo "  sudo $BACKEND_SCRIPT_PATH"
    elif [[ "$server_type" == "proxy" ]]; then
        echo "Reverse proxy server test:"
        echo "  sudo $PROXY_SCRIPT_PATH"
    else
        echo "Backend server test:"
        echo "  sudo $BACKEND_SCRIPT_PATH"
        echo ""
        echo "Reverse proxy server test:"
        echo "  sudo $PROXY_SCRIPT_PATH"
    fi
    
    echo ""
    echo -e "${BLUE}To view maintenance logs:${NC}"
    echo "  tail -f $CRON_LOG_DIR/cron-backend.log"
    echo "  tail -f $CRON_LOG_DIR/cron-proxy.log"
    echo ""
    echo -e "${BLUE}To check cron job status:${NC}"
    echo "  crontab -l"
    echo "  systemctl status cron"
    echo ""
    echo -e "${BLUE}To view next scheduled runs:${NC}"
    echo "  grep -r 'streamdb' /var/spool/cron/ /etc/cron.d/"
}

main() {
    check_root
    
    print_header "StreamDB Maintenance Cron Job Configuration"
    
    local server_type=$(detect_server_type)
    log_info "Detected server type: $server_type"
    
    # Setup common components
    setup_cron_environment
    create_cron_log_rotation
    
    # Install appropriate cron job based on server type
    case "$server_type" in
        "backend")
            install_backend_cron
            ;;
        "proxy")
            install_proxy_cron
            ;;
        "unknown")
            log_warn "Server type unknown - installing both cron jobs"
            log_warn "Please manually remove the inappropriate one"
            install_backend_cron
            install_proxy_cron
            ;;
    esac
    
    # Restart cron service to ensure changes take effect
    systemctl restart cron
    log_info "Cron service restarted"
    
    # Verify installation
    verify_cron_installation
    
    # Show manual testing commands
    show_manual_test_commands
    
    print_header "Cron Configuration Completed Successfully"
    log_info "Maintenance scripts will run every Thursday at midnight"
    log_info "Backend server: 00:00, Reverse proxy: 00:30"
}

# Execute main function
main "$@"
