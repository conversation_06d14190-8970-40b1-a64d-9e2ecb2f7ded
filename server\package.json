{"name": "streamdb-server", "version": "1.0.0", "description": "Ultra-secure backend API server for StreamDB Online", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["streamdb", "streaming", "database", "api", "movies", "series", "secure"], "author": "StreamDB Online", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mysql-session": "^3.0.0", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.10.1", "sharp": "^0.33.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}