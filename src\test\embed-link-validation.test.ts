/**
 * Comprehensive embed link validation tests
 * Tests all video security and validation functions
 */

import { 
  isValidVideoLink, 
  isSecureVideoLink, 
  extractVideoUrl, 
  detectVideoPlatform,
  encodeVideoLinks,
  parseVideoLinks,
  getIFrameConfig
} from '../utils/videoSecurity';

// Test data for different scenarios
const validEmbedLinks = [
  'https://www.youtube.com/embed/dQw4w9WgXcQ',
  'https://player.vimeo.com/video/123456789',
  'https://www.dailymotion.com/embed/video/x7tgad0',
  'https://player.twitch.tv/video/123456789',
  'https://2embed.cc/embed/movie/123456',
  'https://streamable.com/e/abc123',
  'https://filemoon.to/e/abc123',
  'https://streamtape.com/e/abc123',
  'https://gradehgplus.com/e/abc123'
];

const invalidLinks = [
  'not-a-url',
  'http://malicious-site.com/embed',
  'javascript:alert("xss")',
  'data:text/html,<script>alert("xss")</script>',
  '',
  null,
  undefined
];

const iframeEmbeds = [
  '<iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>',
  '<iframe width="560" height="315" src="https://player.vimeo.com/video/123456789" frameborder="0"></iframe>',
  'https://streamable.com/e/abc123',
  '//player.example.com/embed/video123'
];

/**
 * Test basic URL validation
 */
export function testBasicValidation(): boolean {
  console.group('🔍 Testing Basic URL Validation');
  
  let allPassed = true;
  
  // Test valid links
  validEmbedLinks.forEach(link => {
    const isValid = isValidVideoLink(link);
    console.log(`${link}: ${isValid ? '✅' : '❌'}`);
    if (!isValid) {
      console.error(`Expected ${link} to be valid`);
      allPassed = false;
    }
  });
  
  // Test invalid links
  invalidLinks.forEach(link => {
    const isValid = isValidVideoLink(link);
    console.log(`${link}: ${isValid ? '❌' : '✅'}`);
    if (isValid) {
      console.error(`Expected ${link} to be invalid`);
      allPassed = false;
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test security validation
 */
export function testSecurityValidation(): boolean {
  console.group('🔒 Testing Security Validation');
  
  let allPassed = true;
  
  validEmbedLinks.forEach(link => {
    const isSecure = isSecureVideoLink(link);
    console.log(`${link}: ${isSecure ? '✅ Secure' : '⚠️ Insecure'}`);
    
    // All HTTPS links should be secure
    if (link.startsWith('https://') && !isSecure) {
      console.error(`Expected HTTPS link to be secure: ${link}`);
      allPassed = false;
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test platform detection
 */
export function testPlatformDetection(): boolean {
  console.group('🎬 Testing Platform Detection');
  
  let allPassed = true;
  
  const expectedPlatforms = {
    'https://www.youtube.com/embed/dQw4w9WgXcQ': 'youtube',
    'https://player.vimeo.com/video/123456789': 'vimeo',
    'https://www.dailymotion.com/embed/video/x7tgad0': 'dailymotion',
    'https://player.twitch.tv/video/123456789': 'twitch',
    'https://2embed.cc/embed/movie/123456': '2embed',
    'https://streamable.com/e/abc123': 'streamable'
  };
  
  Object.entries(expectedPlatforms).forEach(([url, expectedPlatform]) => {
    const detectedPlatform = detectVideoPlatform(url);
    const isCorrect = detectedPlatform === expectedPlatform;
    
    console.log(`${url}`);
    console.log(`  Expected: ${expectedPlatform}, Detected: ${detectedPlatform} ${isCorrect ? '✅' : '❌'}`);
    
    if (!isCorrect) {
      allPassed = false;
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test URL extraction from various formats
 */
export function testUrlExtraction(): boolean {
  console.group('🔗 Testing URL Extraction');
  
  let allPassed = true;
  
  iframeEmbeds.forEach(embed => {
    const extractedUrl = extractVideoUrl(embed);
    console.log(`Input: ${embed}`);
    console.log(`Extracted: ${extractedUrl}`);
    
    if (extractedUrl && !isValidVideoLink(extractedUrl)) {
      console.error(`Extracted URL is not valid: ${extractedUrl}`);
      allPassed = false;
    } else {
      console.log('✅ Extraction successful');
    }
    console.log('');
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test encoding and parsing functionality
 */
export function testEncodingParsing(): boolean {
  console.group('🔐 Testing Encoding and Parsing');
  
  let allPassed = true;
  
  const testLinks = validEmbedLinks.slice(0, 3); // Test with first 3 links
  
  testLinks.forEach(link => {
    console.log(`Testing: ${link}`);
    
    // Test encoding
    const encoded = encodeVideoLinks([link]);
    console.log(`Encoded: ${encoded}`);
    
    // Test parsing
    const parsed = parseVideoLinks(encoded);
    console.log(`Parsed: ${JSON.stringify(parsed)}`);
    
    // Verify round-trip
    if (parsed.length !== 1 || parsed[0] !== link) {
      console.error(`Round-trip failed for ${link}`);
      allPassed = false;
    } else {
      console.log('✅ Round-trip successful');
    }
    console.log('');
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test iframe configuration generation
 */
export function testIFrameConfig(): boolean {
  console.group('⚙️ Testing IFrame Configuration');
  
  let allPassed = true;
  
  validEmbedLinks.forEach(link => {
    const config = getIFrameConfig(link);
    console.log(`${link}:`);
    console.log(`  Config:`, config);
    
    // Verify config has required properties
    if (!config.src || !config.title) {
      console.error(`Invalid config for ${link}`);
      allPassed = false;
    } else {
      console.log('✅ Valid config');
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Run all embed link validation tests
 */
export function runAllEmbedLinkTests(): boolean {
  console.log('🎯 Starting Embed Link Validation Tests');
  console.log('=========================================');
  
  const results = [
    testBasicValidation(),
    testSecurityValidation(),
    testPlatformDetection(),
    testUrlExtraction(),
    testEncodingParsing(),
    testIFrameConfig()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('=========================================');
  if (allPassed) {
    console.log('✅ All embed link tests passed!');
  } else {
    console.log('❌ Some embed link tests failed!');
  }
  
  return allPassed;
}

// Export individual test functions
export {
  testBasicValidation as testEmbedLinks
};

/**
 * Test the user's specific failing links that were previously having issues
 */
export function testUserFailingLinks(): boolean {
  console.group('🔧 Testing Previously Failing Links');

  const userFailingLinks = [
    'https://gradehgplus.com/e/xvay1ggua7s7',
    'https://streamtape.com/v/YeRw6amy3MsvWa7/Dont.Leave.2022.720p.WEB-DL.English.ESubs.MoviesMod.com.mkv',
    'https://filemoon.to/e/ezfjmgsjwwsh'
  ];

  let allPassed = true;

  userFailingLinks.forEach(link => {
    console.log(`Testing: ${link}`);

    const isValid = isValidVideoLink(link);
    const isSecure = isSecureVideoLink(link);
    const platform = detectVideoPlatform(link);
    const config = getIFrameConfig(link);

    console.log(`  Valid: ${isValid ? '✅' : '❌'}`);
    console.log(`  Secure: ${isSecure ? '✅' : '⚠️'}`);
    console.log(`  Platform: ${platform}`);
    console.log(`  Config: ${config ? '✅' : '❌'}`);

    if (!isValid) {
      console.error(`  Failed validation: ${link}`);
      allPassed = false;
    }
  });

  console.groupEnd();
  return allPassed;
}

// Export for use in other test files
export default {
  testBasicValidation,
  testSecurityValidation,
  testPlatformDetection,
  testUrlExtraction,
  testEncodingParsing,
  testIFrameConfig,
  testUserFailingLinks,
  runAllEmbedLinkTests
};
