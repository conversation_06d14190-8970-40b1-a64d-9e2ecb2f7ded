/**
 * Database Migration Runner
 * Runs the dynamic sections migration
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'dbadmin_streamdb',
  password: process.env.DB_PASSWORD || process.env.DB_PASS,
  database: process.env.DB_NAME || 'streamdb_database',
  socketPath: process.env.DB_SOCKET || '/var/run/mysqld/mysqld.sock',
  multipleStatements: true
};

async function runMigration() {
  let connection;
  
  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database successfully');

    // Read migration file
    const migrationPath = path.join(__dirname, 'migrations', '001_add_dynamic_sections.sql');
    console.log('📖 Reading migration file:', migrationPath);
    
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    console.log('✅ Migration file loaded');

    // Execute migration
    console.log('🚀 Executing migration...');
    const [results] = await connection.execute(migrationSQL);
    
    console.log('✅ Migration executed successfully!');
    
    // Show results if any
    if (Array.isArray(results)) {
      results.forEach((result, index) => {
        if (result && result.length > 0) {
          console.log(`Result ${index + 1}:`, result);
        }
      });
    }

    // Verify sections were created
    console.log('🔍 Verifying sections were created...');
    const [sections] = await connection.execute('SELECT * FROM content_sections ORDER BY display_order');
    console.log(`✅ Found ${sections.length} content sections:`);
    sections.forEach(section => {
      console.log(`  - ${section.name} (${section.slug}) - ${section.is_active ? 'Active' : 'Inactive'}`);
    });

    // Verify categories were created
    const [categories] = await connection.execute('SELECT COUNT(*) as count FROM categories');
    console.log(`✅ Found ${categories[0].count} categories`);

    // Check if content table has section_id column
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'content' 
      AND COLUMN_NAME = 'section_id'
    `);
    
    if (columns.length > 0) {
      console.log('✅ Content table has section_id column');
      
      // Check how many content items have been assigned to sections
      const [contentStats] = await connection.execute(`
        SELECT 
          COUNT(*) as total_content,
          COUNT(section_id) as assigned_to_sections
        FROM content
      `);
      
      console.log(`📊 Content assignment: ${contentStats[0].assigned_to_sections}/${contentStats[0].total_content} items assigned to sections`);
    } else {
      console.log('❌ Content table missing section_id column');
    }

    console.log('\n🎉 Dynamic sections migration completed successfully!');
    console.log('📝 Next steps:');
    console.log('  1. Restart your application server');
    console.log('  2. Visit the admin panel to manage sections');
    console.log('  3. Check the homepage for dynamic sections');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Database connection refused. Please check:');
      console.error('  - Database server is running');
      console.error('  - Connection credentials are correct');
      console.error('  - Socket path is accessible');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Access denied. Please check:');
      console.error('  - Database username and password');
      console.error('  - User has necessary permissions');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 Database not found. Please check:');
      console.error('  - Database name is correct');
      console.error('  - Database exists');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Dynamic Sections Migration Runner');
  console.log('');
  console.log('Usage: node run_migration.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --dry-run      Show what would be executed without running');
  console.log('');
  console.log('Environment Variables:');
  console.log('  DB_HOST        Database host (default: localhost)');
  console.log('  DB_USER        Database user (default: dbadmin_streamdb)');
  console.log('  DB_PASSWORD    Database password');
  console.log('  DB_NAME        Database name (default: streamdb_database)');
  console.log('  DB_SOCKET      Database socket path (default: /var/run/mysqld/mysqld.sock)');
  process.exit(0);
}

if (args.includes('--dry-run')) {
  console.log('🔍 Dry run mode - showing migration content:');
  console.log('');
  
  fs.readFile(path.join(__dirname, 'migrations', '001_add_dynamic_sections.sql'), 'utf8')
    .then(content => {
      console.log(content);
      console.log('');
      console.log('💡 This is what would be executed. Run without --dry-run to execute.');
    })
    .catch(error => {
      console.error('❌ Failed to read migration file:', error);
      process.exit(1);
    });
} else {
  // Run the migration
  runMigration();
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️  Migration interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Migration terminated');
  process.exit(1);
});
