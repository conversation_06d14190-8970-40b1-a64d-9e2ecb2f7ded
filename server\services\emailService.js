const nodemailer = require('nodemailer');
require('dotenv').config();

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  initializeTransporter() {
    try {
      // Configure email transporter based on environment variables
      const emailConfig = {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        },
        tls: {
          rejectUnauthorized: false // Allow self-signed certificates
        }
      };

      // Only create transporter if SMTP credentials are provided
      if (process.env.SMTP_USER && process.env.SMTP_PASS) {
        this.transporter = nodemailer.createTransport(emailConfig);
        console.log('✅ Email service initialized successfully');
      } else {
        console.log('⚠️ Email service not configured - SMTP credentials missing');
      }
    } catch (error) {
      console.error('❌ Email service initialization failed:', error.message);
    }
  }

  async sendPasswordResetEmail(email, resetToken, adminUsername) {
    if (!this.transporter) {
      throw new Error('Email service not configured. Please set SMTP credentials.');
    }

    const resetUrl = `${process.env.FRONTEND_URL || 'https://streamdb.online'}/reset-password?token=${resetToken}`;
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset - StreamDB Admin</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
          .header { background: linear-gradient(135deg, #e6cb8e, #d4b876); padding: 30px; text-align: center; }
          .header h1 { color: #2c2c2c; margin: 0; font-size: 28px; font-weight: 600; }
          .content { padding: 40px 30px; }
          .reset-button { display: inline-block; background: #e6cb8e; color: #2c2c2c; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 20px 0; transition: background 0.3s; }
          .reset-button:hover { background: #d4b876; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
          .security-info { background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔐 StreamDB Admin</h1>
            <p style="margin: 10px 0 0 0; color: #2c2c2c; font-size: 16px;">Password Reset Request</p>
          </div>
          
          <div class="content">
            <h2 style="color: #2c2c2c; margin-bottom: 20px;">Hello ${adminUsername},</h2>
            
            <p>We received a request to reset your StreamDB admin panel password. If you made this request, click the button below to reset your password:</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" class="reset-button">Reset Password</a>
            </div>
            
            <div class="security-info">
              <h3 style="margin-top: 0; color: #1976d2;">🛡️ Security Information</h3>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>This link will expire in <strong>1 hour</strong> for security</li>
                <li>The link can only be used <strong>once</strong></li>
                <li>If you didn't request this reset, please ignore this email</li>
              </ul>
            </div>
            
            <div class="warning">
              <h3 style="margin-top: 0; color: #856404;">⚠️ Important Security Notice</h3>
              <p style="margin-bottom: 0;">If you did not request this password reset, please:</p>
              <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                <li>Do not click the reset link</li>
                <li>Check your admin panel for any unauthorized access</li>
                <li>Consider changing your password immediately</li>
              </ul>
            </div>
            
            <p style="margin-top: 30px;">If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px;">${resetUrl}</p>
          </div>
          
          <div class="footer">
            <p><strong>StreamDB Admin Panel</strong></p>
            <p>This is an automated security email. Please do not reply to this message.</p>
            <p style="font-size: 12px; color: #999;">Generated on ${new Date().toLocaleString()}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const textContent = `
StreamDB Admin - Password Reset Request

Hello ${adminUsername},

We received a request to reset your StreamDB admin panel password.

Reset your password by visiting this link:
${resetUrl}

SECURITY INFORMATION:
- This link expires in 1 hour
- The link can only be used once
- If you didn't request this reset, please ignore this email

If you did not request this password reset:
- Do not click the reset link
- Check your admin panel for unauthorized access
- Consider changing your password immediately

---
StreamDB Admin Panel
This is an automated security email.
Generated on ${new Date().toLocaleString()}
    `;

    const mailOptions = {
      from: `"StreamDB Admin" <${process.env.SMTP_USER}>`,
      to: email,
      subject: '🔐 StreamDB Admin - Password Reset Request',
      text: textContent,
      html: htmlContent,
      headers: {
        'X-Priority': '1',
        'X-MSMail-Priority': 'High',
        'Importance': 'high'
      }
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Password reset email sent successfully:', info.messageId);
      return {
        success: true,
        messageId: info.messageId
      };
    } catch (error) {
      console.error('❌ Failed to send password reset email:', error.message);
      throw new Error('Failed to send password reset email');
    }
  }

  async verifyConnection() {
    if (!this.transporter) {
      return { success: false, error: 'Email service not configured' };
    }

    try {
      await this.transporter.verify();
      return { success: true, message: 'Email service connection verified' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

module.exports = new EmailService();
