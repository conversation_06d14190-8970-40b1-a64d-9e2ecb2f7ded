/**
 * Protected Route Component
 * Wraps routes that require authentication and authorization
 */

import { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { ProtectedRouteConfig, PERMISSIONS } from '@/types/auth';
import { SecurityLogger } from '@/utils/authUtils';
import { Loader2, Shield, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ProtectedRouteProps {
  children: React.ReactNode;
  config?: ProtectedRouteConfig;
  fallback?: React.ReactNode;
}

/**
 * Default protection configuration for admin routes
 */
const DEFAULT_PROTECTION_CONFIG: ProtectedRouteConfig = {
  requireAuth: true,
  requiredPermissions: [PERMISSIONS.ADMIN_PANEL_ACCESS],
  redirectTo: '/login',
  allowedRoles: ['admin'],
};

/**
 * Loading component shown while checking authentication
 */
function AuthLoadingScreen() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Shield className="w-8 h-8 text-primary" />
              <Loader2 className="w-4 h-4 animate-spin absolute -top-1 -right-1 text-primary" />
            </div>
            <div className="text-center">
              <h3 className="font-semibold">Verifying Access</h3>
              <p className="text-sm text-muted-foreground">
                Checking your authentication status...
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Access denied component shown when user lacks permissions
 */
function AccessDeniedScreen({ 
  missingPermissions, 
  userRole, 
  onRetry 
}: { 
  missingPermissions: string[];
  userRole?: string;
  onRetry: () => void;
}) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-6 h-6 text-destructive" />
          </div>
          <CardTitle className="text-xl font-bold text-destructive">
            Access Denied
          </CardTitle>
          <CardDescription>
            You don't have permission to access this page
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <Alert className="border-destructive/50 bg-destructive/10">
            <AlertTriangle className="h-4 w-4 text-destructive" />
            <AlertDescription className="text-destructive">
              <div className="space-y-2">
                <p className="font-medium">Missing Permissions:</p>
                <ul className="list-disc list-inside text-sm space-y-1">
                  {missingPermissions.map((permission) => (
                    <li key={permission} className="font-mono">
                      {permission}
                    </li>
                  ))}
                </ul>
                {userRole && (
                  <p className="text-sm mt-2">
                    Current role: <span className="font-mono">{userRole}</span>
                  </p>
                )}
              </div>
            </AlertDescription>
          </Alert>

          <div className="flex flex-col gap-2">
            <Button onClick={onRetry} variant="outline" className="w-full">
              <Shield className="w-4 h-4 mr-2" />
              Retry Access Check
            </Button>
            <Button 
              onClick={() => window.location.href = '/'} 
              variant="secondary" 
              className="w-full"
            >
              Return to Home
            </Button>
          </div>

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Contact your administrator if you believe this is an error
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Protected Route Component
 */
export default function ProtectedRoute({ 
  children, 
  config = DEFAULT_PROTECTION_CONFIG,
  fallback 
}: ProtectedRouteProps) {
  const { authState, checkAuthStatus, hasPermission, refreshSession } = useAuth();
  const location = useLocation();
  const [isChecking, setIsChecking] = useState(true);
  const [accessDenied, setAccessDenied] = useState(false);
  const [missingPermissions, setMissingPermissions] = useState<string[]>([]);

  /**
   * Check authentication and authorization
   */
  useEffect(() => {
    const checkAccess = async () => {
      setIsChecking(true);
      setAccessDenied(false);
      setMissingPermissions([]);

      try {
        // Check if authentication is required
        if (!config.requireAuth) {
          setIsChecking(false);
          return;
        }

        // Check if user is authenticated
        const isAuthenticated = checkAuthStatus();
        if (!isAuthenticated) {
          // Try to refresh session before redirecting
          const sessionRefreshed = await refreshSession();
          if (!sessionRefreshed) {
            SecurityLogger.logEvent('SECURITY_VIOLATION', {
              reason: 'Unauthenticated access attempt',
              path: location.pathname,
            }, 'medium');
            setIsChecking(false);
            return;
          }
        }

        // Check role-based access
        if (config.allowedRoles && authState.user) {
          const hasValidRole = config.allowedRoles.includes(authState.user.role);
          if (!hasValidRole) {
            SecurityLogger.logEvent('SECURITY_VIOLATION', {
              reason: 'Insufficient role',
              userRole: authState.user.role,
              requiredRoles: config.allowedRoles,
              path: location.pathname,
            }, 'high');
            setAccessDenied(true);
            setIsChecking(false);
            return;
          }
        }

        // Check permission-based access
        if (config.requiredPermissions && config.requiredPermissions.length > 0) {
          const missing = config.requiredPermissions.filter(
            permission => !hasPermission(permission)
          );

          if (missing.length > 0) {
            SecurityLogger.logEvent('SECURITY_VIOLATION', {
              reason: 'Insufficient permissions',
              missingPermissions: missing,
              userPermissions: authState.user?.permissions || [],
              path: location.pathname,
            }, 'high');
            setMissingPermissions(missing);
            setAccessDenied(true);
            setIsChecking(false);
            return;
          }
        }

        // Access granted
        SecurityLogger.logEvent('LOGIN_SUCCESS', {
          path: location.pathname,
          userId: authState.user?.id,
        }, 'low');

      } catch (error) {
        console.error('Error checking access:', error);
        SecurityLogger.logEvent('SECURITY_VIOLATION', {
          reason: 'Access check error',
          error: error instanceof Error ? error.message : 'Unknown error',
          path: location.pathname,
        }, 'critical');
      } finally {
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [
    authState.isAuthenticated,
    authState.user,
    location.pathname,
    config,
    checkAuthStatus,
    hasPermission,
    refreshSession,
  ]);

  /**
   * Retry access check
   */
  const handleRetry = () => {
    setIsChecking(true);
    setAccessDenied(false);
    setMissingPermissions([]);
    
    // Trigger re-check by updating a dependency
    setTimeout(() => {
      setIsChecking(false);
    }, 1000);
  };

  // Show loading screen while checking
  if (isChecking) {
    return fallback || <AuthLoadingScreen />;
  }

  // Show access denied screen
  if (accessDenied) {
    return (
      <AccessDeniedScreen
        missingPermissions={missingPermissions}
        userRole={authState.user?.role}
        onRetry={handleRetry}
      />
    );
  }

  // Redirect to login if not authenticated
  if (config.requireAuth && !authState.isAuthenticated) {
    return (
      <Navigate
        to={config.redirectTo || '/login'}
        state={{ from: location }}
        replace
      />
    );
  }

  // Render protected content
  return <>{children}</>;
}

/**
 * Higher-order component for protecting routes
 */
export function withProtection(
  Component: React.ComponentType,
  config?: ProtectedRouteConfig
) {
  return function ProtectedComponent(props: any) {
    return (
      <ProtectedRoute config={config}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

/**
 * Hook for checking if current user has access to a route
 */
export function useRouteAccess(config: ProtectedRouteConfig) {
  const { authState, hasPermission } = useAuth();

  const hasAccess = () => {
    if (!config.requireAuth) return true;
    if (!authState.isAuthenticated) return false;

    if (config.allowedRoles && authState.user) {
      if (!config.allowedRoles.includes(authState.user.role)) return false;
    }

    if (config.requiredPermissions) {
      return config.requiredPermissions.every(permission => hasPermission(permission));
    }

    return true;
  };

  return {
    hasAccess: hasAccess(),
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
  };
}
