
import { Link, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import SearchBar from "@/components/SearchBar";
import { Button } from "@/components/ui/button";
import { scrollToTop } from "@/utils/scrollToTop";

const navItems = [
  { label: "Home", path: "/" },
  { label: "Movies", path: "/movies" },
  { label: "Web-Series", path: "/series" },
  { label: "Categories", path: "/categories" },
  { label: "Requested", path: "/requested" },
];

const Header = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Scroll to top whenever route changes
  useEffect(() => {
    scrollToTop();
  }, [location.pathname]);

  const handleNavClick = () => {
    setIsMobileMenuOpen(false);
    scrollToTop();
  };

  const handleLogoClick = () => {
    scrollToTop();
  };

  return (
    <header className="sticky top-0 left-0 w-full z-40 bg-background/90 backdrop-blur border-b border-border shadow transition">
      <div className="flex items-center justify-between px-3 sm:px-9 py-2.5 max-w-7xl mx-auto gap-2 sm:gap-5">
        {/* Logo */}
        <div
          className="rounded-lg min-w-[100px] sm:min-w-[133px] min-h-[45px] sm:min-h-[58px] flex items-center bg-background"
          style={{
            background: "hsl(var(--background))",
            height: "45px",
          }}
        >
          <Link
            to="/"
            className="flex items-center gap-2 hover:opacity-80 duration-150 shrink-0 rounded-lg bg-background transform transition-transform hover:scale-110 active:scale-95 cursor-pointer"
            style={{
              background: "hsl(var(--background))",
              padding: 0,
              border: "none",
              margin: 0,
            }}
            onClick={handleLogoClick}
          >
            <img
              src="/lovable-uploads/08a17582-87d4-4b28-815b-4eb58f048b24.png"
              alt="STREAM DB Logo"
              className="h-[45px] sm:h-[58px] w-auto transition-all duration-200 hover:drop-shadow-[0_0_12px_#e6cb8e] hover:brightness-110"
              style={{
                background: "transparent",
                filter:
                  "drop-shadow(0 0 8px #e6cb8e) drop-shadow(0 0 17px #e6cb8eaa)",
                margin: 0,
                padding: 0,
                display: "block",
                maxHeight: "45px",
              }}
            />
            <span className="sr-only">STREAM DB</span>
          </Link>
        </div>

        {/* Centered Search Bar - Hidden on mobile */}
        <div className="hidden md:flex flex-1 justify-center mx-1">
          <div className="w-full max-w-lg">
            <SearchBar />
          </div>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-3 sm:gap-7 whitespace-nowrap main-nav">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`main-nav-link relative rounded-md transition-colors duration-150 hover:bg-primary/10
                ${
                  location.pathname === item.path
                    ? "text-primary bg-primary/10 ring-2 ring-primary ring-offset-2"
                    : "text-muted-foreground hover:text-primary"
                }
              `}
              onClick={handleNavClick}
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden p-2"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>
      </div>

      {/* Mobile Search Bar */}
      <div className="md:hidden px-3 pb-3">
        <SearchBar />
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-background/95 backdrop-blur border-t border-border">
          <nav className="flex flex-col py-2">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`px-3 py-2 text-sm transition-colors duration-150 hover:bg-primary/10
                  ${
                    location.pathname === item.path
                      ? "text-primary bg-primary/10 border-l-2 border-primary"
                      : "text-muted-foreground hover:text-primary"
                  }
                `}
                onClick={handleNavClick}
              >
                {item.label}
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
