/**
 * StreamDB Database Continuous Monitoring Service
 * Real-time monitoring for database health, connection status, and performance
 * 
 * Features:
 * - Continuous connection health monitoring
 * - Real-time error detection and alerting
 * - Performance metrics tracking
 * - Automatic recovery attempts
 * - Integration with existing logging systems
 * 
 * Safety: Non-intrusive monitoring, graceful degradation, configurable intervals
 */

const DatabaseDiagnostic = require('./database-diagnostic');
const mysql = require('mysql2/promise');
const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class DatabaseMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      healthCheckInterval: options.healthCheckInterval || 30000, // 30 seconds
      performanceCheckInterval: options.performanceCheckInterval || 300000, // 5 minutes
      schemaCheckInterval: options.schemaCheckInterval || 3600000, // 1 hour
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 5000, // 5 seconds
      alertThreshold: options.alertThreshold || 3, // Alert after 3 consecutive failures
      enableLogging: options.enableLogging !== false,
      logPath: options.logPath || path.join(__dirname, '../logs/database-monitor.log'),
      ...options
    };
    
    this.diagnostic = new DatabaseDiagnostic();
    this.isRunning = false;
    this.intervals = {};
    this.healthStatus = {
      isHealthy: true,
      lastCheck: null,
      consecutiveFailures: 0,
      lastError: null,
      uptime: 0,
      startTime: null
    };
    
    this.metrics = {
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      averageResponseTime: 0,
      lastPerformanceCheck: null,
      connectionPoolStatus: 'unknown'
    };
    
    // Bind event handlers
    this.on('healthCheck', this.handleHealthCheck.bind(this));
    this.on('performanceCheck', this.handlePerformanceCheck.bind(this));
    this.on('schemaCheck', this.handleSchemaCheck.bind(this));
    this.on('error', this.handleError.bind(this));
    this.on('recovery', this.handleRecovery.bind(this));
  }

  /**
   * Start continuous monitoring
   */
  async start() {
    if (this.isRunning) {
      this.log('WARN', 'Monitor already running');
      return;
    }

    this.log('INFO', 'Starting database monitoring service...');
    this.isRunning = true;
    this.healthStatus.startTime = new Date();
    
    // Initial health check
    await this.performHealthCheck();
    
    // Set up monitoring intervals
    this.intervals.health = setInterval(() => {
      this.performHealthCheck().catch(error => {
        this.emit('error', error);
      });
    }, this.options.healthCheckInterval);
    
    this.intervals.performance = setInterval(() => {
      this.performPerformanceCheck().catch(error => {
        this.emit('error', error);
      });
    }, this.options.performanceCheckInterval);
    
    this.intervals.schema = setInterval(() => {
      this.performSchemaCheck().catch(error => {
        this.emit('error', error);
      });
    }, this.options.schemaCheckInterval);
    
    this.log('INFO', `Database monitoring started with intervals: health=${this.options.healthCheckInterval}ms, performance=${this.options.performanceCheckInterval}ms, schema=${this.options.schemaCheckInterval}ms`);
    
    // Emit started event
    this.emit('started', {
      timestamp: new Date().toISOString(),
      intervals: this.options
    });
  }

  /**
   * Stop monitoring service
   */
  async stop() {
    if (!this.isRunning) {
      this.log('WARN', 'Monitor is not running');
      return;
    }

    this.log('INFO', 'Stopping database monitoring service...');
    this.isRunning = false;
    
    // Clear all intervals
    Object.values(this.intervals).forEach(interval => {
      if (interval) clearInterval(interval);
    });
    this.intervals = {};
    
    // Calculate uptime
    if (this.healthStatus.startTime) {
      this.healthStatus.uptime = Date.now() - this.healthStatus.startTime.getTime();
    }
    
    this.log('INFO', `Database monitoring stopped. Uptime: ${Math.round(this.healthStatus.uptime / 1000)}s`);
    
    // Emit stopped event
    this.emit('stopped', {
      timestamp: new Date().toISOString(),
      uptime: this.healthStatus.uptime,
      metrics: this.metrics
    });
  }

  /**
   * Perform health check
   */
  async performHealthCheck() {
    const startTime = Date.now();
    this.metrics.totalChecks++;
    
    try {
      const isHealthy = await this.diagnostic.testDatabaseConnection();
      const responseTime = Date.now() - startTime;
      
      // Update metrics
      this.updateResponseTimeMetrics(responseTime);
      
      if (isHealthy) {
        this.metrics.successfulChecks++;
        
        // Reset failure counter on success
        if (this.healthStatus.consecutiveFailures > 0) {
          this.emit('recovery', {
            timestamp: new Date().toISOString(),
            previousFailures: this.healthStatus.consecutiveFailures,
            responseTime
          });
        }
        
        this.healthStatus.consecutiveFailures = 0;
        this.healthStatus.isHealthy = true;
        this.healthStatus.lastError = null;
        
      } else {
        this.handleHealthCheckFailure('Health check returned false', responseTime);
      }
      
      this.healthStatus.lastCheck = new Date();
      this.emit('healthCheck', {
        isHealthy,
        responseTime,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.handleHealthCheckFailure(error.message, responseTime);
      this.emit('healthCheck', {
        isHealthy: false,
        error: error.message,
        responseTime,
        timestamp: new Date().toISOString()
      });
    }
  }

  handleHealthCheckFailure(errorMessage, responseTime) {
    this.metrics.failedChecks++;
    this.healthStatus.consecutiveFailures++;
    this.healthStatus.isHealthy = false;
    this.healthStatus.lastError = errorMessage;
    
    // Trigger alert if threshold reached
    if (this.healthStatus.consecutiveFailures >= this.options.alertThreshold) {
      this.emit('alert', {
        type: 'HEALTH_CHECK_FAILURE',
        message: `Database health check failed ${this.healthStatus.consecutiveFailures} consecutive times`,
        error: errorMessage,
        responseTime,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Perform performance check
   */
  async performPerformanceCheck() {
    try {
      const performanceHealthy = await this.diagnostic.checkDatabasePerformance();
      const poolHealthy = await this.diagnostic.monitorConnectionPool();
      
      this.metrics.lastPerformanceCheck = new Date();
      this.metrics.connectionPoolStatus = poolHealthy ? 'healthy' : 'degraded';
      
      this.emit('performanceCheck', {
        performanceHealthy,
        poolHealthy,
        timestamp: new Date().toISOString()
      });
      
      if (!performanceHealthy || !poolHealthy) {
        this.emit('alert', {
          type: 'PERFORMANCE_DEGRADATION',
          message: 'Database performance issues detected',
          details: {
            performanceHealthy,
            poolHealthy
          },
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (error) {
      this.emit('error', error);
    }
  }

  /**
   * Perform schema validation check
   */
  async performSchemaCheck() {
    try {
      const schemaValid = await this.diagnostic.validateDatabaseSchema();
      
      this.emit('schemaCheck', {
        schemaValid,
        timestamp: new Date().toISOString()
      });
      
      if (!schemaValid) {
        this.emit('alert', {
          type: 'SCHEMA_VALIDATION_FAILURE',
          message: 'Database schema validation failed',
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (error) {
      this.emit('error', error);
    }
  }

  updateResponseTimeMetrics(responseTime) {
    // Calculate rolling average response time
    const alpha = 0.1; // Smoothing factor
    if (this.metrics.averageResponseTime === 0) {
      this.metrics.averageResponseTime = responseTime;
    } else {
      this.metrics.averageResponseTime = (alpha * responseTime) + ((1 - alpha) * this.metrics.averageResponseTime);
    }
  }

  /**
   * Get current monitoring status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      healthStatus: {
        ...this.healthStatus,
        uptime: this.healthStatus.startTime ? Date.now() - this.healthStatus.startTime.getTime() : 0
      },
      metrics: {
        ...this.metrics,
        successRate: this.metrics.totalChecks > 0 ? 
          Math.round((this.metrics.successfulChecks / this.metrics.totalChecks) * 100) : 0,
        averageResponseTime: Math.round(this.metrics.averageResponseTime)
      },
      intervals: this.options
    };
  }

  /**
   * Event handlers
   */
  handleHealthCheck(data) {
    this.log('DEBUG', `Health check: ${data.isHealthy ? 'PASS' : 'FAIL'} (${data.responseTime}ms)`);
  }

  handlePerformanceCheck(data) {
    this.log('DEBUG', `Performance check: ${data.performanceHealthy && data.poolHealthy ? 'PASS' : 'FAIL'}`);
  }

  handleSchemaCheck(data) {
    this.log('DEBUG', `Schema check: ${data.schemaValid ? 'PASS' : 'FAIL'}`);
  }

  handleError(error) {
    this.log('ERROR', `Monitor error: ${error.message}`);
  }

  handleRecovery(data) {
    this.log('INFO', `Database recovered after ${data.previousFailures} failures (${data.responseTime}ms)`);
  }

  /**
   * Logging utility
   */
  async log(level, message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level}] ${message}`;
    
    // Console output
    console.log(logEntry);
    
    // File logging if enabled
    if (this.options.enableLogging) {
      try {
        await fs.mkdir(path.dirname(this.options.logPath), { recursive: true });
        await fs.appendFile(this.options.logPath, logEntry + '\n');
      } catch (error) {
        console.error('Failed to write to log file:', error.message);
      }
    }
  }
}

module.exports = DatabaseMonitor;
