/**
 * StreamDB Database Diagnostic API Routes
 * Provides REST API endpoints for database diagnostic and monitoring functionality
 * 
 * Endpoints:
 * - GET /api/database/health - Quick health check
 * - GET /api/database/diagnostic - Run comprehensive diagnostic
 * - GET /api/database/schema - Validate database schema
 * - GET /api/database/performance - Check performance metrics
 * - GET /api/database/monitor/status - Get monitoring service status
 * - POST /api/database/monitor/start - Start monitoring service
 * - POST /api/database/monitor/stop - Stop monitoring service
 * 
 * Security: Admin authentication required for all endpoints
 */

const express = require('express');
const router = express.Router();
const DatabaseDiagnostic = require('../services/database-diagnostic');
const DatabaseMonitor = require('../services/database-monitor');
const { authenticateAdmin } = require('../middleware/auth');

// Global monitor instance
let monitorInstance = null;

/**
 * Quick database health check
 * GET /api/database/health
 */
router.get('/health', async (req, res) => {
  try {
    const diagnostic = new DatabaseDiagnostic();
    const startTime = Date.now();
    const isHealthy = await diagnostic.testDatabaseConnection();
    const responseTime = Date.now() - startTime;
    
    res.json({
      success: true,
      healthy: isHealthy,
      responseTime,
      timestamp: new Date().toISOString(),
      database: process.env.DB_NAME,
      connection: process.env.DB_SOCKET ? 'socket' : 'tcp'
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      healthy: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Run comprehensive database diagnostic
 * GET /api/database/diagnostic
 */
router.get('/diagnostic', authenticateAdmin, async (req, res) => {
  try {
    const diagnostic = new DatabaseDiagnostic();
    const results = await diagnostic.runComprehensiveDiagnostic();
    
    res.json({
      success: true,
      results,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Validate database schema only
 * GET /api/database/schema
 */
router.get('/schema', authenticateAdmin, async (req, res) => {
  try {
    const diagnostic = new DatabaseDiagnostic();
    
    // Test connection first
    const connected = await diagnostic.testDatabaseConnection();
    if (!connected) {
      return res.status(503).json({
        success: false,
        error: 'Cannot validate schema - database connection failed',
        timestamp: new Date().toISOString()
      });
    }
    
    const schemaValid = await diagnostic.validateDatabaseSchema();
    
    res.json({
      success: true,
      schemaValid,
      results: diagnostic.diagnosticResults,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Check database performance metrics
 * GET /api/database/performance
 */
router.get('/performance', authenticateAdmin, async (req, res) => {
  try {
    const diagnostic = new DatabaseDiagnostic();
    
    // Test connection first
    const connected = await diagnostic.testDatabaseConnection();
    if (!connected) {
      return res.status(503).json({
        success: false,
        error: 'Cannot check performance - database connection failed',
        timestamp: new Date().toISOString()
      });
    }
    
    const performanceHealthy = await diagnostic.checkDatabasePerformance();
    const poolHealthy = await diagnostic.monitorConnectionPool();
    
    res.json({
      success: true,
      performanceHealthy,
      poolHealthy,
      results: diagnostic.diagnosticResults,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get monitoring service status
 * GET /api/database/monitor/status
 */
router.get('/monitor/status', authenticateAdmin, (req, res) => {
  try {
    if (!monitorInstance) {
      return res.json({
        success: true,
        isRunning: false,
        message: 'Monitoring service not initialized',
        timestamp: new Date().toISOString()
      });
    }
    
    const status = monitorInstance.getStatus();
    
    res.json({
      success: true,
      ...status,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Start monitoring service
 * POST /api/database/monitor/start
 */
router.post('/monitor/start', authenticateAdmin, async (req, res) => {
  try {
    const options = req.body.options || {};
    
    if (monitorInstance && monitorInstance.isRunning) {
      return res.json({
        success: true,
        message: 'Monitoring service is already running',
        status: monitorInstance.getStatus(),
        timestamp: new Date().toISOString()
      });
    }
    
    // Create new monitor instance
    monitorInstance = new DatabaseMonitor(options);
    
    // Set up event handlers for logging
    monitorInstance.on('alert', (alert) => {
      console.log(`🚨 Database Alert: ${alert.type} - ${alert.message}`);
    });
    
    monitorInstance.on('recovery', (recovery) => {
      console.log(`✅ Database Recovery: After ${recovery.previousFailures} failures`);
    });
    
    monitorInstance.on('error', (error) => {
      console.error(`❌ Database Monitor Error: ${error.message}`);
    });
    
    await monitorInstance.start();
    
    res.json({
      success: true,
      message: 'Database monitoring service started successfully',
      status: monitorInstance.getStatus(),
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Stop monitoring service
 * POST /api/database/monitor/stop
 */
router.post('/monitor/stop', authenticateAdmin, async (req, res) => {
  try {
    if (!monitorInstance || !monitorInstance.isRunning) {
      return res.json({
        success: true,
        message: 'Monitoring service is not running',
        timestamp: new Date().toISOString()
      });
    }
    
    const finalStatus = monitorInstance.getStatus();
    await monitorInstance.stop();
    
    res.json({
      success: true,
      message: 'Database monitoring service stopped successfully',
      finalStatus,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get diagnostic recommendations
 * GET /api/database/recommendations
 */
router.get('/recommendations', authenticateAdmin, async (req, res) => {
  try {
    const diagnostic = new DatabaseDiagnostic();
    
    // Run quick diagnostic to get recommendations
    await diagnostic.testDatabaseConnection();
    await diagnostic.validateDatabaseSchema();
    
    const recommendations = diagnostic.diagnosticResults.recommendations;
    
    res.json({
      success: true,
      recommendations,
      count: recommendations.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Error classification and solutions
 * POST /api/database/classify-error
 */
router.post('/classify-error', authenticateAdmin, (req, res) => {
  try {
    const { error, code, message } = req.body;
    
    if (!error && !code && !message) {
      return res.status(400).json({
        success: false,
        error: 'Error information required (error, code, or message)',
        timestamp: new Date().toISOString()
      });
    }
    
    const diagnostic = new DatabaseDiagnostic();
    const mockError = { 
      code: code || error?.code, 
      message: message || error?.message || error 
    };
    
    const solution = diagnostic.getDatabaseConnectionSolution(mockError);
    
    res.json({
      success: true,
      error: mockError,
      solution,
      category: 'database_connection',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Graceful shutdown handler
process.on('SIGTERM', async () => {
  if (monitorInstance && monitorInstance.isRunning) {
    console.log('Shutting down database monitor...');
    await monitorInstance.stop();
  }
});

process.on('SIGINT', async () => {
  if (monitorInstance && monitorInstance.isRunning) {
    console.log('Shutting down database monitor...');
    await monitorInstance.stop();
  }
});

module.exports = router;
