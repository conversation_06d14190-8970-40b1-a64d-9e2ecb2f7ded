-- Migration: Add Dynamic Content Sections
-- This migration adds the dynamic content sections functionality
-- Run this after the main schema is created

-- Check if content_sections table exists, if not create it
CREATE TABLE IF NOT EXISTS content_sections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50), -- Icon name for UI
    color VARCHAR(20), -- Theme color for section
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    show_in_navigation BOOLEAN DEFAULT TRUE,
    show_on_homepage BOOLEAN DEFAULT TRUE,
    max_items_homepage INT DEFAULT 20,
    content_types JSON, -- ['movie', 'series'] or specific types
    filter_rules JSON, -- Custom filtering rules for this section
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_slug (slug),
    INDEX idx_display_order (display_order),
    INDEX idx_homepage (show_on_homepage)
);

-- Check if section_categories table exists, if not create it
CREATE TABLE IF NOT EXISTS section_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_id INT NOT NULL,
    category_id INT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (section_id) REFERENCES content_sections(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_section_category (section_id, category_id),
    INDEX idx_section (section_id),
    INDEX idx_category (category_id)
);

-- Check if section_content_types table exists, if not create it
CREATE TABLE IF NOT EXISTS section_content_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_id INT NOT NULL,
    type_name VARCHAR(50) NOT NULL,
    type_label VARCHAR(100) NOT NULL,
    description TEXT,
    fields_config JSON, -- Custom field configurations for this content type
    validation_rules JSON, -- Validation rules for this content type
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (section_id) REFERENCES content_sections(id) ON DELETE CASCADE,
    UNIQUE KEY unique_section_type (section_id, type_name),
    INDEX idx_section (section_id),
    INDEX idx_active (is_active)
);

-- Add section_id column to content table if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'content'
    AND COLUMN_NAME = 'section_id'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE content ADD COLUMN section_id INT AFTER category',
    'SELECT "section_id column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraint for section_id if it doesn't exist
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'content'
    AND COLUMN_NAME = 'section_id'
    AND REFERENCED_TABLE_NAME = 'content_sections'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE content ADD FOREIGN KEY (section_id) REFERENCES content_sections(id) ON DELETE SET NULL',
    'SELECT "section_id foreign key already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for section_id if it doesn't exist
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'content'
    AND INDEX_NAME = 'idx_section'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE content ADD INDEX idx_section (section_id)',
    'SELECT "idx_section index already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Insert default content sections if they don't exist
INSERT IGNORE INTO content_sections (name, slug, description, icon, color, display_order, show_in_navigation, show_on_homepage, max_items_homepage, content_types, filter_rules) VALUES
('Movies', 'movies', 'Latest and popular movies collection', 'Film', '#e11d48', 1, TRUE, TRUE, 20, '["movie"]', '{"type": "movie"}'),
('Web Series', 'web-series', 'Trending web series and TV shows', 'Tv', '#3b82f6', 2, TRUE, TRUE, 20, '["series"]', '{"type": "series"}'),
('Requested', 'requested', 'User requested content', 'Clock', '#f59e0b', 3, TRUE, FALSE, 10, '["requested"]', '{"type": "requested"}'),
('Featured', 'featured', 'Featured and highlighted content', 'Star', '#10b981', 4, FALSE, TRUE, 15, '["movie", "series"]', '{"is_featured": true}'),
('Latest Releases', 'latest', 'Recently added content', 'Calendar', '#8b5cf6', 5, TRUE, TRUE, 12, '["movie", "series"]', '{"order_by": "created_at", "order": "desc"}');

-- Insert default categories if they don't exist
INSERT IGNORE INTO categories (name, type, slug, description) VALUES
('English Movies', 'movie', 'english-movies', 'English language movies'),
('Hindi Movies', 'movie', 'hindi-movies', 'Hindi language movies'),
('Tamil Movies', 'movie', 'tamil-movies', 'Tamil language movies'),
('Telugu Movies', 'movie', 'telugu-movies', 'Telugu language movies'),
('Malayalam Movies', 'movie', 'malayalam-movies', 'Malayalam language movies'),
('Kannada Movies', 'movie', 'kannada-movies', 'Kannada language movies'),
('English Series', 'series', 'english-series', 'English language web series'),
('Hindi Series', 'series', 'hindi-series', 'Hindi language web series'),
('Korean Series', 'series', 'korean-series', 'Korean language web series'),
('Anime Series', 'series', 'anime-series', 'Anime and animated series'),
('Documentary', 'both', 'documentary', 'Documentary content'),
('Kids Content', 'both', 'kids', 'Family-friendly content for children');

-- Link sections to categories (many-to-many relationships)
INSERT IGNORE INTO section_categories (section_id, category_id, is_default) 
SELECT 
    s.id as section_id,
    c.id as category_id,
    CASE 
        WHEN s.slug = 'movies' AND c.type IN ('movie', 'both') THEN TRUE
        WHEN s.slug = 'web-series' AND c.type IN ('series', 'both') THEN TRUE
        ELSE FALSE
    END as is_default
FROM content_sections s
CROSS JOIN categories c
WHERE 
    (s.slug = 'movies' AND c.type IN ('movie', 'both')) OR
    (s.slug = 'web-series' AND c.type IN ('series', 'both')) OR
    (s.slug = 'featured' AND c.type IN ('movie', 'series', 'both')) OR
    (s.slug = 'latest' AND c.type IN ('movie', 'series', 'both'));

-- Update existing content to assign to appropriate sections based on type
UPDATE content c
SET section_id = (
    SELECT s.id 
    FROM content_sections s 
    WHERE s.slug = CASE 
        WHEN c.type = 'movie' THEN 'movies'
        WHEN c.type = 'series' THEN 'web-series'
        WHEN c.type = 'requested' THEN 'requested'
        ELSE 'movies'
    END
    LIMIT 1
)
WHERE c.section_id IS NULL;

-- Create a view for easy section content querying
CREATE OR REPLACE VIEW section_content_view AS
SELECT 
    s.id as section_id,
    s.name as section_name,
    s.slug as section_slug,
    s.color as section_color,
    s.icon as section_icon,
    s.max_items_homepage,
    c.*,
    cat.name as category_name
FROM content_sections s
LEFT JOIN content c ON s.id = c.section_id
LEFT JOIN categories cat ON c.category = cat.slug
WHERE s.is_active = TRUE
ORDER BY s.display_order, c.created_at DESC;

-- Success message
SELECT 'Dynamic content sections migration completed successfully!' as message;
