/**
 * IFrame Configuration Tests
 * Tests iframe security, configuration, and compatibility
 */

import { 
  getIFrameConfig, 
  getIFrameConfigDescription, 
  detectVideoPlatform 
} from '../utils/videoSecurity';
import { validateIframeSrc, getSecureIframeAttributes } from '../utils/securityHeaders';

// Test URLs for different platforms
const testPlatforms = [
  {
    name: 'YouTube',
    url: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    expectedPlatform: 'youtube'
  },
  {
    name: 'Vimeo',
    url: 'https://player.vimeo.com/video/123456789',
    expectedPlatform: 'vimeo'
  },
  {
    name: 'Dailymotion',
    url: 'https://www.dailymotion.com/embed/video/x7tgad0',
    expectedPlatform: 'dailymotion'
  },
  {
    name: 'Twitch',
    url: 'https://player.twitch.tv/video/123456789',
    expectedPlatform: 'twitch'
  },
  {
    name: '2embed',
    url: 'https://2embed.cc/embed/movie/123456',
    expectedPlatform: '2embed'
  },
  {
    name: 'Streamable',
    url: 'https://streamable.com/e/abc123',
    expectedPlatform: 'streamable'
  },
  {
    name: 'FileMoon',
    url: 'https://filemoon.to/e/abc123',
    expectedPlatform: 'filemoon'
  },
  {
    name: 'StreamTape',
    url: 'https://streamtape.com/e/abc123',
    expectedPlatform: 'streamtape'
  }
];

/**
 * Test iframe configuration generation
 */
export function testIFrameConfigGeneration(): boolean {
  console.group('⚙️ Testing IFrame Configuration Generation');
  
  let allPassed = true;
  
  testPlatforms.forEach(platform => {
    console.log(`\nTesting ${platform.name}:`);
    console.log(`URL: ${platform.url}`);
    
    const config = getIFrameConfig(platform.url);
    const description = getIFrameConfigDescription(platform.url);
    
    console.log('Config:', config);
    console.log('Description:', description);
    
    // Verify config has required properties
    if (!config || !config.src) {
      console.error(`❌ Missing src in config for ${platform.name}`);
      allPassed = false;
    } else {
      console.log(`✅ Valid config for ${platform.name}`);
    }
    
    // Verify description is provided
    if (!description) {
      console.error(`❌ Missing description for ${platform.name}`);
      allPassed = false;
    } else {
      console.log(`✅ Valid description for ${platform.name}`);
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test iframe security attributes
 */
export function testIFrameSecurityAttributes(): boolean {
  console.group('🔒 Testing IFrame Security Attributes');
  
  let allPassed = true;
  
  testPlatforms.forEach(platform => {
    console.log(`\nTesting security for ${platform.name}:`);
    
    const config = getIFrameConfig(platform.url);
    const secureAttrs = getSecureIframeAttributes(platform.url);
    
    console.log('Security attributes:', secureAttrs);
    
    // Check for essential security attributes
    const requiredAttrs = ['referrerPolicy', 'loading'];
    
    requiredAttrs.forEach(attr => {
      if (!secureAttrs.hasOwnProperty(attr)) {
        console.error(`❌ Missing security attribute: ${attr}`);
        allPassed = false;
      } else {
        console.log(`✅ Has ${attr}: ${secureAttrs[attr]}`);
      }
    });
    
    // Verify no sandbox restrictions (as per requirements)
    if (secureAttrs.sandbox) {
      console.error(`❌ Sandbox restrictions found (should be removed): ${secureAttrs.sandbox}`);
      allPassed = false;
    } else {
      console.log(`✅ No sandbox restrictions (correct)`);
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test iframe source validation
 */
export function testIFrameSourceValidation(): boolean {
  console.group('🔍 Testing IFrame Source Validation');
  
  let allPassed = true;
  
  testPlatforms.forEach(platform => {
    console.log(`\nValidating ${platform.name} source:`);
    
    const isValid = validateIframeSrc(platform.url);
    console.log(`URL: ${platform.url}`);
    console.log(`Valid: ${isValid ? '✅' : '❌'}`);
    
    if (!isValid) {
      console.error(`❌ Source validation failed for ${platform.name}`);
      allPassed = false;
    }
  });
  
  // Test invalid sources
  const invalidSources = [
    'javascript:alert("xss")',
    'data:text/html,<script>alert("xss")</script>',
    'http://malicious-site.com/embed',
    ''
  ];
  
  console.log('\nTesting invalid sources:');
  invalidSources.forEach(source => {
    const isValid = validateIframeSrc(source);
    console.log(`${source}: ${isValid ? '❌ Should be invalid' : '✅ Correctly invalid'}`);
    
    if (isValid) {
      console.error(`❌ Invalid source was accepted: ${source}`);
      allPassed = false;
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test platform detection accuracy
 */
export function testPlatformDetectionAccuracy(): boolean {
  console.group('🎯 Testing Platform Detection Accuracy');
  
  let allPassed = true;
  
  testPlatforms.forEach(platform => {
    const detectedPlatform = detectVideoPlatform(platform.url);
    const isCorrect = detectedPlatform === platform.expectedPlatform;
    
    console.log(`${platform.name}:`);
    console.log(`  Expected: ${platform.expectedPlatform}`);
    console.log(`  Detected: ${detectedPlatform}`);
    console.log(`  Result: ${isCorrect ? '✅' : '❌'}`);
    
    if (!isCorrect) {
      allPassed = false;
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test iframe responsiveness configuration
 */
export function testIFrameResponsiveness(): boolean {
  console.group('📱 Testing IFrame Responsiveness');
  
  let allPassed = true;
  
  testPlatforms.forEach(platform => {
    const config = getIFrameConfig(platform.url);
    
    console.log(`${platform.name} responsiveness:`);
    
    // Check for responsive attributes
    const hasResponsiveClass = config.className && config.className.includes('w-full');
    const hasResponsiveStyle = config.style && (config.style.width === '100%' || config.style.maxWidth);
    
    console.log(`  Responsive class: ${hasResponsiveClass ? '✅' : '❌'}`);
    console.log(`  Responsive style: ${hasResponsiveStyle ? '✅' : '❌'}`);
    
    if (!hasResponsiveClass && !hasResponsiveStyle) {
      console.error(`❌ No responsive configuration for ${platform.name}`);
      allPassed = false;
    }
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Run all iframe configuration tests
 */
export function runIFrameConfigTests(): boolean {
  console.log('🎯 Starting IFrame Configuration Tests');
  console.log('======================================');
  
  const results = [
    testIFrameConfigGeneration(),
    testIFrameSecurityAttributes(),
    testIFrameSourceValidation(),
    testPlatformDetectionAccuracy(),
    testIFrameResponsiveness()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('======================================');
  if (allPassed) {
    console.log('✅ All iframe configuration tests passed!');
  } else {
    console.log('❌ Some iframe configuration tests failed!');
  }
  
  return allPassed;
}

// Export for use in other test files
export default {
  testIFrameConfigGeneration,
  testIFrameSecurityAttributes,
  testIFrameSourceValidation,
  testPlatformDetectionAccuracy,
  testIFrameResponsiveness,
  runIFrameConfigTests
};
