const mysql = require('mysql2/promise');

// Database connection configuration
const dbConfig = {
  socketPath: process.env.DB_SOCKET,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4'
};

/**
 * Log admin actions to the security_logs table
 * @param {string} userId - Admin user ID
 * @param {string} action - Action performed
 * @param {string} details - Additional details about the action
 * @param {string} ipAddress - IP address of the admin
 * @param {string} userAgent - User agent string
 */
async function logAdminAction(userId, action, details = null, ipAddress = null, userAgent = null) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const query = `
      INSERT INTO admin_security_logs (
        user_id,
        action,
        details,
        ip_address,
        user_agent,
        created_at
      ) VALUES (?, ?, ?, ?, ?, NOW())
    `;
    
    await connection.execute(query, [
      userId,
      action,
      details,
      ipAddress,
      userAgent
    ]);
    
    console.log(`Admin action logged: ${action} by user ${userId}`);
    
  } catch (error) {
    console.error('Error logging admin action:', error);
    // Don't throw error to prevent breaking the main functionality
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Express middleware to automatically log admin actions
 * @param {string} action - The action being performed
 * @returns {Function} Express middleware function
 */
function adminActionLogger(action) {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id || req.session?.userId || 'unknown';
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');
      const details = JSON.stringify({
        method: req.method,
        url: req.originalUrl,
        body: req.method === 'POST' ? req.body : undefined,
        params: req.params,
        query: req.query
      });
      
      // Log the action asynchronously
      setImmediate(() => {
        logAdminAction(userId, action, details, ipAddress, userAgent);
      });
      
    } catch (error) {
      console.error('Error in admin action logger middleware:', error);
    }
    
    next();
  };
}

/**
 * Get admin action logs with pagination
 * @param {number} page - Page number (default: 1)
 * @param {number} limit - Items per page (default: 50)
 * @param {string} userId - Filter by user ID (optional)
 * @returns {Object} Logs and pagination info
 */
async function getAdminLogs(page = 1, limit = 50, userId = null) {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const offset = (page - 1) * limit;
    let whereClause = '';
    let params = [];
    
    if (userId) {
      whereClause = 'WHERE user_id = ?';
      params.push(userId);
    }
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM admin_security_logs ${whereClause}`;
    const [countResult] = await connection.execute(countQuery, params);
    const total = countResult[0].total;

    // Get logs
    const logsQuery = `
      SELECT
        id,
        user_id,
        action,
        details,
        ip_address,
        user_agent,
        created_at
      FROM admin_security_logs
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const [logs] = await connection.execute(logsQuery, [...params, limit, offset]);
    
    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    };
    
  } catch (error) {
    console.error('Error getting admin logs:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

module.exports = {
  logAdminAction,
  adminActionLogger,
  getAdminLogs
};
