/**
 * Server-side TMDB API Service
 * 
 * Comprehensive service for interacting with The Movie Database API from the backend
 * Handles movies, TV series, cast, crew, and image fetching
 */

const axios = require('axios');
require('dotenv').config();

// TMDB API Configuration
const TMDB_API_KEY = process.env.VITE_TMDB_API_KEY || '';
const TMDB_BASE_URL = process.env.VITE_TMDB_BASE_URL || 'https://api.themoviedb.org/3';
const TMDB_IMAGE_BASE_URL = process.env.VITE_TMDB_IMAGE_BASE_URL || 'https://image.tmdb.org/t/p';

// Rate limiting configuration
const RATE_LIMIT_DELAY = 250; // 4 requests per second (TMDB limit is 40/10s)
let lastRequestTime = 0;

// Image size configurations
const IMAGE_SIZES = {
  poster: {
    small: 'w185',
    medium: 'w342',
    large: 'w500',
    original: 'original'
  },
  backdrop: {
    small: 'w300',
    medium: 'w780',
    large: 'w1280',
    original: 'original'
  },
  profile: {
    small: 'w45',
    medium: 'w185',
    large: 'h632',
    original: 'original'
  }
};

// Custom error class for TMDB API errors
class TMDBError extends Error {
  constructor(message, statusCode = null) {
    super(message);
    this.name = 'TMDBError';
    this.statusCode = statusCode;
  }
}

// Rate limiting function
async function rateLimitedRequest() {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  lastRequestTime = Date.now();
}

// Generic API request function with error handling
async function tmdbRequest(endpoint, params = {}) {
  if (!TMDB_API_KEY) {
    throw new TMDBError('TMDB API key is not configured');
  }

  await rateLimitedRequest();

  const url = `${TMDB_BASE_URL}${endpoint}`;
  const requestParams = {
    api_key: TMDB_API_KEY,
    ...params
  };

  try {
    const response = await axios.get(url, { params: requestParams });
    return response.data;
  } catch (error) {
    if (error.response) {
      const statusCode = error.response.status;
      const message = error.response.data?.status_message || `HTTP ${statusCode} error`;
      throw new TMDBError(message, statusCode);
    } else if (error.request) {
      throw new TMDBError('Network error: Unable to reach TMDB API');
    } else {
      throw new TMDBError(`Request error: ${error.message}`);
    }
  }
}

// Image URL builder
function buildImageUrl(path, type = 'poster', size = 'medium') {
  if (!path) return null;
  
  const sizeMap = IMAGE_SIZES[type];
  const selectedSize = sizeMap[size] || sizeMap.medium;
  
  return `${TMDB_IMAGE_BASE_URL}/${selectedSize}${path}`;
}

// Get movie details by TMDB ID
async function getMovieDetails(tmdbId) {
  return tmdbRequest(`/movie/${tmdbId}`, {
    append_to_response: 'credits,videos,images'
  });
}

// Get TV show details by TMDB ID
async function getTVShowDetails(tmdbId) {
  return tmdbRequest(`/tv/${tmdbId}`, {
    append_to_response: 'credits,videos,images,seasons'
  });
}

// Get movie or TV show details (auto-detect)
async function getContentDetails(tmdbId, contentType) {
  if (contentType === 'movie') {
    return getMovieDetails(tmdbId);
  } else if (contentType === 'tv') {
    return getTVShowDetails(tmdbId);
  }
  
  // Try movie first, then TV show
  try {
    return await getMovieDetails(tmdbId);
  } catch (error) {
    if (error instanceof TMDBError && error.statusCode === 404) {
      return await getTVShowDetails(tmdbId);
    }
    throw error;
  }
}

// Search for content
async function searchContent(query, type) {
  const endpoint = type ? `/search/${type}` : '/search/multi';
  return tmdbRequest(endpoint, { query });
}

// Data transformation utilities
function transformTMDBToMediaItem(tmdbData) {
  const isMovie = 'title' in tmdbData;

  // Extract basic information
  const title = isMovie ? tmdbData.title : tmdbData.name;
  const description = tmdbData.overview || '';
  const releaseDate = isMovie ? tmdbData.release_date : tmdbData.first_air_date;
  const year = releaseDate ? new Date(releaseDate).getFullYear().toString() : '';

  // Extract genres
  const genres = tmdbData.genres?.map(g => g.name) || [];

  // Build image URLs
  const posterUrl = buildImageUrl(tmdbData.poster_path, 'poster', 'large') || '';
  const thumbnailUrl = buildImageUrl(tmdbData.poster_path, 'poster', 'medium') || '';
  const coverImage = buildImageUrl(tmdbData.backdrop_path, 'backdrop', 'original') || '';

  // Extract rating and runtime
  const imdbRating = tmdbData.vote_average ? tmdbData.vote_average.toFixed(1) : '';
  let runtime = '';
  if (isMovie && tmdbData.runtime) {
    runtime = tmdbData.runtime.toString();
  } else if (!isMovie && tmdbData.episode_run_time?.length) {
    runtime = tmdbData.episode_run_time[0].toString();
  }

  // Extract studio/production companies
  const studio = tmdbData.production_companies?.[0]?.name || '';

  // Extract languages
  const languages = tmdbData.spoken_languages?.map(lang => lang.english_name) || [];

  // Extract trailer
  const youtubeTrailer = tmdbData.videos?.results.find(
    video => video.site === 'YouTube' && video.type === 'Trailer'
  );
  const trailer = youtubeTrailer ? `https://www.youtube.com/watch?v=${youtubeTrailer.key}` : '';

  // Extract cast and crew information
  const cast = tmdbData.credits?.cast.slice(0, 10).map(member => member.name) || [];
  const crew = tmdbData.credits?.crew.slice(0, 5).map(member => `${member.name} (${member.job})`) || [];

  // Extract specific crew roles
  const director = tmdbData.credits?.crew.find(member => member.job === 'Director')?.name || '';
  const writers = tmdbData.credits?.crew
    .filter(member => ['Writer', 'Screenplay', 'Story'].includes(member.job))
    .map(member => member.name) || [];
  const producers = tmdbData.credits?.crew
    .filter(member => member.job.includes('Producer'))
    .map(member => member.name) || [];

  return {
    title,
    description,
    year,
    genres,
    posterUrl,
    thumbnailUrl,
    coverImage,
    imdbRating,
    runtime,
    studio,
    languages,
    trailer,
    cast,
    crew,
    director,
    writers,
    producers
  };
}

// Get comprehensive content data with all metadata
async function getComprehensiveContentData(tmdbId, contentType) {
  try {
    const tmdbData = await getContentDetails(tmdbId, contentType);
    const transformedData = transformTMDBToMediaItem(tmdbData);

    return {
      success: true,
      data: transformedData,
      rawData: tmdbData,
      contentType: 'title' in tmdbData ? 'movie' : 'series'
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof TMDBError ? error.message : 'Unknown error occurred',
      statusCode: error instanceof TMDBError ? error.statusCode : undefined
    };
  }
}

// Validate TMDB ID format
function isValidTMDBId(id) {
  return /^\d+$/.test(id.toString().trim()) && parseInt(id) > 0;
}

module.exports = {
  TMDBError,
  buildImageUrl,
  getMovieDetails,
  getTVShowDetails,
  getContentDetails,
  searchContent,
  transformTMDBToMediaItem,
  getComprehensiveContentData,
  isValidTMDBId,
  IMAGE_SIZES
};
