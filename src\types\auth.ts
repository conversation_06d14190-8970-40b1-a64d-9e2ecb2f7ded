/**
 * Authentication Types and Interfaces
 * Defines all TypeScript interfaces for the authentication system
 */

// User authentication credentials
export interface LoginCredentials {
  username: string;
  password: string;
}

// User information after successful authentication
export interface AuthUser {
  id: string;
  username: string;
  role: 'admin' | 'moderator';
  lastLogin?: string;
  permissions: string[];
}

// Authentication state
export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  isLoading: boolean;
  error: string | null;
  sessionExpiry: number | null;
}

// Login response from authentication service
export interface LoginResponse {
  success: boolean;
  user?: AuthUser;
  token?: string;
  expiresIn?: number;
  error?: string;
}

// Session data stored in secure storage
export interface SessionData {
  user: AuthUser;
  token: string;
  expiresAt: number;
  createdAt: number;
}

// Authentication configuration
export interface AuthConfig {
  sessionTimeout: number; // in milliseconds
  tokenRefreshThreshold: number; // in milliseconds before expiry
  secureStorage: boolean;
}

// Login attempt tracking for security
export interface LoginAttempt {
  timestamp: number;
  success: boolean;
  ip?: string;
  userAgent?: string;
}

// Security settings
export interface SecuritySettings {
  enableBruteForceProtection: boolean;
  enableSessionTimeout: boolean;
  enableSecureHeaders: boolean;
  enableCSRFProtection: boolean;
  logSecurityEvents: boolean;
}

// Authentication context type
export interface AuthContextType {
  // State
  authState: AuthState;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<LoginResponse>;
  logout: () => void;
  refreshSession: () => Promise<boolean>;
  checkAuthStatus: () => boolean;
  
  // Utilities
  hasPermission: (permission: string) => boolean;
  isSessionValid: () => boolean;
  getTimeUntilExpiry: () => number;
}

// Route protection configuration
export interface ProtectedRouteConfig {
  requireAuth: boolean;
  requiredPermissions?: string[];
  redirectTo?: string;
  allowedRoles?: ('admin' | 'moderator')[];
}

// Authentication error types
export type AuthError = 
  | 'INVALID_CREDENTIALS'
  | 'SESSION_EXPIRED'
  | 'ACCOUNT_LOCKED'
  | 'TOO_MANY_ATTEMPTS'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

// Authentication event types for logging
export type AuthEvent = 
  | 'LOGIN_SUCCESS'
  | 'LOGIN_FAILED'
  | 'LOGOUT'
  | 'SESSION_EXPIRED'
  | 'SESSION_REFRESHED'
  | 'ACCOUNT_LOCKED'
  | 'SECURITY_VIOLATION';

// Authentication event log entry
export interface AuthEventLog {
  event: AuthEvent;
  timestamp: number;
  userId?: string;
  details?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Default authentication configuration
export const DEFAULT_AUTH_CONFIG: AuthConfig = {
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes
  secureStorage: true,
};

// Default security settings
export const DEFAULT_SECURITY_SETTINGS: SecuritySettings = {
  enableBruteForceProtection: true,
  enableSessionTimeout: true,
  enableSecureHeaders: true,
  enableCSRFProtection: true,
  logSecurityEvents: true,
};

// Permission constants
export const PERMISSIONS = {
  ADMIN_PANEL_ACCESS: 'admin_panel_access',
  CONTENT_CREATE: 'content_create',
  CONTENT_EDIT: 'content_edit',
  CONTENT_DELETE: 'content_delete',
  USER_MANAGEMENT: 'user_management',
  SYSTEM_SETTINGS: 'system_settings',
  BULK_OPERATIONS: 'bulk_operations',
  EXPORT_DATA: 'export_data',
} as const;

// Type for permission values
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];
