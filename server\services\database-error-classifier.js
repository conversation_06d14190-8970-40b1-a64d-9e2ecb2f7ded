/**
 * StreamDB Database Error Classification System
 * Advanced error detection, classification, and solution provider for MySQL database issues
 * 
 * Features:
 * - Comprehensive error code mapping
 * - Context-aware solution recommendations
 * - Performance issue detection
 * - Security vulnerability identification
 * - Automated fix suggestions
 */

class DatabaseErrorClassifier {
  constructor() {
    this.errorCategories = {
      CONNECTION: 'connection',
      AUTHENTICATION: 'authentication',
      PERMISSION: 'permission',
      SYNTAX: 'syntax',
      CONSTRAINT: 'constraint',
      PERFORMANCE: 'performance',
      RESOURCE: 'resource',
      NETWORK: 'network',
      CONFIGURATION: 'configuration',
      CORRUPTION: 'corruption'
    };

    this.errorDatabase = {
      // Connection Errors
      'ECONNREFUSED': {
        category: this.errorCategories.CONNECTION,
        severity: 'CRITICAL',
        title: 'MySQL Server Connection Refused',
        description: 'Cannot connect to MySQL server - server may be down or not accepting connections',
        solutions: [
          'Check if MySQL service is running: sudo systemctl status mysql',
          'Start MySQL service: sudo systemctl start mysql',
          'Verify MySQL is listening on correct port: netstat -tlnp | grep :3306',
          'Check MySQL configuration file: /etc/mysql/mysql.conf.d/mysqld.cnf',
          'Review MySQL error logs: sudo tail -f /var/log/mysql/error.log'
        ],
        preventive_measures: [
          'Set up MySQL service monitoring',
          'Configure automatic MySQL restart on failure',
          'Monitor server resource usage'
        ]
      },

      'ENOTFOUND': {
        category: this.errorCategories.NETWORK,
        severity: 'HIGH',
        title: 'Database Host Not Found',
        description: 'Cannot resolve database hostname - DNS or network issue',
        solutions: [
          'Verify DB_HOST in .env file is correct',
          'Test DNS resolution: nslookup [hostname]',
          'Try using IP address instead of hostname',
          'Check network connectivity: ping [hostname]',
          'Verify firewall settings are not blocking connection'
        ],
        preventive_measures: [
          'Use IP addresses for local connections',
          'Set up local DNS caching',
          'Monitor network connectivity'
        ]
      },

      'ER_ACCESS_DENIED_ERROR': {
        category: this.errorCategories.AUTHENTICATION,
        severity: 'HIGH',
        title: 'MySQL Access Denied',
        description: 'Invalid username or password for database connection',
        solutions: [
          'Verify DB_USER and DB_PASSWORD in .env file',
          'Check user exists: SELECT User, Host FROM mysql.user WHERE User="[username]";',
          'Reset user password: ALTER USER "[username]"@"[host]" IDENTIFIED BY "[new_password]";',
          'Grant necessary privileges: GRANT ALL PRIVILEGES ON [database].* TO "[username]"@"[host]";',
          'Flush privileges: FLUSH PRIVILEGES;'
        ],
        preventive_measures: [
          'Use strong, unique database passwords',
          'Regularly rotate database credentials',
          'Implement principle of least privilege'
        ]
      },

      'ER_BAD_DB_ERROR': {
        category: this.errorCategories.CONFIGURATION,
        severity: 'HIGH',
        title: 'Database Does Not Exist',
        description: 'The specified database name does not exist on the MySQL server',
        solutions: [
          'Verify DB_NAME in .env file matches existing database',
          'Create database: CREATE DATABASE [database_name] CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;',
          'List existing databases: SHOW DATABASES;',
          'Import database schema if needed',
          'Check database name spelling and case sensitivity'
        ],
        preventive_measures: [
          'Document database setup procedures',
          'Use database migration scripts',
          'Implement database backup and restore procedures'
        ]
      },

      'ENOENT': {
        category: this.errorCategories.CONFIGURATION,
        severity: 'HIGH',
        title: 'MySQL Socket File Not Found',
        description: 'Cannot find MySQL socket file - MySQL may not be running or socket path is incorrect',
        solutions: [
          'Check if MySQL is running: sudo systemctl status mysql',
          'Verify socket path: sudo find /var -name "mysqld.sock" 2>/dev/null',
          'Check MySQL configuration: grep socket /etc/mysql/mysql.conf.d/mysqld.cnf',
          'Update DB_SOCKET in .env file with correct path',
          'Restart MySQL service: sudo systemctl restart mysql'
        ],
        preventive_measures: [
          'Monitor MySQL socket file availability',
          'Use TCP connection as fallback',
          'Document correct socket paths'
        ]
      },

      'ETIMEDOUT': {
        category: this.errorCategories.NETWORK,
        severity: 'MEDIUM',
        title: 'Database Connection Timeout',
        description: 'Connection to database server timed out - network or server performance issue',
        solutions: [
          'Increase connectTimeout in database configuration',
          'Check network latency: ping [database_host]',
          'Monitor database server load',
          'Optimize slow queries causing server overload',
          'Check for network congestion or packet loss'
        ],
        preventive_measures: [
          'Set appropriate timeout values',
          'Monitor database performance metrics',
          'Implement connection pooling'
        ]
      },

      'ER_TOO_MANY_CONNECTIONS': {
        category: this.errorCategories.RESOURCE,
        severity: 'HIGH',
        title: 'Too Many Database Connections',
        description: 'MySQL server has reached maximum connection limit',
        solutions: [
          'Increase max_connections: SET GLOBAL max_connections = [higher_value];',
          'Optimize connection pooling in application',
          'Close unused database connections',
          'Monitor and kill long-running connections: SHOW PROCESSLIST;',
          'Review application connection management'
        ],
        preventive_measures: [
          'Implement proper connection pooling',
          'Monitor connection usage',
          'Set connection timeouts'
        ]
      },

      'ER_LOCK_WAIT_TIMEOUT': {
        category: this.errorCategories.PERFORMANCE,
        severity: 'MEDIUM',
        title: 'Lock Wait Timeout',
        description: 'Transaction waited too long for a table lock',
        solutions: [
          'Increase innodb_lock_wait_timeout setting',
          'Optimize queries to reduce lock time',
          'Use smaller transactions',
          'Check for deadlocks: SHOW ENGINE INNODB STATUS;',
          'Review table locking patterns'
        ],
        preventive_measures: [
          'Design efficient database schema',
          'Use appropriate transaction isolation levels',
          'Monitor long-running transactions'
        ]
      },

      'ER_DISK_FULL': {
        category: this.errorCategories.RESOURCE,
        severity: 'CRITICAL',
        title: 'Disk Space Full',
        description: 'MySQL server has run out of disk space',
        solutions: [
          'Free up disk space immediately',
          'Clean up old log files: sudo mysql -e "PURGE BINARY LOGS BEFORE NOW() - INTERVAL 7 DAY;"',
          'Optimize tables: OPTIMIZE TABLE [table_name];',
          'Move data to larger partition',
          'Set up log rotation for MySQL logs'
        ],
        preventive_measures: [
          'Monitor disk space usage',
          'Set up automated cleanup scripts',
          'Configure log rotation'
        ]
      },

      'ER_OUT_OF_MEMORY': {
        category: this.errorCategories.RESOURCE,
        severity: 'CRITICAL',
        title: 'Out of Memory',
        description: 'MySQL server has run out of available memory',
        solutions: [
          'Increase server RAM',
          'Optimize MySQL memory settings (innodb_buffer_pool_size)',
          'Reduce query complexity',
          'Kill memory-intensive processes',
          'Review and optimize table indexes'
        ],
        preventive_measures: [
          'Monitor memory usage',
          'Optimize MySQL configuration',
          'Use query optimization techniques'
        ]
      }
    };

    this.performanceIndicators = {
      SLOW_QUERY: {
        threshold: 1000, // ms
        description: 'Query execution time exceeds threshold',
        solutions: [
          'Add appropriate indexes',
          'Optimize query structure',
          'Use EXPLAIN to analyze query execution plan',
          'Consider query caching'
        ]
      },
      HIGH_CONNECTION_USAGE: {
        threshold: 80, // percentage
        description: 'Connection pool usage is high',
        solutions: [
          'Increase connection pool size',
          'Optimize connection lifecycle',
          'Implement connection pooling',
          'Review application architecture'
        ]
      },
      LOCK_CONTENTION: {
        threshold: 5, // seconds
        description: 'High lock wait times detected',
        solutions: [
          'Optimize transaction scope',
          'Use appropriate isolation levels',
          'Redesign schema to reduce contention',
          'Implement row-level locking strategies'
        ]
      }
    };
  }

  /**
   * Classify database error and provide solutions
   */
  classifyError(error) {
    const errorCode = this.extractErrorCode(error);
    const errorInfo = this.errorDatabase[errorCode];

    if (!errorInfo) {
      return this.handleUnknownError(error);
    }

    return {
      code: errorCode,
      category: errorInfo.category,
      severity: errorInfo.severity,
      title: errorInfo.title,
      description: errorInfo.description,
      solutions: errorInfo.solutions,
      preventive_measures: errorInfo.preventive_measures,
      context: this.analyzeErrorContext(error),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Extract error code from various error formats
   */
  extractErrorCode(error) {
    if (typeof error === 'string') {
      // Try to extract code from error message
      const codeMatch = error.match(/ER_[A-Z_]+|E[A-Z]+/);
      return codeMatch ? codeMatch[0] : 'UNKNOWN';
    }

    if (error && typeof error === 'object') {
      return error.code || error.errno || error.sqlState || 'UNKNOWN';
    }

    return 'UNKNOWN';
  }

  /**
   * Analyze error context for better solutions
   */
  analyzeErrorContext(error) {
    const context = {
      environment: process.env.NODE_ENV || 'unknown',
      connection_method: process.env.DB_SOCKET ? 'socket' : 'tcp',
      database: process.env.DB_NAME || 'unknown',
      timestamp: new Date().toISOString()
    };

    // Add specific context based on error type
    if (error && error.sql) {
      context.query = error.sql;
    }

    if (error && error.sqlMessage) {
      context.sql_message = error.sqlMessage;
    }

    return context;
  }

  /**
   * Handle unknown errors
   */
  handleUnknownError(error) {
    return {
      code: 'UNKNOWN',
      category: 'unknown',
      severity: 'MEDIUM',
      title: 'Unknown Database Error',
      description: 'An unrecognized database error occurred',
      solutions: [
        'Check MySQL error logs for more details',
        'Verify database server status',
        'Review recent configuration changes',
        'Contact database administrator if issue persists'
      ],
      preventive_measures: [
        'Implement comprehensive error logging',
        'Monitor database health regularly',
        'Keep database software updated'
      ],
      context: this.analyzeErrorContext(error),
      raw_error: error,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Analyze performance metrics and detect issues
   */
  analyzePerformanceMetrics(metrics) {
    const issues = [];

    // Check query performance
    if (metrics.averageQueryTime > this.performanceIndicators.SLOW_QUERY.threshold) {
      issues.push({
        type: 'SLOW_QUERY',
        severity: 'MEDIUM',
        description: this.performanceIndicators.SLOW_QUERY.description,
        value: metrics.averageQueryTime,
        threshold: this.performanceIndicators.SLOW_QUERY.threshold,
        solutions: this.performanceIndicators.SLOW_QUERY.solutions
      });
    }

    // Check connection usage
    if (metrics.connectionUsage > this.performanceIndicators.HIGH_CONNECTION_USAGE.threshold) {
      issues.push({
        type: 'HIGH_CONNECTION_USAGE',
        severity: 'HIGH',
        description: this.performanceIndicators.HIGH_CONNECTION_USAGE.description,
        value: metrics.connectionUsage,
        threshold: this.performanceIndicators.HIGH_CONNECTION_USAGE.threshold,
        solutions: this.performanceIndicators.HIGH_CONNECTION_USAGE.solutions
      });
    }

    // Check lock contention
    if (metrics.averageLockWaitTime > this.performanceIndicators.LOCK_CONTENTION.threshold) {
      issues.push({
        type: 'LOCK_CONTENTION',
        severity: 'MEDIUM',
        description: this.performanceIndicators.LOCK_CONTENTION.description,
        value: metrics.averageLockWaitTime,
        threshold: this.performanceIndicators.LOCK_CONTENTION.threshold,
        solutions: this.performanceIndicators.LOCK_CONTENTION.solutions
      });
    }

    return {
      issues,
      summary: {
        total_issues: issues.length,
        critical: issues.filter(i => i.severity === 'CRITICAL').length,
        high: issues.filter(i => i.severity === 'HIGH').length,
        medium: issues.filter(i => i.severity === 'MEDIUM').length
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get all error categories and their descriptions
   */
  getErrorCategories() {
    return Object.entries(this.errorCategories).map(([key, value]) => ({
      key,
      value,
      description: this.getCategoryDescription(value)
    }));
  }

  getCategoryDescription(category) {
    const descriptions = {
      connection: 'Issues related to establishing database connections',
      authentication: 'Problems with user credentials and authentication',
      permission: 'Database access and privilege issues',
      syntax: 'SQL syntax and query structure errors',
      constraint: 'Database constraint violations',
      performance: 'Performance-related issues and bottlenecks',
      resource: 'Server resource limitations (memory, disk, CPU)',
      network: 'Network connectivity and communication issues',
      configuration: 'Database and server configuration problems',
      corruption: 'Data corruption and integrity issues'
    };

    return descriptions[category] || 'Unknown category';
  }
}

module.exports = DatabaseErrorClassifier;
