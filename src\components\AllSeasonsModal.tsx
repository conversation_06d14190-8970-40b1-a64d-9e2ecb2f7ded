import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Play, Calendar, Clock, ChevronDown, ChevronUp } from "lucide-react";
import { MediaItem, Season, Episode } from "@/types/media";
import apiService from "@/services/apiService";

interface AllSeasonsModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: MediaItem | null;
  onEpisodeSelect: (videoLinks: string, episodeTitle: string) => void;
}

export default function AllSeasonsModal({ 
  isOpen, 
  onClose, 
  content, 
  onEpisodeSelect 
}: AllSeasonsModalProps) {
  const [seasons, setSeasons] = useState<Season[]>([]);
  const [loading, setLoading] = useState(false);
  const [expandedSeasons, setExpandedSeasons] = useState<Set<number>>(new Set([1])); // Expand first season by default

  // Load seasons and episodes from database
  const loadSeasons = async () => {
    if (!content || content.type !== 'series') return;

    try {
      setLoading(true);
      const result = await apiService.getContentSeasons(content.id);
      
      if (result.success) {
        setSeasons(result.data || []);
        // Auto-expand first season if no seasons are expanded
        if (result.data && result.data.length > 0 && expandedSeasons.size === 0) {
          setExpandedSeasons(new Set([result.data[0].seasonNumber]));
        }
      } else {
        console.error('Failed to load seasons:', result.message);
      }
    } catch (error) {
      console.error('Error loading seasons:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load seasons when modal opens
  useEffect(() => {
    if (isOpen && content) {
      loadSeasons();
    }
  }, [isOpen, content]);

  // Toggle season expansion
  const toggleSeason = (seasonNumber: number) => {
    const newExpanded = new Set(expandedSeasons);
    if (newExpanded.has(seasonNumber)) {
      newExpanded.delete(seasonNumber);
    } else {
      newExpanded.add(seasonNumber);
    }
    setExpandedSeasons(newExpanded);
  };

  // Handle episode selection
  const handleEpisodeSelect = (episode: Episode) => {
    if (episode.secureVideoLinks) {
      onEpisodeSelect(episode.secureVideoLinks, `${episode.title} (S${episode.season}E${episode.episode})`);
      onClose();
    }
  };

  if (!content || content.type !== 'series') {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Play className="w-5 h-5 text-primary" />
            All Seasons - {content.title}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Select any episode to watch it in the main player
          </p>
        </DialogHeader>
        
        <ScrollArea className="max-h-[70vh] pr-4">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="text-4xl mb-4">📺</div>
                <p className="text-lg text-muted-foreground">Loading seasons...</p>
              </div>
            </div>
          ) : seasons.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="text-6xl mb-4">📺</div>
                <h3 className="text-xl font-semibold mb-2">No Seasons Available</h3>
                <p className="text-muted-foreground">
                  This series doesn't have any seasons or episodes yet.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {seasons.map((season) => (
                <Card key={season.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    {/* Season Header */}
                    <Button
                      variant="ghost"
                      onClick={() => toggleSeason(season.seasonNumber)}
                      className="w-full justify-between p-4 h-auto hover:bg-muted/50"
                    >
                      <div className="flex items-center gap-3">
                        <div className="text-left">
                          <h3 className="font-semibold text-lg">
                            Season {season.seasonNumber}
                            {season.title && ` - ${season.title}`}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {season.episodes?.length || 0} episodes
                          </p>
                        </div>
                      </div>
                      {expandedSeasons.has(season.seasonNumber) ? (
                        <ChevronUp className="w-5 h-5" />
                      ) : (
                        <ChevronDown className="w-5 h-5" />
                      )}
                    </Button>
                    
                    {/* Episodes List */}
                    {expandedSeasons.has(season.seasonNumber) && (
                      <div className="border-t border-border/50 bg-muted/20">
                        {season.episodes && season.episodes.length > 0 ? (
                          <div className="p-4 space-y-2">
                            {season.episodes.map((episode) => (
                              <div
                                key={episode.id}
                                className="flex items-center justify-between p-3 border border-border/30 rounded-lg hover:bg-background/50 hover:border-primary/30 transition-all duration-200 group"
                              >
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <Badge variant="outline" className="text-xs">
                                      E{episode.episode}
                                    </Badge>
                                    <h4 className="font-medium text-sm group-hover:text-primary transition-colors truncate">
                                      {episode.title}
                                    </h4>
                                  </div>
                                  {episode.description && (
                                    <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed">
                                      {episode.description}
                                    </p>
                                  )}
                                  <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                                    {episode.runtime && (
                                      <div className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        <span>{episode.runtime}</span>
                                      </div>
                                    )}
                                    {episode.airDate && (
                                      <div className="flex items-center gap-1">
                                        <Calendar className="w-3 h-3" />
                                        <span>{new Date(episode.airDate).toLocaleDateString()}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  onClick={() => handleEpisodeSelect(episode)}
                                  disabled={!episode.secureVideoLinks}
                                  className="ml-3 flex-shrink-0"
                                >
                                  <Play className="w-4 h-4 mr-1" />
                                  Watch
                                </Button>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="p-6 text-center text-muted-foreground">
                            <p className="text-sm">No episodes available for this season</p>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
