
import React, { useState, useEffect } from "react";

// Telegram channel link
const telegramLink = "https://t.me/thestreamdb";

// Multiple image sources for maximum compatibility across hosting environments
const getTelegramImageSources = () => {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  return [
    "/telegram-banner.png",
    "/telegram-banner-backup.png",
    "./telegram-banner.png",
    `${baseUrl}/telegram-banner.png`,
    // Add data URL as ultimate fallback
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDQwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIwJSI+PHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzJmYmRmYTtzdG9wLW9wYWNpdHk6MSIgLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMyZGExZmE7c3RvcC1vcGFjaXR5OjEiIC8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PHJlY3Qgd2lkdGg9IjQwMCIgaGVpZ2h0PSIxNTAiIGZpbGw9InVybCgjZ3JhZCkiIHJ4PSIxMiIvPjx0ZXh0IHg9IjIwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJzeXN0ZW0tdWksIC1hcHBsZS1zeXN0ZW0sIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7wn5OxPC90ZXh0Pjx0ZXh0IHg9IjIwMCIgeT0iOTAiIGZvbnQtZmFtaWx5PSJzeXN0ZW0tdWksIC1hcHBsZS1zeXN0ZW0sIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Sm9pbiBvdXIgVGVsZWdyYW0gQ2hhbm5lbDwvdGV4dD48dGV4dCB4PSIyMDAiIHk9IjExNSIgZm9udC1mYW1pbHk9InN5c3RlbS11aSwgLWFwcGxlLXN5c3RlbSwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIG9wYWNpdHk9IjAuOSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TGF0ZXN0IHVwbG9hZHMgJmFtcDsgZXhjbHVzaXZlIGNvbnRlbnQ8L3RleHQ+PC9zdmc+"
  ];
};

export default function TelegramBanner() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showFallback, setShowFallback] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [telegramImageSources] = useState(() => getTelegramImageSources());

  // Enhanced image loading with comprehensive diagnostics
  useEffect(() => {
    const loadImage = async () => {
      const currentSrc = telegramImageSources[currentImageIndex];
      setDebugInfo(prev => [...prev, `Attempting to load: ${currentSrc}`]);

      try {
        const img = new Image();

        const loadPromise = new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            setDebugInfo(prev => [...prev, `⏰ Timeout loading: ${currentSrc}`]);
            reject(new Error(`Timeout loading ${currentSrc}`));
          }, 5000); // 5 second timeout

          img.onload = () => {
            clearTimeout(timeout);
            setDebugInfo(prev => [...prev, `✅ Successfully loaded: ${currentSrc}`]);
            setImageLoaded(true);
            setShowFallback(false);
            resolve();
          };

          img.onerror = (error) => {
            clearTimeout(timeout);
            setDebugInfo(prev => [...prev, `❌ Failed to load: ${currentSrc} - ${error}`]);
            reject(new Error(`Failed to load ${currentSrc}`));
          };
        });

        img.src = currentSrc;
        await loadPromise;

      } catch (error) {
        if (currentImageIndex < telegramImageSources.length - 1) {
          setDebugInfo(prev => [...prev, `Trying next source...`]);
          setCurrentImageIndex(prev => prev + 1);
        } else {
          setDebugInfo(prev => [...prev, `All sources failed, showing fallback`]);
          setShowFallback(true);
          setImageLoaded(false);
        }
      }
    };

    loadImage();
  }, [currentImageIndex, telegramImageSources]);

  // Log debug info to console for troubleshooting
  useEffect(() => {
    if (debugInfo.length > 0) {
      const latestDebug = debugInfo[debugInfo.length - 1];
      console.log('Telegram Banner Debug:', latestDebug);

      // Also log environment info on first load
      if (debugInfo.length === 1) {
        console.log('Environment Info:', {
          host: typeof window !== 'undefined' ? window.location.host : 'SSR',
          protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
          userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
          isVercel: typeof window !== 'undefined' ? window.location.host.includes('vercel') : false
        });
      }
    }
  }, [debugInfo]);

  return (
    <section
      className="mb-4 mx-auto max-w-3xl"
      style={{
        background: "none",
        boxShadow: "none",
        border: "none",
        padding: 0,
        transform: "scale(1)",
      }}
    >
      {/* Match Cloudflare banner structure exactly */}
      <div
        className="relative rounded-xl w-full mx-auto overflow-hidden"
        style={{ minHeight: 110 }}
      >
        <div
          className="relative z-10 px-2 py-5 flex flex-col items-center justify-center"
          style={{
            minHeight: 150, // Match Cloudflare banner's actual content height
          }}
        >
          {/* Clickable area for the entire content */}
          <a
            href={telegramLink}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-background rounded-xl"
            aria-label="Join our Telegram channel for latest uploads, exclusive content, and early access"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                window.open(telegramLink, '_blank', 'noopener,noreferrer');
              }
            }}
          >
            {/* Telegram Banner Content */}
            {!showFallback && (
              <img
                src={telegramImageSources[currentImageIndex]}
                alt="Join our Telegram channel for latest uploads, exclusive content, and early access"
                className="w-full rounded-xl transition-transform duration-200 group-hover:scale-[1.02]"
                style={{
                  height: "150px",
                  width: "100%",
                  objectFit: "cover",
                  objectPosition: "center",
                  display: imageLoaded ? "block" : "none",
                  background: "linear-gradient(to right, #2fbdfa 0%, #2da1fa 100%)",
                }}
                loading="lazy"
                crossOrigin="anonymous"
                onLoad={(e) => {
                  const target = e.target as HTMLImageElement;
                  setImageLoaded(true);
                  setShowFallback(false);
                  setDebugInfo(prev => [...prev, `✅ Image onLoad triggered: ${target.src}`]);
                }}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  setDebugInfo(prev => [...prev, `❌ Image onError triggered: ${target.src}`]);

                  if (currentImageIndex < telegramImageSources.length - 1) {
                    setCurrentImageIndex(prev => prev + 1);
                  } else {
                    setShowFallback(true);
                    setImageLoaded(false);
                  }
                }}
              />
            )}

            {/* Enhanced Fallback Banner */}
            {(showFallback || !imageLoaded) && (
              <div
                className="w-full rounded-xl transition-transform duration-200 group-hover:scale-[1.02]"
                style={{
                  background: "linear-gradient(to right, #2fbdfa 0%, #2da1fa 100%)",
                  color: "white",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "150px",
                  width: "100%",
                  fontFamily: "system-ui, -apple-system, sans-serif",
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  textAlign: "center",
                  padding: "1rem",
                  flexDirection: "column",
                  gap: "0.5rem",
                  position: "relative"
                }}
              >
                <div style={{ fontSize: "2rem" }}>📱</div>
                <div>Join our Telegram Channel</div>
                <div style={{ fontSize: "0.9rem", opacity: 0.9 }}>
                  Latest uploads & exclusive content
                </div>
                {/* Debug info in development */}
                {process.env.NODE_ENV === 'development' && (
                  <div style={{
                    position: "absolute",
                    bottom: "5px",
                    right: "5px",
                    fontSize: "0.7rem",
                    opacity: 0.7,
                    background: "rgba(0,0,0,0.5)",
                    padding: "2px 4px",
                    borderRadius: "2px"
                  }}>
                    Fallback Active
                  </div>
                )}
              </div>
            )}
          </a>
        </div>
      </div>
    </section>
  );
}
