# Alexhost VPS Database Setup Instructions

## Step 1: Database Setup in FastPanel

### 1.1 Access Your Database
From your FastPanel screenshots, I can see you have:
- Database name: `streamdb_database`
- Owner: `streamdb_opl_user`
- Server: `mysql(localhost)`

### 1.2 Create Database Tables

1. **Open phpMyAdmin** from your FastPanel control panel
2. **Select your database** `streamdb_database`
3. **Run the schema creation script**:
   - Go to the "SQL" tab in phpMyAdmin
   - Copy and paste the contents of `database/schema.sql`
   - Click "Go" to execute

### 1.3 Insert Initial Data

1. **Run the initial data script**:
   - In phpMyAdmin SQL tab
   - Copy and paste the contents of `database/initial_data.sql`
   - Click "Go" to execute

## Step 2: Database Connection Details

You'll need these details for your application:

```env
# Database Configuration for Alexhost VPS
DB_HOST=localhost
DB_PORT=3306
DB_NAME=streamdb_database
DB_USER=streamdb_opl_user
DB_PASSWORD=[Your database password from FastPanel]
```

### 2.1 Find Your Database Password
1. In FastPanel, go to **Databases** section
2. Click on your database `streamdb_database`
3. Look for the password or reset it if needed
4. **IMPORTANT**: Save this password securely!

## Step 3: Import Your CSV Data

### 3.1 Prepare Your CSV File
Your CSV should have these columns (adjust as needed):
```
id,title,description,year,type,category,genres,languages,image,cover_image,tmdb_id,imdb_rating,runtime,studio,tags,video_links,quality,audio_tracks,is_published,is_featured,add_to_carousel
```

### 3.2 Import via phpMyAdmin
1. **Upload CSV to server** (via FastPanel File Manager or FTP)
2. **In phpMyAdmin**:
   - Select your database
   - Go to "Import" tab
   - Choose your CSV file
   - Set format to "CSV"
   - Configure column mappings
   - Click "Go"

### 3.3 Alternative: Use Import Script
1. **Modify the import script**:
   - Edit `database/csv_import_template.sql`
   - Add your actual CSV data in the INSERT statements
   - Run the script in phpMyAdmin

## Step 4: Verify Database Setup

### 4.1 Check Tables Created
Run this query in phpMyAdmin:
```sql
SHOW TABLES;
```

You should see these tables:
- `admin_security_logs`
- `admin_sessions`
- `admin_users`
- `audio_tracks`
- `categories`
- `content`
- `content_audio`
- `content_genres`
- `content_languages`
- `content_quality`
- `episodes`
- `genres`
- `languages`
- `quality_options`
- `seasons`

### 4.2 Check Sample Data
```sql
SELECT COUNT(*) as total_content FROM content;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_genres FROM genres;
```

### 4.3 Test Admin User
```sql
SELECT username, email, role, is_active FROM admin_users;
```

## Step 5: Security Configuration

### 5.1 Change Default Admin Password
**IMPORTANT**: The default admin password is `admin123` - CHANGE THIS IMMEDIATELY!

1. **Generate new password hash**:
   - Use an online bcrypt generator with 12 rounds
   - Or use Node.js: `bcrypt.hash('your_new_password', 12)`

2. **Update password in database**:
```sql
UPDATE admin_users 
SET password_hash = 'your_new_bcrypt_hash_here' 
WHERE username = 'admin';
```

### 5.2 Database User Permissions
Ensure your database user has these permissions:
- SELECT, INSERT, UPDATE, DELETE on all tables
- CREATE, ALTER, DROP for schema updates

## Step 6: Connection Testing

### 6.1 Test Connection
Create a simple PHP test file to verify connection:

```php
<?php
$host = 'localhost';
$dbname = 'streamdb_database';
$username = 'streamdb_opl_user';
$password = 'your_database_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM content");
    $result = $stmt->fetch();
    
    echo "Database connection successful!<br>";
    echo "Total content items: " . $result['count'];
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
?>
```

## Step 7: Next Steps

After database setup is complete:

1. **Backend API Development**: Create Node.js/Express server
2. **Frontend Integration**: Update admin panel to use database
3. **File Upload**: Configure file storage for images/videos
4. **Security**: Implement proper authentication and authorization
5. **Testing**: Comprehensive testing of all functionality

## Troubleshooting

### Common Issues:

1. **Permission Denied**: Check database user permissions
2. **Connection Refused**: Verify host, port, and credentials
3. **Table Already Exists**: Drop existing tables or use IF NOT EXISTS
4. **Character Encoding**: Ensure UTF8MB4 charset for emoji support

### Support:
- Check FastPanel documentation
- Contact Alexhost support for server-specific issues
- Verify firewall settings for database access

## Security Checklist

- [ ] Changed default admin password
- [ ] Database user has minimal required permissions
- [ ] Connection uses SSL (if available)
- [ ] Regular database backups configured
- [ ] Access logs enabled
- [ ] Strong passwords for all accounts

---

**Next**: Once database is set up, we'll proceed with backend API development and frontend integration.
