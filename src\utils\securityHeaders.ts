/**
 * Security Headers and Policies for Streaming Website
 * Provides enhanced security without restrictive iframe sandbox attributes
 */

/**
 * Content Security Policy configuration for streaming website
 */
export const CSP_CONFIG = {
  // Allow iframe sources from trusted streaming platforms
  frameSrc: [
    "'self'",
    "https://*.youtube.com",
    "https://*.vimeo.com",
    "https://*.dailymotion.com",
    "https://*.twitch.tv",
    "https://*.streamtape.com",
    "https://*.filemoon.to",
    "https://*.filemoon.sx",
    "https://*.filemoon.in",
    "https://*.gradehgplus.com",
    "https://*.2embed.cc",
    "https://*.2embed.to",
    "https://*.streamable.com",
    "https://*.wistia.com",
    "https://*.jwplatform.com",
    "https://*.bitchute.com",
    "https://*.rumble.com",
    "https://*.odysee.com",
    // Add more trusted domains as needed
    "https://*" // Temporary for development - restrict in production
  ],
  
  // Script sources
  scriptSrc: [
    "'self'",
    "'unsafe-inline'", // Required for dynamic iframe loading
    "'unsafe-eval'", // May be needed for some embed players
    "https://cdn.jsdelivr.net",
    "https://unpkg.com"
  ],
  
  // Style sources
  styleSrc: [
    "'self'",
    "'unsafe-inline'",
    "https://fonts.googleapis.com",
    "https://cdn.jsdelivr.net"
  ],
  
  // Image sources
  imgSrc: [
    "'self'",
    "data:",
    "https:",
    "http:" // Some embed thumbnails may use HTTP
  ],
  
  // Font sources
  fontSrc: [
    "'self'",
    "https://fonts.gstatic.com",
    "data:"
  ],
  
  // Connect sources (for API calls)
  connectSrc: [
    "'self'",
    "https://api.themoviedb.org", // If using TMDB API
    "wss:", // WebSocket connections
    "https:" // HTTPS connections
  ]
};

/**
 * Generate Content Security Policy header value
 * @returns CSP header string
 */
export function generateCSPHeader(): string {
  const policies = [
    `default-src 'self'`,
    `frame-src ${CSP_CONFIG.frameSrc.join(' ')}`,
    `script-src ${CSP_CONFIG.scriptSrc.join(' ')}`,
    `style-src ${CSP_CONFIG.styleSrc.join(' ')}`,
    `img-src ${CSP_CONFIG.imgSrc.join(' ')}`,
    `font-src ${CSP_CONFIG.fontSrc.join(' ')}`,
    `connect-src ${CSP_CONFIG.connectSrc.join(' ')}`,
    `object-src 'none'`,
    `base-uri 'self'`,
    `form-action 'self'`,
    `frame-ancestors 'none'` // Prevent embedding this site in other frames
  ];
  
  return policies.join('; ');
}

/**
 * Security headers configuration for the application
 */
export const SECURITY_HEADERS = {
  // Content Security Policy
  'Content-Security-Policy': generateCSPHeader(),
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Enable XSS protection
  'X-XSS-Protection': '1; mode=block',
  
  // Prevent clickjacking
  'X-Frame-Options': 'DENY',
  
  // Strict Transport Security (HTTPS only)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Referrer Policy for privacy
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Permissions Policy (formerly Feature Policy)
  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'autoplay=(self)',
    'fullscreen=(self)',
    'picture-in-picture=(self)'
  ].join(', ')
};

/**
 * Platform-specific security configurations
 */
export const PLATFORM_SECURITY_CONFIG = {
  youtube: {
    referrerPolicy: 'strict-origin-when-cross-origin',
    allowedFeatures: ['autoplay', 'fullscreen', 'picture-in-picture'],
    requiresHTTPS: true
  },
  
  vimeo: {
    referrerPolicy: 'strict-origin-when-cross-origin',
    allowedFeatures: ['autoplay', 'fullscreen', 'picture-in-picture'],
    requiresHTTPS: true
  },
  
  streamtape: {
    referrerPolicy: 'strict-origin-when-cross-origin',
    allowedFeatures: ['autoplay', 'fullscreen'],
    requiresHTTPS: true,
    notes: 'May require popup permissions for some content'
  },
  
  filemoon: {
    referrerPolicy: 'no-referrer',
    allowedFeatures: ['autoplay', 'fullscreen'],
    requiresHTTPS: true,
    notes: 'Requires no-referrer policy for privacy'
  },
  
  gradehgplus: {
    referrerPolicy: 'strict-origin-when-cross-origin',
    allowedFeatures: ['autoplay', 'fullscreen'],
    requiresHTTPS: true,
    notes: 'May require form submission permissions'
  },
  
  '2embed': {
    referrerPolicy: 'no-referrer',
    allowedFeatures: ['autoplay', 'fullscreen'],
    requiresHTTPS: true,
    notes: 'Works best with no-referrer policy'
  }
};

/**
 * Apply security headers to a response (for server-side implementation)
 * @param response - HTTP response object
 */
export function applySecurityHeaders(response: any): void {
  Object.entries(SECURITY_HEADERS).forEach(([header, value]) => {
    response.setHeader(header, value);
  });
}

/**
 * Validate iframe source against security policy
 * @param src - Iframe source URL
 * @returns Whether the source is allowed
 */
export function validateIframeSrc(src: string): boolean {
  try {
    const url = new URL(src);
    
    // Must be HTTPS
    if (url.protocol !== 'https:') {
      console.warn('Iframe source must use HTTPS:', src);
      return false;
    }
    
    // Check against allowed frame sources
    const allowedDomains = CSP_CONFIG.frameSrc
      .filter(source => source.startsWith('https://'))
      .map(source => source.replace('https://', '').replace('*', ''));
    
    const hostname = url.hostname;
    const isAllowed = allowedDomains.some(domain => {
      if (domain.startsWith('*.')) {
        const baseDomain = domain.substring(2);
        return hostname.endsWith(baseDomain);
      }
      return hostname === domain;
    });
    
    if (!isAllowed) {
      console.warn('Iframe source not in allowed domains:', hostname);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Invalid iframe source URL:', src, error);
    return false;
  }
}

/**
 * Get security recommendations for a specific platform
 * @param platform - Platform identifier
 * @returns Security configuration and recommendations
 */
export function getPlatformSecurityConfig(platform: string) {
  return PLATFORM_SECURITY_CONFIG[platform as keyof typeof PLATFORM_SECURITY_CONFIG] || {
    referrerPolicy: 'strict-origin-when-cross-origin',
    allowedFeatures: ['autoplay', 'fullscreen'],
    requiresHTTPS: true,
    notes: 'Using default security configuration'
  };
}

/**
 * Enhanced iframe security attributes without sandbox restrictions
 * @param src - Iframe source URL
 * @returns Security attributes object
 */
export function getSecureIframeAttributes(src: string) {
  const url = new URL(src);
  const platform = detectPlatformFromUrl(url.hostname);
  const config = getPlatformSecurityConfig(platform);
  
  return {
    // No sandbox restrictions to prevent "sandboxed iframe" errors
    sandbox: '',
    
    // Platform-specific referrer policy
    referrerPolicy: config.referrerPolicy,
    
    // Enhanced permissions for video playback
    allow: [
      'accelerometer',
      'autoplay',
      'clipboard-write',
      'encrypted-media',
      'gyroscope',
      'picture-in-picture',
      'fullscreen'
    ].join('; '),
    
    // Security attributes
    loading: 'lazy',
    
    // Additional security
    'data-platform': platform,
    'data-secure': 'true'
  };
}

/**
 * Detect platform from hostname
 * @param hostname - URL hostname
 * @returns Platform identifier
 */
function detectPlatformFromUrl(hostname: string): string {
  if (hostname.includes('youtube.com')) return 'youtube';
  if (hostname.includes('vimeo.com')) return 'vimeo';
  if (hostname.includes('streamtape.com')) return 'streamtape';
  if (hostname.includes('filemoon')) return 'filemoon';
  if (hostname.includes('gradehgplus.com')) return 'gradehgplus';
  if (hostname.includes('2embed')) return '2embed';
  return 'unknown';
}
