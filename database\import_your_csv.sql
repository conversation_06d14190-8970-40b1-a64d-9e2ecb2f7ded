-- CSV Import Script for Your Specific Data Structure
-- This script imports your content.csv file into the database

-- Step 1: Create temporary table matching your CSV structure
CREATE TEMPORARY TABLE temp_csv_import (
    title VARCHAR(255),
    description TEXT,
    type VARCHAR(20),
    year INT,
    genres TEXT, -- Semicolon-separated: Action;Sci-Fi;Thriller
    languages TEXT, -- Semicolon-separated: English;Spanish
    status VARCHAR(20), -- Published/Draft
    featured VARCHAR(10), -- Yes/No
    carousel VARCHAR(10), -- Yes/No
    imdb_rating DECIMAL(3,1),
    runtime VARCHAR(20),
    studio VARCHAR(255),
    tags TEXT, -- Comma-separated: action,blockbuster,2024
    poster_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    cover_image VARCHAR(500),
    trailer_url VARCHAR(500),
    subtitle_url VARCHAR(500),
    video_links TEXT, -- Pipe-separated: link1|link2
    secure_video_links TEXT, -- Pipe-separated: encrypted1|encrypted2
    quality TEXT, -- Semicolon-separated: HD;BluRay
    audio_tracks TEXT, -- Semicolon-separated: English;Spanish;French
    tmdb_id VARCHAR(20),
    total_seasons INT,
    total_episodes INT,
    created_at VARCHAR(30),
    updated_at VARCHAR(30)
);

-- Step 2: Load your CSV data
-- IMPORTANT: Upload your content.csv file to your server first
-- Then update the file path below to match your server location

/*
LOAD DATA INFILE '/path/to/your/content.csv'
INTO TABLE temp_csv_import
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS; -- Skip header row
*/

-- Alternative: Manual INSERT for your sample data
-- Replace this with your actual CSV data:

INSERT INTO temp_csv_import (
    title, description, type, year, genres, languages, status, featured, carousel,
    imdb_rating, runtime, studio, tags, poster_url, thumbnail_url, cover_image,
    trailer_url, subtitle_url, video_links, secure_video_links, quality, audio_tracks,
    tmdb_id, total_seasons, total_episodes, created_at, updated_at
) VALUES
(
    'Movie Title Example',
    'A thrilling action movie with amazing special effects',
    'movie',
    2024,
    'Action;Sci-Fi;Thriller',
    'English;Spanish',
    'Published',
    'Yes',
    'No',
    8.5,
    '120',
    'Example Studios',
    'action,blockbuster,2024',
    'https://example.com/poster.jpg',
    'https://example.com/thumb.jpg',
    'https://example.com/cover.jpg',
    'https://example.com/trailer.mp4',
    'https://example.com/subtitles.srt',
    'https://player.com/embed/123',
    'encrypted_link_here',
    'HD;BluRay',
    'English;Spanish;French',
    '1234567',
    NULL,
    NULL,
    '2024-01-15T10:30:00Z',
    '2024-01-15T10:30:00Z'
),
(
    'Web Series Example',
    'An exciting web series with multiple seasons',
    'series',
    2023,
    'Drama;Mystery',
    'English',
    'Published',
    'Yes',
    'Yes',
    9.0,
    '45',
    'Streaming Network',
    'drama,mystery,series',
    'https://example.com/series-poster.jpg',
    'https://example.com/series-thumb.jpg',
    'https://example.com/series-cover.jpg',
    'https://example.com/series-trailer.mp4',
    '',
    'https://player.com/embed/456|https://player.com/embed/789',
    'encrypted_link_1|encrypted_link_2',
    'HD;WEB',
    'English',
    '7654321',
    3,
    24,
    '2023-06-01T08:00:00Z',
    '2024-01-10T15:45:00Z'
);

-- Step 3: Helper function to get or create category
DELIMITER //
CREATE FUNCTION GetOrCreateCategoryByType(content_type VARCHAR(20)) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE cat_id INT DEFAULT NULL;
    
    -- Get appropriate category based on content type
    IF content_type = 'movie' THEN
        SELECT id INTO cat_id FROM categories WHERE slug = 'english-movies' LIMIT 1;
    ELSEIF content_type = 'series' THEN
        SELECT id INTO cat_id FROM categories WHERE slug = 'english-web-series' LIMIT 1;
    ELSE
        SELECT id INTO cat_id FROM categories WHERE slug = 'english-movies' LIMIT 1;
    END IF;
    
    RETURN cat_id;
END//
DELIMITER ;

-- Step 4: Import main content data
INSERT INTO content (
    id, title, description, year, type, category_id, image, cover_image,
    tmdb_id, poster_url, thumbnail_url, video_links, secure_video_links,
    imdb_rating, runtime, studio, tags, trailer, subtitle_url,
    is_published, is_featured, add_to_carousel, total_seasons, total_episodes,
    created_at, updated_at
)
SELECT 
    UUID() as id,
    title,
    description,
    year,
    type,
    GetOrCreateCategoryByType(type) as category_id,
    poster_url as image, -- Using poster as main image
    cover_image,
    tmdb_id,
    poster_url,
    thumbnail_url,
    video_links,
    secure_video_links,
    imdb_rating,
    runtime,
    studio,
    tags,
    trailer_url as trailer,
    subtitle_url,
    CASE WHEN status = 'Published' THEN 1 ELSE 0 END as is_published,
    CASE WHEN featured = 'Yes' THEN 1 ELSE 0 END as is_featured,
    CASE WHEN carousel = 'Yes' THEN 1 ELSE 0 END as add_to_carousel,
    COALESCE(total_seasons, 0),
    COALESCE(total_episodes, 0),
    STR_TO_DATE(created_at, '%Y-%m-%dT%H:%i:%sZ'),
    STR_TO_DATE(updated_at, '%Y-%m-%dT%H:%i:%sZ')
FROM temp_csv_import;

-- Step 5: Import genres (handle semicolon-separated values)
INSERT INTO content_genres (content_id, genre_id)
SELECT DISTINCT
    c.id,
    g.id
FROM content c
JOIN temp_csv_import t ON c.title = t.title
CROSS JOIN genres g
WHERE FIND_IN_SET(g.name, REPLACE(t.genres, ';', ',')) > 0
AND t.genres IS NOT NULL
AND t.genres != '';

-- Step 6: Import languages (handle semicolon-separated values)
INSERT INTO content_languages (content_id, language_id)
SELECT DISTINCT
    c.id,
    l.id
FROM content c
JOIN temp_csv_import t ON c.title = t.title
CROSS JOIN languages l
WHERE FIND_IN_SET(l.name, REPLACE(t.languages, ';', ',')) > 0
AND t.languages IS NOT NULL
AND t.languages != '';

-- Step 7: Import quality options (handle semicolon-separated values)
INSERT INTO content_quality (content_id, quality_id)
SELECT DISTINCT
    c.id,
    q.id
FROM content c
JOIN temp_csv_import t ON c.title = t.title
CROSS JOIN quality_options q
WHERE FIND_IN_SET(q.name, REPLACE(t.quality, ';', ',')) > 0
AND t.quality IS NOT NULL
AND t.quality != '';

-- Step 8: Import audio tracks (handle semicolon-separated values)
INSERT INTO content_audio (content_id, audio_id)
SELECT DISTINCT
    c.id,
    a.id
FROM content c
JOIN temp_csv_import t ON c.title = t.title
CROSS JOIN audio_tracks a
WHERE FIND_IN_SET(a.name, REPLACE(t.audio_tracks, ';', ',')) > 0
AND t.audio_tracks IS NOT NULL
AND t.audio_tracks != '';

-- Step 9: Create seasons for web series
INSERT INTO seasons (id, content_id, season_number, title, description, created_at, updated_at)
SELECT 
    CONCAT(c.id, '-season-', season_num.num) as id,
    c.id as content_id,
    season_num.num as season_number,
    CONCAT('Season ', season_num.num) as title,
    CONCAT('Season ', season_num.num, ' of ', c.title) as description,
    c.created_at,
    c.updated_at
FROM content c
CROSS JOIN (
    SELECT 1 as num UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5
) season_num
WHERE c.type = 'series' 
AND c.total_seasons >= season_num.num
AND c.total_seasons > 0;

-- Step 10: Clean up
DROP FUNCTION IF EXISTS GetOrCreateCategoryByType;
DROP TEMPORARY TABLE temp_csv_import;

-- Step 11: Verify import
SELECT 
    'Import Summary' as status,
    COUNT(*) as total_content,
    SUM(CASE WHEN type = 'movie' THEN 1 ELSE 0 END) as movies,
    SUM(CASE WHEN type = 'series' THEN 1 ELSE 0 END) as series,
    SUM(CASE WHEN is_published = 1 THEN 1 ELSE 0 END) as published,
    SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured
FROM content;

SELECT 
    'Relationships Created' as status,
    (SELECT COUNT(*) FROM content_genres) as genre_links,
    (SELECT COUNT(*) FROM content_languages) as language_links,
    (SELECT COUNT(*) FROM content_quality) as quality_links,
    (SELECT COUNT(*) FROM content_audio) as audio_links,
    (SELECT COUNT(*) FROM seasons) as seasons_created
;

-- Show sample of imported data
SELECT 
    c.id,
    c.title,
    c.year,
    c.type,
    c.is_published,
    c.is_featured,
    cat.name as category,
    GROUP_CONCAT(DISTINCT g.name SEPARATOR ';') as genres,
    GROUP_CONCAT(DISTINCT l.name SEPARATOR ';') as languages
FROM content c
LEFT JOIN categories cat ON c.category_id = cat.id
LEFT JOIN content_genres cg ON c.id = cg.content_id
LEFT JOIN genres g ON cg.genre_id = g.id
LEFT JOIN content_languages cl ON c.id = cl.content_id
LEFT JOIN languages l ON cl.language_id = l.id
GROUP BY c.id, c.title, c.year, c.type, c.is_published, c.is_featured, cat.name
ORDER BY c.created_at DESC
LIMIT 10;

-- Success message
SELECT 'CSV Import Completed Successfully!' as message;
