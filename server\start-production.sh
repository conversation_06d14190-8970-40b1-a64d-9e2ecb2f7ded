#!/bin/bash

# StreamDB Online - Production Start Script
# This script properly starts the application on the production server

set -e

echo "🚀 Starting StreamDB Online Production Server..."

# Navigate to project directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Load environment variables
if [ ! -f .env ]; then
    echo "❌ Environment file not found!"
    exit 1
fi

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 is not installed!"
    echo "Installing PM2..."
    npm install -g pm2
fi

# Stop existing processes
echo "🛑 Stopping existing processes..."
pm2 stop streamdb-online 2>/dev/null || true
pm2 delete streamdb-online 2>/dev/null || true

# Start the application
echo "🚀 Starting StreamDB Online..."
pm2 start index.js --name streamdb-online --env production

# Start webhook handler if it exists
if [ -f "../deployment/webhook-handler.js" ]; then
    echo "🔗 Starting webhook handler..."
    pm2 stop webhook-handler 2>/dev/null || true
    pm2 delete webhook-handler 2>/dev/null || true
    pm2 start ../deployment/webhook-handler.js --name webhook-handler
fi

# Save PM2 configuration
pm2 save

# Show status
echo "📊 Current PM2 Status:"
pm2 status

echo "✅ StreamDB Online started successfully!"
echo "🌐 Server should be accessible at: https://streamdb.online"
echo "🔧 Admin panel: https://streamdb.online/admin"

# Test database connection
echo "🗄️  Testing database connection..."
node test-db-connection.js

echo "🏁 Startup complete!"
