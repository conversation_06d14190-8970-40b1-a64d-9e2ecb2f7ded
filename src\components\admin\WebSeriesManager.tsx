import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { MediaItem } from "@/types/media";
import { Search, Play, Calendar, Clock, Users, Filter } from "lucide-react";
import apiService from "@/services/apiService";
import EpisodeManager from "./EpisodeManager";

interface WebSeriesManagerProps {}

export default function WebSeriesManager({}: WebSeriesManagerProps) {
  const { toast } = useToast();
  const [webSeries, setWebSeries] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSeries, setSelectedSeries] = useState<MediaItem | null>(null);
  const [isEpisodeManagerOpen, setIsEpisodeManagerOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Load web series from database
  const loadWebSeries = async (page = 1, search = "") => {
    try {
      setLoading(true);
      const params = {
        type: 'series',
        page,
        limit: 20,
        search: search.trim() || undefined,
        published: undefined // Show both published and unpublished
      };

      const result = await apiService.getContent(params);
      
      if (result.success) {
        setWebSeries(result.data || []);
        setTotalPages(result.pagination?.totalPages || 1);
        setTotalItems(result.pagination?.totalItems || 0);
        setCurrentPage(page);
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to load web series",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading web series:', error);
      toast({
        title: "Error",
        description: "Failed to load web series",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadWebSeries(1, searchTerm);
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
    loadWebSeries(1, value);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    loadWebSeries(page, searchTerm);
  };

  // Handle manage episodes
  const handleManageEpisodes = (series: MediaItem) => {
    setSelectedSeries(series);
    setIsEpisodeManagerOpen(true);
  };

  // Handle save content (callback from EpisodeManager)
  const handleSaveContent = (updatedContent: MediaItem) => {
    setWebSeries(prev => prev.map(item =>
      item.id === updatedContent.id ? updatedContent : item
    ));
  };

  if (loading && webSeries.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-4xl mb-4">📺</div>
          <p className="text-lg text-muted-foreground">Loading web series...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-5 h-5 text-primary" />
            All Web Series Management
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Manage seasons and episodes for all web series content. Click on any series to access its episodes.
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search web series..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>{totalItems} series</span>
              </div>
              <div className="flex items-center gap-1">
                <Filter className="w-4 h-4" />
                <span>Page {currentPage} of {totalPages}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Web Series Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {webSeries.map((series) => (
          <Card key={series.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardContent className="p-0">
              {/* Series Image */}
              <div className="relative aspect-video overflow-hidden rounded-t-lg">
                <img
                  src={series.image || series.coverImage || '/placeholder-image.jpg'}
                  alt={series.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
                <div className="absolute top-2 right-2">
                  <Badge variant={series.isPublished ? "default" : "secondary"}>
                    {series.isPublished ? "Published" : "Draft"}
                  </Badge>
                </div>
              </div>
              
              {/* Series Info */}
              <div className="p-4 space-y-3">
                <div>
                  <h3 className="font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors">
                    {series.title}
                  </h3>
                  <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                    {series.description}
                  </p>
                </div>
                
                {/* Metadata */}
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    <span>{series.year}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Play className="w-3 h-3" />
                    <span>{series.totalSeasons || 0} seasons</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{series.totalEpisodes || 0} episodes</span>
                  </div>
                </div>
                
                {/* Action Button */}
                <Button
                  onClick={() => handleManageEpisodes(series)}
                  className="w-full mt-3"
                  variant="outline"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Manage Episodes
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {webSeries.length === 0 && !loading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-6xl mb-4">📺</div>
            <h3 className="text-xl font-semibold mb-2">No Web Series Found</h3>
            <p className="text-muted-foreground text-center max-w-md">
              {searchTerm 
                ? `No web series found matching "${searchTerm}". Try adjusting your search terms.`
                : "No web series have been added yet. Create some web series content first."
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4 text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Episode Manager Dialog */}
      <EpisodeManager
        isOpen={isEpisodeManagerOpen}
        onClose={() => setIsEpisodeManagerOpen(false)}
        content={selectedSeries}
        onSave={handleSaveContent}
      />
    </div>
  );
}
