
import * as React from "react";
import CardGrid from "./CardGrid";
import { MediaItem } from "@/types/media";
import { scrollToTop } from "@/utils/scrollToTop";

interface Props {
  items: MediaItem[];
  pageSize?: number;
}

export default function PaginatedGrid({ items, pageSize = 8 }: Props) {
  const [page, setPage] = React.useState(1);

  const pageCount = Math.ceil(items.length / pageSize);

  const pageItems = items.slice((page - 1) * pageSize, page * pageSize);

  // Function to handle page changes with scroll to top
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    scrollToTop();
  };

  return (
    <div className="w-full">
      <CardGrid items={pageItems} />

      {pageCount > 1 && (
        <div className="flex justify-center items-center space-x-1 sm:space-x-1.5 mt-6 sm:mt-7 px-2">
          <button
            className="px-3 py-2 sm:px-2 sm:py-1 rounded-md bg-secondary hover:bg-secondary/80 font-semibold text-secondary-foreground text-xs disabled:opacity-50 min-h-[44px] sm:min-h-auto flex items-center justify-center"
            onClick={() => handlePageChange(page - 1)}
            disabled={page === 1}
          >
            Prev
          </button>
          <div className="flex space-x-1 overflow-x-auto max-w-[200px] sm:max-w-none">
            {Array.from({ length: pageCount }, (_, i) => (
              <button
                key={i}
                className={`px-3 py-2 sm:px-2 sm:py-1 rounded-md font-semibold text-xs min-h-[44px] sm:min-h-auto flex items-center justify-center flex-shrink-0 ${
                  page === i + 1 ? "bg-primary text-primary-foreground" : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                }`}
                onClick={() => handlePageChange(i + 1)}
              >
                {i + 1}
              </button>
            ))}
          </div>
          <button
            className="px-3 py-2 sm:px-2 sm:py-1 rounded-md bg-secondary hover:bg-secondary/80 font-semibold text-secondary-foreground text-xs disabled:opacity-50 min-h-[44px] sm:min-h-auto flex items-center justify-center"
            onClick={() => handlePageChange(page + 1)}
            disabled={page === pageCount}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
