# 🚀 StreamDB.online Complete Production Deployment Guide

## 📋 OVERVIEW

This guide provides step-by-step instructions for deploying your StreamDB.online website with a secure reverse proxy architecture that completely hides your backend server.

### Architecture Overview
```
Client Browser
     ↓
Cloudflare (Free Plan) - SSL Termination & CDN
     ↓
Reverse Proxy VPS: ************* (Nginx Proxy)
     ↓
Backend VPS: *********** (FastPanel + MySQL + StreamDB)
```

### Key Security Features
- ✅ Backend server completely hidden from public
- ✅ FastPanel accessible via secure subdomain
- ✅ MySQL database local-only connections
- ✅ SSL certificates managed by Cloudflare
- ✅ All traffic filtered through reverse proxy

---

## 🌐 PHASE 1: CLOUDFLARE DNS CONFIGURATION

### Step 1.1: Access Cloudflare Dashboard
1. Go to: https://dash.cloudflare.com/
2. Login with your Cloudflare account
3. Click on your domain: `streamdb.online`
4. Navigate to: DNS → Records

### Step 1.2: Configure DNS Records

**Delete any existing A/AAAA records first, then add:**

1. **Main Website Record:**
   ```
   Type: A
   Name: @
   IPv4 address: *************
   Proxy status: ✅ Proxied (Orange Cloud)
   TTL: Auto
   ```

2. **WWW Subdomain Record:**
   ```
   Type: A
   Name: www
   IPv4 address: *************
   Proxy status: ✅ Proxied (Orange Cloud)
   TTL: Auto
   ```

3. **FastPanel Subdomain Record:**
   ```
   Type: A
   Name: fastpanel
   IPv4 address: *************
   Proxy status: ✅ Proxied (Orange Cloud)
   TTL: Auto
   ```

### Step 1.3: Configure SSL/TLS Settings
1. Navigate to: SSL/TLS → Overview
2. Set encryption mode to: `Full (strict)`
3. Navigate to: SSL/TLS → Edge Certificates
4. Enable: Always Use HTTPS: `ON`
5. Enable: HTTP Strict Transport Security (HSTS): `ON`
6. Enable: Automatic HTTPS Rewrites: `ON`

### Step 1.4: Configure Security Settings
1. Navigate to: Security → Settings
2. Set Security Level: `Medium`
3. Enable: Bot Fight Mode: `ON`
4. Navigate to: Speed → Optimization
5. Enable: Auto Minify (CSS, HTML, JS): `ON`
6. Enable: Brotli: `ON`

### Step 1.5: Verify DNS Propagation
Wait 5-10 minutes, then test:
```bash
nslookup streamdb.online
nslookup www.streamdb.online
nslookup fastpanel.streamdb.online
```

Expected result: All should resolve to *************

---

## 🔄 PHASE 2: REVERSE PROXY SERVER SETUP (*************)

### Step 2.1: Connect to Reverse Proxy Server
```bash
ssh root@*************
```

### Step 2.2: Install and Update System
```bash
# Update system packages
apt update && apt upgrade -y

# Install required packages
apt install -y nginx certbot python3-certbot-nginx ufw fail2ban htop curl wget
```

### Step 2.3: Configure Firewall (UFW)
```bash
# Reset firewall to default
ufw --force reset

# Set default policies
ufw default deny incoming
ufw default allow outgoing

# Allow essential services
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS

# Enable firewall
ufw --force enable

# Verify firewall status
ufw status numbered
```

### Step 2.4: Generate Cloudflare Origin Certificate

1. Go to Cloudflare Dashboard: SSL/TLS → Origin Server
2. Click: "Create Certificate"
3. Select: "Let Cloudflare generate a private key and a CSR"
4. Hostnames: `*.streamdb.online, streamdb.online`
5. Key type: `RSA (2048)`
6. Certificate Validity: `15 years`
7. Click: "Create"

Save the certificates:
```bash
# Create SSL directory
mkdir -p /etc/ssl/certs /etc/ssl/private

# Save the Origin Certificate
nano /etc/ssl/certs/cloudflare-origin.pem
# Paste the "Origin Certificate" content from Cloudflare

# Save the Private Key
nano /etc/ssl/private/cloudflare-origin.key
# Paste the "Private Key" content from Cloudflare

# Set proper permissions
chmod 644 /etc/ssl/certs/cloudflare-origin.pem
chmod 600 /etc/ssl/private/cloudflare-origin.key
```

### Step 2.5: Create Nginx Configuration

Create the configuration file:
```bash
nano /etc/nginx/sites-available/streamdb.online
```

**Note:** The complete Nginx configuration is provided in the separate file: `nginx-reverse-proxy-config.conf`

### Step 2.6: Enable Nginx Configuration
```bash
# Test nginx configuration
nginx -t

# Enable the site
ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/

# Remove default nginx site
rm -f /etc/nginx/sites-enabled/default

# Restart nginx
systemctl restart nginx
systemctl enable nginx

# Check nginx status
systemctl status nginx
```

---

## 🖥️ PHASE 3: BACKEND SERVER PREPARATION (***********)

### Step 3.1: Connect to Backend Server
```bash
ssh root@***********
```

### Step 3.2: Update System and Install Dependencies
```bash
# Update system
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget git unzip nginx ufw fail2ban htop

# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Install PM2 globally
npm install -g pm2

# Verify installations
node --version    # Should show v18.x.x
npm --version     # Should show 9.x.x or higher
pm2 --version     # Should show PM2 version
```

### Step 3.3: Configure Firewall for Backend Server
```bash
# Reset firewall
ufw --force reset

# Set default policies
ufw default deny incoming
ufw default allow outgoing

# Allow only necessary ports
ufw allow 22/tcp                    # SSH
ufw allow 80/tcp                    # HTTP (for reverse proxy)
ufw allow 443/tcp                   # HTTPS (for reverse proxy)
ufw allow 8888/tcp                  # FastPanel HTTPS
ufw allow from *************        # Allow reverse proxy server

# Enable firewall
ufw --force enable

# Verify firewall status
ufw status numbered
```

### Step 3.4: Configure FastPanel for Secure Access

Check FastPanel status:
```bash
systemctl status fastpanel
netstat -tlnp | grep :8888
```

Configure FastPanel:
```bash
nano /etc/fastpanel/fastpanel.conf
```

Ensure these settings:
```ini
bind_ip = 0.0.0.0
bind_port = 8888
ssl_enabled = false
behind_proxy = true
proxy_protocol = false
auto_ssl = false
letsencrypt_enabled = false
```

Restart FastPanel:
```bash
systemctl restart fastpanel
systemctl enable fastpanel
systemctl status fastpanel
```

### Step 3.5: Create Project Directory Structure
```bash
# Create the main project directory
mkdir -p /var/www/streamdb_onl_usr/data/www/streamdb.online
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Create necessary subdirectories
mkdir -p server logs uploads backups

# Set proper ownership
chown -R www-data:www-data /var/www/streamdb_onl_usr/data/www/streamdb.online
chmod -R 755 /var/www/streamdb_onl_usr/data/www/streamdb.online
```

### Step 3.6: Configure Nginx for Backend

Create nginx configuration:
```bash
nano /etc/nginx/sites-available/streamdb-backend
```

**Note:** The complete backend Nginx configuration is provided in the separate file: `nginx-backend-config.conf`

Enable the configuration:
```bash
# Test nginx configuration
nginx -t

# Enable the site
ln -s /etc/nginx/sites-available/streamdb-backend /etc/nginx/sites-enabled/

# Remove default site
rm -f /etc/nginx/sites-enabled/default

# Restart nginx
systemctl restart nginx
systemctl enable nginx
systemctl status nginx
```

---

## 🗄️ PHASE 4: MYSQL DATABASE CONFIGURATION

### Step 4.1: Access FastPanel Database Management

Open FastPanel:
- Primary: https://fastpanel.streamdb.online
- Fallback: http://***********:8888

Login with your FastPanel credentials.

### Step 4.2: Create Database and User

In FastPanel:
1. Navigate to: Databases → MySQL
2. Click: "Create Database"
3. Database Name: `streamdb_database`
4. Character Set: `utf8mb4`
5. Collation: `utf8mb4_unicode_ci`
6. Click: "Create"

Create Database User:
1. Click: "Create User"
2. Username: `dbadmin_streamdb`
3. Password: Generate a strong password (save this!)
4. Host: `localhost`
5. Click: "Create"

Grant Permissions:
1. Select the user: `dbadmin_streamdb`
2. Select the database: `streamdb_database`
3. Grant: ALL PRIVILEGES
4. Click: "Apply"

### Step 4.3: Test Database Connection

SSH into backend server:
```bash
ssh root@***********

# Test MySQL service
systemctl status mysql

# Test socket connection
ls -la /var/run/mysqld/mysqld.sock

# Test database connection
mysql -u dbadmin_streamdb -p -h localhost streamdb_database
```

### Step 4.4: Import Database Schema

Method 1 - Using FastPanel phpMyAdmin (Recommended):
1. In FastPanel: Navigate to Databases → phpMyAdmin
2. Select database: `streamdb_database`
3. Click: Import tab
4. Upload file: Upload your `database/complete_schema.sql` file
5. Click: "Go"

Method 2 - Using Command Line:
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
mysql -u dbadmin_streamdb -p streamdb_database < database/complete_schema.sql
```

### Step 4.5: Verify Database Schema

Check if tables were created:
```bash
mysql -u dbadmin_streamdb -p streamdb_database

SHOW TABLES;
DESCRIBE content;
exit
```

Expected tables: content, categories, genres, admin_users, sessions, etc.

### Step 4.6: Configure MySQL for Production

Edit MySQL configuration:
```bash
nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

Ensure these settings:
```ini
[mysqld]
bind-address = 127.0.0.1
socket = /var/run/mysqld/mysqld.sock
skip-networking = false
skip-name-resolve = true
innodb_buffer_pool_size = 256M
max_connections = 100
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
```

Restart MySQL:
```bash
systemctl restart mysql
systemctl status mysql
```

---

## 🚀 PHASE 5: APPLICATION DEPLOYMENT

### Step 5.1: Prepare Local Codebase

On your local machine:
```bash
cd "g:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB"

# Build the React frontend
npm run build

# Verify build
ls -la dist/

# Create deployment package
zip -r streamdb-production.zip . -x "node_modules/*" ".git/*" "*.log" ".env*"
```

### Step 5.2: Upload Codebase to Backend Server

Method 1 - Using FastPanel File Manager (Recommended):
1. Access FastPanel: https://fastpanel.streamdb.online
2. Navigate to: File Manager
3. Go to: `/var/www/streamdb_onl_usr/data/www/streamdb.online/`
4. Upload: `streamdb-production.zip`
5. Extract: Right-click → Extract Here
6. Delete: The zip file after extraction

Method 2 - Using SCP:
```bash
scp streamdb-production.zip root@***********:/var/www/streamdb_onl_usr/data/www/streamdb.online/
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/
unzip streamdb-production.zip
rm streamdb-production.zip
```

### Step 5.3: Configure Environment Variables

SSH into backend server:
```bash
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
```

Create production environment file:
```bash
cp .env.example .env
nano .env
```

Configure your .env file:
```env
# Database Configuration
DB_HOST=localhost
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=streamdb_database
DB_USER=dbadmin_streamdb
DB_PASSWORD=YOUR_ACTUAL_DATABASE_PASSWORD_FROM_STEP_4_2

# JWT Configuration
JWT_SECRET=your_very_long_random_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Session Configuration
SESSION_SECRET=your_very_long_random_session_secret_key_here
SESSION_TIMEOUT=86400000

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# CORS Configuration
FRONTEND_URL=https://streamdb.online
CORS_ORIGIN=https://streamdb.online

# Server Configuration
PORT=3001
NODE_ENV=production

# Admin User Configuration
ADMIN_USERNAME=streamdb_admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=YOUR_ULTRA_SECURE_ADMIN_PASSWORD_HERE
```

Generate secure secrets:
```bash
echo "JWT_SECRET=$(openssl rand -hex 64)"
echo "SESSION_SECRET=$(openssl rand -hex 64)"
```

### Step 5.4: Install Backend Dependencies

```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
npm install --production
npm list --depth=0
```

### Step 5.5: Test Database Connection

```bash
# Update test script with your password
nano test-db-connection.js
# Replace 'YOUR_DATABASE_PASSWORD' with actual password

# Test connection
node test-db-connection.js
```

Expected output:
```
Testing database connection...
✅ Database connection successful
📊 Database has 10 tables
🏁 Database test completed successfully
```

### Step 5.6: Create Required Directories

```bash
mkdir -p /var/www/streamdb_onl_usr/data/www/streamdb.online/logs
mkdir -p /var/www/streamdb_onl_usr/data/www/streamdb.online/uploads
mkdir -p /var/www/streamdb_onl_usr/data/www/streamdb.online/backups

# Set proper permissions
chown -R www-data:www-data /var/www/streamdb_onl_usr/data/www/streamdb.online/
chmod -R 755 /var/www/streamdb_onl_usr/data/www/streamdb.online/
chmod -R 777 /var/www/streamdb_onl_usr/data/www/streamdb.online/uploads/
```

### Step 5.7: Start Application with PM2

```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Start application
pm2 start index.js --name "streamdb-online" --env production

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup

# Check status
pm2 status
pm2 logs streamdb-online
```

### Step 5.8: Test Application

```bash
# Test health endpoint
curl http://localhost:3001/api/health

# Test nginx serving
curl -I http://localhost:80

# Test API proxy
curl http://localhost:80/api/health
```

---

## 🔒 PHASE 6: SSL CERTIFICATE CONFIGURATION

### Step 6.1: Understanding SSL Architecture

Your SSL Setup:
```
Browser → Cloudflare (SSL Termination) → Reverse Proxy (Origin Cert) → Backend (HTTP)
```

### Step 6.2: Fix FastPanel SSL Issues

SSH into backend server:
```bash
ssh root@***********
nano /etc/fastpanel/fastpanel.conf
```

Configure FastPanel for reverse proxy:
```ini
bind_ip = 0.0.0.0
bind_port = 8888
ssl_enabled = false
behind_proxy = true
proxy_protocol = false
force_https = false
auto_ssl = false
letsencrypt_enabled = false
```

Restart FastPanel:
```bash
systemctl restart fastpanel
systemctl status fastpanel
```

### Step 6.3: Update Reverse Proxy for FastPanel

SSH into reverse proxy server:
```bash
ssh root@*************
nano /etc/nginx/sites-available/streamdb.online
```

Ensure FastPanel section uses HTTP:
```nginx
# Proxy to FastPanel on backend server (HTTP, not HTTPS)
location / {
    proxy_pass http://***********:8888;  # HTTP, not HTTPS
    # ... other proxy settings
}
```

Test and reload:
```bash
nginx -t
systemctl reload nginx
```

### Step 6.4: Test SSL Configuration

```bash
# Test main website
curl -I https://streamdb.online

# Test FastPanel
curl -I https://fastpanel.streamdb.online

# Test API
curl https://streamdb.online/api/health
```

Expected results:
- ✅ All HTTPS requests return 200 OK
- ✅ No SSL certificate errors
- ✅ No redirect loops

---

## 🛡️ PHASE 7: SECURITY HARDENING & TESTING

### Step 7.1: Verify Firewall Configuration

On both servers, check:
```bash
ufw status numbered
netstat -tlnp | grep LISTEN
```

### Step 7.2: Test Database Security

Verify MySQL is local-only:
```bash
# Should work (local)
mysql -h localhost -u dbadmin_streamdb -p

# Should fail (external) - this is good for security
telnet *********** 3306
```

### Step 7.3: Test Application Security

Test rate limiting:
```bash
for i in {1..20}; do curl -I https://streamdb.online/api/health; done
```

Check security headers:
```bash
curl -I https://streamdb.online
```

Should include:
- X-Frame-Options: SAMEORIGIN
- X-Content-Type-Options: nosniff
- Strict-Transport-Security

### Step 7.4: Comprehensive Testing

Test all components:
1. Website: https://streamdb.online
2. Admin Panel: https://streamdb.online/admin
3. FastPanel: https://fastpanel.streamdb.online
4. API: https://streamdb.online/api/health

### Step 7.5: Performance Testing

```bash
# Test response times
time curl -s https://streamdb.online > /dev/null

# Test concurrent connections
ab -n 100 -c 10 https://streamdb.online/
```

### Step 7.6: Security Audit

```bash
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Check for vulnerabilities
npm audit
npm audit fix

# Check logs
pm2 logs streamdb-online | grep -i error
```

---

## ✅ PHASE 8: PRODUCTION READINESS VERIFICATION

### Step 8.1: Complete Health Check

```bash
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
node production-readiness-check.js
```

### Step 8.2: Final Testing

Test complete user journey:
```bash
curl -I https://streamdb.online          # Website
curl -I https://streamdb.online/admin    # Admin Panel
curl -I https://fastpanel.streamdb.online # FastPanel
curl https://streamdb.online/api/health   # API
```

### Step 8.3: Performance Check

```bash
ssh root@***********
htop                    # Check resources
df -h                   # Check disk space
pm2 status              # Check processes
pm2 logs streamdb-online --lines 20
```

### Step 8.4: Production Readiness Checklist

**Infrastructure:**
- ✅ Reverse proxy configured and running
- ✅ Backend server secured and operational
- ✅ Firewall rules properly configured
- ✅ SSL certificates working correctly

**Database:**
- ✅ MySQL running and accessible locally only
- ✅ Database schema imported successfully
- ✅ Database user configured properly
- ✅ Connection pooling configured

**Application:**
- ✅ React frontend built and deployed
- ✅ Node.js backend running with PM2
- ✅ Environment variables configured
- ✅ File uploads working
- ✅ Admin panel accessible

**Security:**
- ✅ Backend server IP completely hidden
- ✅ HTTPS enforced on all domains
- ✅ Security headers implemented
- ✅ Rate limiting active
- ✅ FastPanel secured behind proxy

---

## 🚨 TROUBLESHOOTING GUIDE

### Issue 1: Website Shows "502 Bad Gateway"
**Cause:** Backend Node.js application not running
**Solution:**
```bash
ssh root@***********
pm2 status
pm2 restart streamdb-online
pm2 logs streamdb-online
```

### Issue 2: FastPanel Not Accessible
**Cause:** FastPanel configuration or firewall issues
**Solution:**
```bash
ssh root@***********
systemctl status fastpanel
systemctl restart fastpanel
ufw status
```

### Issue 3: Database Connection Errors
**Cause:** MySQL not running or connection issues
**Solution:**
```bash
ssh root@***********
systemctl status mysql
mysql -u dbadmin_streamdb -p streamdb_database
# Check .env file database credentials
```

### Issue 4: SSL Certificate Errors
**Cause:** Cloudflare SSL settings or Origin certificate issues
**Solution:**
1. Check Cloudflare SSL mode is "Full (strict)"
2. Verify Origin certificate on reverse proxy server
3. Clear browser cache and try again

### Issue 5: "ERR_TOO_MANY_REDIRECTS"
**Cause:** Double SSL redirect between Cloudflare and server
**Solution:**
```bash
ssh root@*************
nano /etc/nginx/sites-available/streamdb.online
# Ensure HTTP redirects to HTTPS only once
nginx -t
systemctl reload nginx
```

### Issue 6: Admin Panel Login Not Working
**Cause:** Database not seeded with admin user
**Solution:**
```bash
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
# Check .env file for ADMIN_USERNAME and ADMIN_PASSWORD
```

### Issue 7: File Uploads Failing
**Cause:** Directory permissions or file size limits
**Solution:**
```bash
ssh root@***********
chmod -R 777 /var/www/streamdb_onl_usr/data/www/streamdb.online/uploads/
```

### Issue 8: High Memory Usage
**Cause:** PM2 process memory leak
**Solution:**
```bash
ssh root@***********
pm2 restart streamdb-online
pm2 monit
```

---

## 🎯 FINAL VERIFICATION COMMANDS

Run these commands to verify everything is working:

```bash
# Test main website
curl -I https://streamdb.online

# Test admin panel
curl -I https://streamdb.online/admin

# Test FastPanel
curl -I https://fastpanel.streamdb.online

# Test API health
curl https://streamdb.online/api/health

# Check PM2 status
ssh root@*********** "pm2 status"

# Check database connection
ssh root@*********** "cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server && node test-db-connection.js"
```

**Expected Results:**
- ✅ All curl commands return 200 OK
- ✅ PM2 shows "online" status
- ✅ Database connection test passes
- ✅ No errors in any logs

---

## 🎉 DEPLOYMENT COMPLETE!

### 📋 SUMMARY

**✅ Complete Reverse Proxy Architecture:**
- Cloudflare → ************* (Reverse Proxy) → *********** (Backend)
- Backend server completely hidden from public access
- SSL termination handled by Cloudflare
- FastPanel accessible via secure subdomain

**✅ Security Implementation:**
- MySQL database local-only connections
- Firewall configured on both servers
- Rate limiting and security headers
- File upload restrictions
- Admin panel authentication

**✅ Production-Ready Application:**
- React frontend built and deployed
- Node.js backend running with PM2
- Database schema imported and configured
- Environment variables properly set
- Logging and monitoring configured

### 🌐 YOUR LIVE URLS

**Main Website:** https://streamdb.online  
**Admin Panel:** https://streamdb.online/admin  
**FastPanel:** https://fastpanel.streamdb.online  

### 🔧 MAINTENANCE COMMANDS

**Check Application Status:**
```bash
ssh root@*********** "pm2 status && systemctl status nginx && systemctl status mysql"
```

**View Application Logs:**
```bash
ssh root@*********** "pm2 logs streamdb-online"
```

**Restart Application:**
```bash
ssh root@*********** "pm2 restart streamdb-online"
```

**Update Application:**
```bash
# Build locally, upload dist folder, restart PM2
```

---

## 📞 SUPPORT INFORMATION

**If you encounter any issues:**

1. Check the troubleshooting guide above
2. Run the verification commands
3. Check PM2 and Nginx logs
4. Verify firewall and DNS settings

**Your setup is now production-ready and secure!** 🚀

The reverse proxy architecture ensures your backend server (***********) is completely hidden while providing secure access to your website, admin panel, and FastPanel through the proper domains.

---

**Document Version:** 1.0  
**Last Updated:** 2025-01-02  
**Author:** Augment Agent  
**Project:** StreamDB.online Production Deployment
