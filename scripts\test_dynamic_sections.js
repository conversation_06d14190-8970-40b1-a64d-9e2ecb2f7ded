/**
 * Test Script for Dynamic Sections API
 * Tests the sections API endpoints to ensure they work correctly
 */

const axios = require('axios');

// Configuration
const API_BASE = process.env.API_BASE || 'http://localhost:3001/api';
const TEST_ADMIN_TOKEN = process.env.TEST_ADMIN_TOKEN; // Set this if you have a test token

// Test data
const testSection = {
  name: 'Test Section',
  slug: 'test-section',
  description: 'A test section for API testing',
  icon: 'Film',
  color: '#e11d48',
  display_order: 99,
  is_active: true,
  show_in_navigation: false,
  show_on_homepage: true,
  max_items_homepage: 10,
  content_types: ['movie'],
  filter_rules: { type: 'movie' },
  category_ids: []
};

// Helper function to make authenticated requests
async function makeRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {}
    };

    if (TEST_ADMIN_TOKEN) {
      config.headers.Authorization = `Bearer ${TEST_ADMIN_TOKEN}`;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
}

// Test functions
async function testGetSections() {
  console.log('🧪 Testing GET /api/sections...');
  const result = await makeRequest('GET', '/sections');
  
  if (result.success) {
    console.log('✅ GET sections successful');
    console.log(`   Found ${result.data.data?.length || 0} sections`);
    return result.data.data || [];
  } else {
    console.log('❌ GET sections failed:', result.error);
    return [];
  }
}

async function testCreateSection() {
  console.log('🧪 Testing POST /api/sections...');
  const result = await makeRequest('POST', '/sections', testSection);
  
  if (result.success) {
    console.log('✅ CREATE section successful');
    console.log(`   Created section with ID: ${result.data.id}`);
    return result.data.id;
  } else {
    console.log('❌ CREATE section failed:', result.error);
    if (result.status === 401) {
      console.log('   💡 Authentication required - set TEST_ADMIN_TOKEN environment variable');
    }
    return null;
  }
}

async function testGetSection(id) {
  console.log(`🧪 Testing GET /api/sections/${id}...`);
  const result = await makeRequest('GET', `/sections/${id}`);
  
  if (result.success) {
    console.log('✅ GET single section successful');
    console.log(`   Section name: ${result.data.data?.name}`);
    return result.data.data;
  } else {
    console.log('❌ GET single section failed:', result.error);
    return null;
  }
}

async function testUpdateSection(id) {
  console.log(`🧪 Testing PUT /api/sections/${id}...`);
  const updateData = {
    description: 'Updated test section description',
    max_items_homepage: 15
  };
  
  const result = await makeRequest('PUT', `/sections/${id}`, updateData);
  
  if (result.success) {
    console.log('✅ UPDATE section successful');
    return true;
  } else {
    console.log('❌ UPDATE section failed:', result.error);
    return false;
  }
}

async function testGetSectionContent(id) {
  console.log(`🧪 Testing GET /api/sections/${id}/content...`);
  const result = await makeRequest('GET', `/sections/${id}/content`);
  
  if (result.success) {
    console.log('✅ GET section content successful');
    console.log(`   Found ${result.data.data?.length || 0} content items`);
    return result.data.data || [];
  } else {
    console.log('❌ GET section content failed:', result.error);
    return [];
  }
}

async function testDeleteSection(id) {
  console.log(`🧪 Testing DELETE /api/sections/${id}...`);
  const result = await makeRequest('DELETE', `/sections/${id}`);
  
  if (result.success) {
    console.log('✅ DELETE section successful');
    return true;
  } else {
    console.log('❌ DELETE section failed:', result.error);
    return false;
  }
}

async function testReorderSections(sections) {
  console.log('🧪 Testing PUT /api/sections/reorder...');
  
  if (sections.length < 2) {
    console.log('⏭️  Skipping reorder test - need at least 2 sections');
    return true;
  }

  const reorderData = {
    sections: sections.slice(0, 2).map((section, index) => ({
      id: section.id,
      display_order: index + 100
    }))
  };
  
  const result = await makeRequest('PUT', '/sections/reorder', reorderData);
  
  if (result.success) {
    console.log('✅ REORDER sections successful');
    return true;
  } else {
    console.log('❌ REORDER sections failed:', result.error);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Dynamic Sections API Tests');
  console.log('=====================================\n');

  let createdSectionId = null;
  let allSections = [];

  try {
    // Test 1: Get all sections
    allSections = await testGetSections();
    console.log('');

    // Test 2: Create a new section (requires auth)
    createdSectionId = await testCreateSection();
    console.log('');

    // Test 3: Get single section
    if (createdSectionId) {
      await testGetSection(createdSectionId);
    } else if (allSections.length > 0) {
      await testGetSection(allSections[0].id);
    }
    console.log('');

    // Test 4: Update section (requires auth)
    if (createdSectionId) {
      await testUpdateSection(createdSectionId);
    }
    console.log('');

    // Test 5: Get section content
    const testSectionId = createdSectionId || (allSections.length > 0 ? allSections[0].id : null);
    if (testSectionId) {
      await testGetSectionContent(testSectionId);
    }
    console.log('');

    // Test 6: Reorder sections (requires auth)
    if (allSections.length > 0) {
      await testReorderSections(allSections);
    }
    console.log('');

    // Test 7: Delete test section (cleanup)
    if (createdSectionId) {
      await testDeleteSection(createdSectionId);
    }

  } catch (error) {
    console.error('💥 Test runner error:', error);
  }

  console.log('\n=====================================');
  console.log('🏁 Dynamic Sections API Tests Complete');
  
  if (!TEST_ADMIN_TOKEN) {
    console.log('\n💡 Note: Some tests were skipped due to missing authentication.');
    console.log('   Set TEST_ADMIN_TOKEN environment variable to test authenticated endpoints.');
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Dynamic Sections API Test Script');
  console.log('');
  console.log('Usage: node test_dynamic_sections.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('');
  console.log('Environment Variables:');
  console.log('  API_BASE           API base URL (default: http://localhost:3001/api)');
  console.log('  TEST_ADMIN_TOKEN   Admin JWT token for authenticated tests');
  console.log('');
  console.log('Examples:');
  console.log('  node test_dynamic_sections.js');
  console.log('  API_BASE=https://your-domain.com/api node test_dynamic_sections.js');
  console.log('  TEST_ADMIN_TOKEN=your-jwt-token node test_dynamic_sections.js');
  process.exit(0);
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});
