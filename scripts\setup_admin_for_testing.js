/**
 * Setup Admin User for Testing
 * Creates an admin user in the database so you can test the new admin panel features
 */

import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'dbadmin_streamdb',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'streamdb_database',
  socketPath: process.env.DB_SOCKET || '/var/run/mysqld/mysqld.sock',
  multipleStatements: true
};

// Admin user configuration from environment
const adminConfig = {
  username: process.env.ADMIN_USERNAME || 'streamdb_admin',
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  password: process.env.ADMIN_PASSWORD || 'Ohdamn@Ufoundme2'
};

async function setupAdminUser() {
  let connection;
  
  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database successfully');

    // Check if admin_users table exists
    console.log('🔍 Checking if admin_users table exists...');
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'admin_users'
    `);

    if (tables.length === 0) {
      console.log('📝 Creating admin_users table...');
      await connection.execute(`
        CREATE TABLE admin_users (
          id INT PRIMARY KEY AUTO_INCREMENT,
          username VARCHAR(50) NOT NULL UNIQUE,
          email VARCHAR(100) NOT NULL UNIQUE,
          password_hash VARCHAR(255) NOT NULL,
          role ENUM('admin', 'moderator') DEFAULT 'admin',
          is_active BOOLEAN DEFAULT TRUE,
          last_login TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          INDEX idx_username (username),
          INDEX idx_email (email),
          INDEX idx_active (is_active)
        )
      `);
      console.log('✅ admin_users table created successfully');
    } else {
      console.log('✅ admin_users table already exists');
    }

    // Check if admin user already exists
    console.log('🔍 Checking if admin user already exists...');
    const [existingUsers] = await connection.execute(
      'SELECT id, username, email FROM admin_users WHERE username = ? OR email = ?',
      [adminConfig.username, adminConfig.email]
    );

    if (existingUsers.length > 0) {
      console.log('⚠️  Admin user already exists:');
      existingUsers.forEach(user => {
        console.log(`   - ID: ${user.id}, Username: ${user.username}, Email: ${user.email}`);
      });
      
      console.log('\n🔄 Updating existing admin user password...');
      const hashedPassword = await bcrypt.hash(adminConfig.password, 12);
      
      await connection.execute(`
        UPDATE admin_users 
        SET password_hash = ?, 
            failed_login_attempts = 0, 
            locked_until = NULL,
            is_active = TRUE,
            updated_at = CURRENT_TIMESTAMP
        WHERE username = ?
      `, [hashedPassword, adminConfig.username]);
      
      console.log('✅ Admin user password updated successfully');
    } else {
      console.log('📝 Creating new admin user...');
      const hashedPassword = await bcrypt.hash(adminConfig.password, 12);
      
      const [result] = await connection.execute(`
        INSERT INTO admin_users (username, email, password_hash, role, is_active)
        VALUES (?, ?, ?, 'admin', TRUE)
      `, [adminConfig.username, adminConfig.email, hashedPassword]);
      
      console.log(`✅ Admin user created successfully with ID: ${result.insertId}`);
    }

    // Run the dynamic sections migration if needed
    console.log('🔍 Checking if dynamic sections are set up...');
    const [sections] = await connection.execute('SHOW TABLES LIKE "content_sections"');
    
    if (sections.length === 0) {
      console.log('📝 Setting up dynamic sections...');
      
      // Read and execute the migration
      const fs = require('fs').promises;
      const path = require('path');
      const migrationPath = path.join(__dirname, '..', 'database', 'migrations', '001_add_dynamic_sections.sql');
      
      try {
        const migrationSQL = await fs.readFile(migrationPath, 'utf8');
        await connection.execute(migrationSQL);
        console.log('✅ Dynamic sections migration completed');
      } catch (migrationError) {
        console.log('⚠️  Could not run dynamic sections migration automatically');
        console.log('   Please run: cd database && node run_migration.js');
      }
    } else {
      console.log('✅ Dynamic sections already set up');
    }

    // Verify the setup
    console.log('\n🔍 Verifying setup...');
    
    // Check admin user
    const [adminUsers] = await connection.execute(
      'SELECT id, username, email, role, is_active, created_at FROM admin_users WHERE username = ?',
      [adminConfig.username]
    );
    
    if (adminUsers.length > 0) {
      const admin = adminUsers[0];
      console.log('✅ Admin user verified:');
      console.log(`   - Username: ${admin.username}`);
      console.log(`   - Email: ${admin.email}`);
      console.log(`   - Role: ${admin.role}`);
      console.log(`   - Active: ${admin.is_active ? 'Yes' : 'No'}`);
      console.log(`   - Created: ${admin.created_at}`);
    }

    // Check content sections
    const [contentSections] = await connection.execute('SELECT COUNT(*) as count FROM content_sections');
    console.log(`✅ Content sections: ${contentSections[0].count} sections available`);

    // Check content table
    const [contentCount] = await connection.execute('SELECT COUNT(*) as count FROM content');
    console.log(`✅ Content items: ${contentCount[0].count} items in database`);

    console.log('\n🎉 Setup completed successfully!');
    console.log('\n📋 Testing Instructions:');
    console.log('1. Start your server: npm run dev (or pm2 restart index)');
    console.log('2. Visit: http://localhost:5173/admin (or your domain/admin)');
    console.log('3. Login with:');
    console.log(`   - Username: ${adminConfig.username}`);
    console.log(`   - Password: ${adminConfig.password}`);
    console.log('4. Test the new features:');
    console.log('   - Add New Content (with section selection)');
    console.log('   - Manage Content (database-integrated)');
    console.log('   - Manage Sections (new dynamic sections feature)');
    console.log('5. Check homepage for dynamic sections');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Database connection refused. Please check:');
      console.error('  - Database server is running');
      console.error('  - Connection credentials in .env file');
      console.error('  - Socket path is accessible');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Access denied. Please check:');
      console.error('  - Database username and password in .env');
      console.error('  - User has necessary permissions');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 Database not found. Please check:');
      console.error('  - Database name in .env file');
      console.error('  - Database exists on server');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('Setup Admin User for Testing');
  console.log('');
  console.log('This script will:');
  console.log('1. Create admin_users table if it doesn\'t exist');
  console.log('2. Create or update admin user for testing');
  console.log('3. Verify dynamic sections are set up');
  console.log('4. Provide testing instructions');
  console.log('');
  console.log('Usage: node setup_admin_for_testing.js');
  console.log('');
  console.log('Environment Variables (from .env):');
  console.log('  DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_SOCKET');
  console.log('  ADMIN_USERNAME, ADMIN_EMAIL, ADMIN_PASSWORD');
  process.exit(0);
}

// Run the setup
console.log('🚀 Setting up admin user for testing...');
console.log('=====================================');
setupAdminUser().catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});
