/**
 * Login Page Component
 * Secure login interface with form validation and error handling
 */

import { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Eye,
  EyeOff,
  Lock,
  User,
  Shield,
  ArrowLeft,
  AlertTriangle,
  Loader2,
  Mail,
  Key
} from 'lucide-react';
import { scrollToTop } from '@/utils/scrollToTop';
// Temporarily removed AdminSetupModal to fix build issues
// import AdminSetupModal from '@/components/admin/AdminSetupModal';
// import useAdminSetup from '@/hooks/useAdminSetup';

export default function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const { authState, login, checkAuthStatus } = useAuth();
  // Temporarily removed useAdminSetup to fix build issues
  // const { needsSetup, isLoading: setupLoading, refetchSetupStatus } = useAdminSetup();

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Modal states
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetEmailSent, setResetEmailSent] = useState(false);
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [needsSetup, setNeedsSetup] = useState(false);
  const [setupLoading, setSetupLoading] = useState(true);

  // Get redirect path from location state or default to admin panel
  const redirectTo = (location.state as any)?.from?.pathname || '/admin';

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuth = async () => {
      const isAuthenticated = await checkAuthStatus();
      if (isAuthenticated) {
        navigate(redirectTo, { replace: true });
      }
    };

    checkAuth();
  }, [checkAuthStatus, navigate, redirectTo]);

  // Check setup status
  useEffect(() => {
    const checkSetupStatus = async () => {
      try {
        const response = await fetch('/api/auth/setup-status');
        const data = await response.json();
        setNeedsSetup(data.needsSetup);
      } catch (error) {
        console.error('Setup status check failed:', error);
        setNeedsSetup(false);
      } finally {
        setSetupLoading(false);
      }
    };

    checkSetupStatus();
  }, []);



  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSubmitting(true);

    try {
      const result = await login(formData);
      
      if (result.success) {
        // Redirect to intended page or admin panel
        navigate(redirectTo, { replace: true });
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle admin setup completion (simplified)
  const handleSetupComplete = () => {
    setNeedsSetup(false);
  };

  // Handle password reset request
  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!resetEmail.trim()) {
      return;
    }

    setIsResettingPassword(true);

    try {
      const response = await fetch('/api/auth/request-password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: resetEmail.trim() }),
      });

      const data = await response.json();

      if (response.ok) {
        setResetEmailSent(true);
      } else {
        console.error('Password reset request failed:', data.message);
      }
    } catch (error) {
      console.error('Password reset request error:', error);
    } finally {
      setIsResettingPassword(false);
    }
  };



  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header with back button */}
      <div className="w-full p-4 border-b border-border">
        <div className="max-w-md mx-auto flex items-center justify-between">
          <Link 
            to="/" 
            onClick={scrollToTop}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="text-sm">Back to Home</span>
          </Link>
          <Badge variant="outline" className="flex items-center gap-1">
            <Shield className="w-3 h-3" />
            <span className="text-xs">Secure Login</span>
          </Badge>
        </div>
      </div>

      {/* Main login content */}
      <div className="flex-1 flex items-center justify-center p-3 sm:p-4">
        <Card className="w-full max-w-md mx-2 sm:mx-0">
          <CardHeader className="text-center px-4 sm:px-6">
            <div className="mx-auto mb-4 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Lock className="w-6 h-6 text-primary" />
            </div>
            <CardTitle className="text-xl sm:text-2xl font-bold">Admin Login</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Enter your credentials to access the Admin Panel
            </CardDescription>
          </CardHeader>

          <CardContent className="px-4 sm:px-6">


            {/* Login error */}
            {authState.error && (
              <Alert className="mb-4 sm:mb-6 border-destructive/50 bg-destructive/10">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <AlertDescription className="text-destructive text-sm">
                  {authState.error}
                </AlertDescription>
              </Alert>
            )}

            {/* Setup notice */}
            {needsSetup && (
              <Alert className="mb-4 border-primary/50 bg-primary/10">
                <Shield className="h-4 w-4 text-primary" />
                <AlertDescription className="text-primary text-sm">
                  <strong>Admin Setup Required:</strong> No admin account exists.
                  <a
                    href="/manual-admin-setup.html"
                    className="ml-1 underline hover:no-underline font-medium"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Create admin account here
                  </a>
                </AlertDescription>
              </Alert>
            )}

            {/* Login form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Username field */}
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="username"
                    name="username"
                    type="text"
                    placeholder="Enter your username"
                    value={formData.username}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                    className="pl-10 min-h-[44px] text-base"
                    required
                  />
                </div>
              </div>

              {/* Password field */}
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                    className="pl-10 pr-10 min-h-[44px] text-base"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isSubmitting}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* Submit button */}
              <Button
                type="submit"
                className="w-full min-h-[48px] text-base"
                disabled={isSubmitting || !formData.username || !formData.password}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <Lock className="w-4 h-4 mr-2" />
                    Sign In
                  </>
                )}
              </Button>
            </form>

            {/* Password Reset Link */}
            <div className="mt-4 text-center">
              <button
                type="button"
                onClick={() => setShowPasswordReset(true)}
                className="text-sm text-primary hover:text-primary/80 transition-colors"
                disabled={needsSetup}
              >
                Forgot your password?
              </button>
            </div>

            {/* Security notice */}
            <div className="mt-6 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-start gap-2">
                <Shield className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                <div className="text-xs text-muted-foreground">
                  <p className="font-medium mb-1">Security Notice:</p>
                  <ul className="space-y-1">
                    <li>• Sessions expire after 24 hours</li>
                    <li>• Secure authentication system</li>
                    <li>• All login attempts are logged</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <div className="w-full p-4 border-t border-border">
        <div className="max-w-md mx-auto text-center">
          <p className="text-xs text-muted-foreground">
            StreamDB Admin Panel • Secure Authentication System
          </p>
        </div>
      </div>

      {/* Admin Setup Notice */}
      {needsSetup && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Admin Setup Required
              </CardTitle>
              <CardDescription>
                No admin account exists. Please create one to access the admin panel.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <AlertTriangle className="w-4 h-4" />
                <AlertDescription>
                  Use the manual setup page to create your admin account:
                  <br />
                  <a
                    href="/manual-admin-setup.html"
                    className="text-primary hover:underline font-medium"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Open Manual Setup Page
                  </a>
                </AlertDescription>
              </Alert>

              <Button
                onClick={() => setNeedsSetup(false)}
                variant="outline"
                className="w-full"
              >
                I'll Set Up Later
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Password Reset Modal */}
      {showPasswordReset && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                Reset Password
              </CardTitle>
              <CardDescription>
                Enter your email address to receive a password reset link
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!resetEmailSent ? (
                <form onSubmit={handlePasswordReset} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="resetEmail">Email Address</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                      <Input
                        id="resetEmail"
                        type="email"
                        placeholder="Enter your email address"
                        value={resetEmail}
                        onChange={(e) => setResetEmail(e.target.value)}
                        disabled={isResettingPassword}
                        className="pl-10 min-h-[44px] text-base"
                        required
                      />
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setShowPasswordReset(false);
                        setResetEmail('');
                        setResetEmailSent(false);
                      }}
                      disabled={isResettingPassword}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isResettingPassword || !resetEmail.trim()}
                      className="flex-1"
                    >
                      {isResettingPassword ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Mail className="w-4 h-4 mr-2" />
                          Send Reset Link
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <Alert>
                    <Mail className="w-4 h-4" />
                    <AlertDescription>
                      If an admin account exists with this email address, a password reset link has been sent.
                      Please check your email and follow the instructions to reset your password.
                    </AlertDescription>
                  </Alert>

                  <Button
                    onClick={() => {
                      setShowPasswordReset(false);
                      setResetEmail('');
                      setResetEmailSent(false);
                    }}
                    className="w-full"
                  >
                    Close
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
