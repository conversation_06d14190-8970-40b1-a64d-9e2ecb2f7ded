#!/bin/bash

# StreamDB Database Diagnostic System Setup Script
# Automated setup and verification for the comprehensive database diagnostic system
# 
# This script:
# - Verifies system requirements
# - Tests database connectivity
# - Runs initial diagnostic
# - Sets up monitoring service
# - Configures automated health checks
#
# Usage: bash setup-database-diagnostic.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo ""
    echo "🔍 StreamDB Database Diagnostic System Setup"
    echo "=============================================="
    echo "🏗️  Infrastructure: Two-tier offshore VPS"
    echo "🔒 Security: MySQL socket connection"
    echo "📊 Features: Health monitoring, schema validation, error classification"
    echo ""
}

# Check if we're in the correct directory
check_directory() {
    log_info "Checking project directory..."
    
    if [[ ! -f "server/services/database-diagnostic.js" ]]; then
        log_error "Database diagnostic files not found. Please run this script from the project root directory."
        exit 1
    fi
    
    if [[ ! -f ".env" ]]; then
        log_error ".env file not found. Please ensure environment configuration is set up."
        exit 1
    fi
    
    log_success "Project directory structure verified"
}

# Check Node.js and dependencies
check_dependencies() {
    log_info "Checking system dependencies..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    log_success "Node.js found: $NODE_VERSION"
    
    # Check if we're in server directory or project root
    if [[ -f "package.json" ]]; then
        PACKAGE_DIR="."
    elif [[ -f "server/package.json" ]]; then
        PACKAGE_DIR="server"
    else
        log_error "package.json not found. Please ensure dependencies are installed."
        exit 1
    fi
    
    # Check required dependencies
    log_info "Checking required Node.js dependencies..."
    
    cd "$PACKAGE_DIR"
    
    if ! npm list mysql2 &> /dev/null; then
        log_warning "mysql2 dependency not found. Installing..."
        npm install mysql2
    fi
    
    if ! npm list dotenv &> /dev/null; then
        log_warning "dotenv dependency not found. Installing..."
        npm install dotenv
    fi
    
    cd - > /dev/null
    log_success "Dependencies verified"
}

# Test database connectivity
test_database_connection() {
    log_info "Testing database connectivity..."
    
    # Run basic connectivity test
    if node -e "
        require('dotenv').config();
        const mysql = require('mysql2/promise');
        
        async function test() {
            try {
                const config = {
                    user: process.env.DB_USER,
                    password: process.env.DB_PASSWORD,
                    database: process.env.DB_NAME,
                    charset: 'utf8mb4'
                };
                
                if (process.env.DB_SOCKET) {
                    config.socketPath = process.env.DB_SOCKET;
                } else {
                    config.host = process.env.DB_HOST || 'localhost';
                    config.port = process.env.DB_PORT || 3306;
                }
                
                const connection = await mysql.createConnection(config);
                await connection.execute('SELECT 1');
                await connection.end();
                
                console.log('Database connection successful');
            } catch (error) {
                console.error('Database connection failed:', error.message);
                process.exit(1);
            }
        }
        
        test();
    "; then
        log_success "Database connection test passed"
    else
        log_error "Database connection test failed. Please check your database configuration."
        exit 1
    fi
}

# Run initial diagnostic
run_initial_diagnostic() {
    log_info "Running initial database diagnostic..."
    
    if node server/scripts/run-database-diagnostic.js --quick; then
        log_success "Initial diagnostic completed successfully"
    else
        log_warning "Initial diagnostic found issues. Check the output above for recommendations."
        echo ""
        log_info "You can run a full diagnostic later with:"
        echo "  node server/scripts/run-database-diagnostic.js --full --save-report"
    fi
}

# Test API endpoints
test_api_endpoints() {
    log_info "Testing diagnostic API endpoints..."
    
    # Check if server is running by testing health endpoint
    if curl -s -f "http://localhost:3001/api/database/health" > /dev/null 2>&1; then
        log_success "Database health API endpoint is accessible"
        
        # Test health endpoint response
        HEALTH_RESPONSE=$(curl -s "http://localhost:3001/api/database/health")
        if echo "$HEALTH_RESPONSE" | grep -q '"healthy":true'; then
            log_success "Database health check API reports healthy status"
        else
            log_warning "Database health check API reports issues"
        fi
    else
        log_warning "Server not running or API endpoints not accessible"
        log_info "Start your server and test endpoints manually:"
        echo "  curl http://localhost:3001/api/database/health"
    fi
}

# Setup cron job for automated diagnostics
setup_automated_diagnostics() {
    log_info "Setting up automated diagnostic checks..."
    
    PROJECT_ROOT=$(pwd)
    CRON_COMMAND="0 6 * * * cd $PROJECT_ROOT && node server/scripts/run-database-diagnostic.js --full --save-report >> /var/log/streamdb-diagnostic.log 2>&1"
    
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -q "run-database-diagnostic.js"; then
        log_warning "Automated diagnostic cron job already exists"
    else
        log_info "Would you like to set up daily automated diagnostics? (y/n)"
        read -r response
        
        if [[ "$response" =~ ^[Yy]$ ]]; then
            # Add cron job
            (crontab -l 2>/dev/null; echo "$CRON_COMMAND") | crontab -
            log_success "Daily diagnostic cron job added (runs at 6:00 AM)"
        else
            log_info "Skipping automated diagnostic setup"
            log_info "You can manually add this cron job later:"
            echo "  $CRON_COMMAND"
        fi
    fi
}

# Create logs directory
setup_logging() {
    log_info "Setting up logging directory..."
    
    mkdir -p server/logs
    touch server/logs/database-diagnostic.log
    touch server/logs/database-monitor.log
    
    log_success "Logging directory created: server/logs/"
}

# Show usage examples
show_usage_examples() {
    echo ""
    echo "🎯 SETUP COMPLETE! Here's how to use the diagnostic system:"
    echo ""
    echo "📋 Command Line Usage:"
    echo "  # Quick health check"
    echo "  node server/scripts/run-database-diagnostic.js --quick"
    echo ""
    echo "  # Full diagnostic with report"
    echo "  node server/scripts/run-database-diagnostic.js --full --save-report"
    echo ""
    echo "  # Schema validation only"
    echo "  node server/scripts/run-database-diagnostic.js --schema-only"
    echo ""
    echo "  # Run interactive demo"
    echo "  node server/scripts/demo-diagnostic-system.js"
    echo ""
    echo "🌐 API Endpoints (require admin authentication):"
    echo "  GET  /api/database/health           - Quick health check"
    echo "  GET  /api/database/diagnostic       - Full diagnostic"
    echo "  GET  /api/database/schema          - Schema validation"
    echo "  POST /api/database/monitor/start   - Start monitoring"
    echo "  GET  /api/database/monitor/status  - Monitor status"
    echo ""
    echo "📚 Documentation:"
    echo "  See DATABASE_DIAGNOSTIC_SYSTEM.md for complete documentation"
    echo ""
    echo "🔍 Next Steps:"
    echo "  1. Run the demo: node server/scripts/demo-diagnostic-system.js"
    echo "  2. Review any diagnostic recommendations"
    echo "  3. Set up monitoring in your application"
    echo "  4. Configure alerting for critical issues"
    echo ""
}

# Main setup function
main() {
    print_header
    
    check_directory
    check_dependencies
    test_database_connection
    setup_logging
    run_initial_diagnostic
    test_api_endpoints
    setup_automated_diagnostics
    
    log_success "Database diagnostic system setup completed successfully!"
    show_usage_examples
}

# Handle script interruption
trap 'echo ""; log_warning "Setup interrupted by user"; exit 1' INT TERM

# Run main function
main "$@"
