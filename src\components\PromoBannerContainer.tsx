import React from "react";
import TelegramBanner from "./TelegramBanner";
import CloudflareWarpBanner from "./CloudflareWarpBanner";

export default function PromoBannerContainer() {
  return (
    <div className="w-full max-w-7xl mx-auto px-0">
      {/* Mobile: Stacked layout, Tablet+: Side-by-side layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 lg:gap-6">
        {/* Telegram Banner */}
        <div className="w-full">
          <div className="transform scale-95 md:scale-90 lg:scale-85 origin-center">
            <TelegramBanner />
          </div>
        </div>

        {/* Cloudflare WARP Banner */}
        <div className="w-full">
          <div className="transform scale-95 md:scale-90 lg:scale-85 origin-center">
            <CloudflareWarpBanner />
          </div>
        </div>
      </div>
    </div>
  );
}
