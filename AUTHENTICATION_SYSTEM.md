# StreamDB Authentication System Documentation

## Overview

The StreamDB Authentication System provides secure access control for the Admin Panel with comprehensive security features including session management, brute force protection, and audit logging.

## 🔐 Current Implementation

### Development Mode Features

- **Demo Credentials**: `admin` / `streamdb2024`
- **Client-Side Authentication**: Secure session management with encryption
- **Session Fingerprinting**: Browser-based session validation
- **Brute Force Protection**: Account lockout after failed attempts
- **Security Logging**: Comprehensive audit trail
- **Mobile Responsive**: Works on all device sizes

### Security Features

1. **Session Management**
   - Encrypted session storage
   - Automatic session expiry (24 hours)
   - Activity-based session refresh
   - Session fingerprinting for security

2. **Brute Force Protection**
   - Maximum 5 login attempts
   - 15-minute account lockout
   - IP-based tracking
   - Progressive lockout periods

3. **Input Validation**
   - XSS protection
   - Input sanitization
   - Credential validation
   - SQL injection prevention

4. **Audit Logging**
   - All authentication events logged
   - Security violation tracking
   - Failed attempt monitoring
   - Session activity logging

## 🚀 Getting Started

### 1. Access the Admin Panel

1. Navigate to your website
2. Click "Admin Panel" in the footer
3. You'll be redirected to the login page
4. Use the demo credentials:
   - **Username**: `admin`
   - **Password**: `streamdb2024`

### 2. Admin Panel Features

Once logged in, you'll have access to:
- Content management (add/edit/delete movies and series)
- Bulk operations (CSV upload/export)
- TMDB integration
- Player testing
- Authentication system testing

### 3. Session Management

- Sessions last 24 hours by default
- Automatic logout on inactivity
- Session status displayed in admin panel
- Manual logout available

## 🛡️ Security Architecture

### Client-Side Security

```typescript
// Session encryption and validation
SecureSessionManager.createSession(sessionData);
SessionFingerprint.validate(storedFingerprint);
LoginAttemptTracker.recordAttempt(success);
```

### Authentication Flow

1. **Login Request**: Credentials validated and sanitized
2. **Session Creation**: Encrypted session with fingerprint
3. **Route Protection**: All admin routes require authentication
4. **Activity Monitoring**: User activity tracked for security
5. **Automatic Logout**: Session expires or security violation detected

### Protected Routes

- `/admin` - Main admin panel
- `/admin/player-test` - Video player testing
- All admin API endpoints (when server-side implemented)

## 🔧 Configuration

### Environment Variables

```env
# Authentication Configuration
VITE_AUTH_SESSION_TIMEOUT=86400000    # 24 hours

# Security Configuration
VITE_ENABLE_SECURITY_LOGGING=true
VITE_ENABLE_BRUTE_FORCE_PROTECTION=true

# Development Settings
VITE_DEV_MODE=true
VITE_ENABLE_DEMO_CREDENTIALS=true
```

### Customization Options

1. **Session Timeout**: Adjust `VITE_AUTH_SESSION_TIMEOUT`
2. **Login Attempts**: Modify `VITE_AUTH_MAX_LOGIN_ATTEMPTS`
3. **Lockout Duration**: Change `VITE_AUTH_LOCKOUT_DURATION`
4. **Security Logging**: Toggle `VITE_ENABLE_SECURITY_LOGGING`

## 🧪 Testing

### Built-in Test Suite

The admin panel includes a comprehensive test suite:

1. **Access Admin Panel**: Login with demo credentials
2. **Click "Test Authentication"**: Runs all security tests
3. **View Results**: Check console for detailed test results

### Test Categories

1. **Configuration Tests**: Verify settings are loaded correctly
2. **Validation Tests**: Test input validation and XSS protection
3. **Session Tests**: Verify session creation, retrieval, and destruction
4. **Security Tests**: Test brute force protection and logging
5. **Authentication Flow**: End-to-end authentication testing

### Manual Testing Checklist

- [ ] Login with correct credentials
- [ ] Login with incorrect credentials
- [ ] Test account lockout (5 failed attempts)
- [ ] Test session expiry
- [ ] Test logout functionality
- [ ] Test protected route access
- [ ] Test mobile responsiveness
- [ ] Verify security logging

## 📱 Mobile Support

The authentication system is fully responsive:

- **Login Page**: Optimized for mobile screens
- **Admin Panel**: Touch-friendly interface
- **Session Management**: Works across all devices
- **Security Features**: Full functionality on mobile

## 🔄 Migration to Production

### Phase 1: Current Implementation (✅ Complete)

- Client-side authentication with demo credentials
- Secure session management
- Comprehensive security features
- Mobile responsive design

### Phase 2: Server-Side Authentication (📋 Ready for Implementation)

See `SERVER_IMPLEMENTATION_GUIDE.md` for:
- Database setup instructions
- Server-side API implementation
- Production security measures
- Deployment guidelines

### Phase 3: Advanced Features (🔮 Future)

- Multi-factor authentication
- Role-based permissions
- Advanced audit logging
- Real-time security monitoring

## 🚨 Security Considerations

### Current Limitations

1. **Demo Credentials**: Hardcoded for development
2. **Client-Side Storage**: Session data stored in browser
3. **No Server Validation**: Authentication happens client-side

### Production Requirements

1. **Server-Side Authentication**: Move to database-backed auth
2. **HTTPS Only**: Encrypt all communications
3. **Environment Variables**: Secure credential storage
4. **Regular Updates**: Keep dependencies updated

### Best Practices

1. **Change Default Credentials**: Never use demo credentials in production
2. **Enable HTTPS**: Always use secure connections
3. **Monitor Logs**: Regularly review security logs
4. **Update Regularly**: Keep system updated
5. **Backup Strategy**: Secure backup of authentication data

## 📊 Monitoring and Analytics

### Security Metrics

- Login success/failure rates
- Account lockout frequency
- Session duration analytics
- Security violation patterns

### Available Logs

1. **Authentication Events**: Login, logout, session events
2. **Security Violations**: Failed attempts, suspicious activity
3. **Session Management**: Creation, expiry, refresh events
4. **System Events**: Configuration changes, errors

### Log Analysis

```javascript
// View security logs in browser console
import { SecurityLogger } from '@/utils/authUtils';
const logs = SecurityLogger.getLogs();
console.table(logs);
```

## 🆘 Troubleshooting

### Common Issues

1. **Can't Login**: Check demo credentials are correct
2. **Session Expires**: Normal behavior after 24 hours
3. **Account Locked**: Wait 15 minutes or clear browser storage
4. **Mobile Issues**: Ensure responsive design is working

### Debug Mode

Enable debug logging in development:

```javascript
// Check authentication configuration
import { logAuthConfig } from '@/config/auth';
logAuthConfig();
```

### Reset Authentication

To reset the authentication system:

1. Clear browser storage (localStorage + sessionStorage)
2. Refresh the page
3. Try logging in again

## 📞 Support

For issues or questions:

1. Check this documentation
2. Review the troubleshooting section
3. Check browser console for errors
4. Test with the built-in test suite

## 🔗 Related Documentation

- `DATABASE_SETUP_GUIDE.md` - Database configuration
- `SERVER_IMPLEMENTATION_GUIDE.md` - Server-side setup
- `SECURITY_IMPLEMENTATION.md` - Security features overview

---

**Note**: This system is designed for development and testing. For production use, implement server-side authentication as described in the server implementation guide.
