#!/usr/bin/env node

/**
 * Simple Database Initialization Script
 * Creates deployment tables without using prepared statements or stored procedures
 */

const mysql = require('mysql2');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+00:00',
  multipleStatements: true
};

// Add socket path if specified (for local connections)
if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
  dbConfig.socketPath = process.env.DB_SOCKET;
  delete dbConfig.host;
  delete dbConfig.port;
  console.log('Using socket connection for production');
} else {
  console.log(`Using TCP connection: ${dbConfig.host}:${dbConfig.port}`);
}

const createTablesSQL = `
-- Table for storing deployment attempts and their status
CREATE TABLE IF NOT EXISTS deployments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status ENUM('STARTED', 'SUCCESS', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'STARTED',
    branch VARCHAR(100) NOT NULL DEFAULT 'main',
    commits INT NOT NULL DEFAULT 0,
    pusher VARCHAR(100),
    repository VARCHAR(200),
    ip_address VARCHAR(45),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_branch (branch)
);

-- Table for storing detailed deployment logs
CREATE TABLE IF NOT EXISTS deployment_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('INFO', 'WARNING', 'ERROR', 'SUCCESS', 'DEBUG') NOT NULL DEFAULT 'INFO',
    message TEXT NOT NULL,
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_level (level),
    INDEX idx_created_at (created_at)
);

-- Table for storing webhook events (for debugging and monitoring)
CREATE TABLE IF NOT EXISTS webhook_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    repository VARCHAR(200),
    branch VARCHAR(100),
    commit_sha VARCHAR(40),
    pusher VARCHAR(100),
    payload JSON,
    signature_valid BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_repository (repository),
    INDEX idx_branch (branch),
    INDEX idx_created_at (created_at),
    INDEX idx_processed (processed)
);

-- Table for manual deployments triggered from admin panel
CREATE TABLE IF NOT EXISTS manual_deployments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id INT,
    deployment_type ENUM('MANUAL', 'SCHEDULED', 'ROLLBACK') NOT NULL DEFAULT 'MANUAL',
    target_branch VARCHAR(100) NOT NULL DEFAULT 'main',
    status ENUM('RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'RUNNING',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_admin_user (admin_user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Configuration table for deployment settings
CREATE TABLE IF NOT EXISTS deployment_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
);
`;

const createViewsSQL = `
-- View for recent deployments with enhanced information
CREATE OR REPLACE VIEW recent_deployments AS
SELECT 
    d.*,
    CASE 
        WHEN d.status = 'SUCCESS' THEN '✅'
        WHEN d.status = 'FAILED' THEN '❌'
        WHEN d.status = 'STARTED' THEN '🔄'
        ELSE '⏸️'
    END as status_icon,
    TIMESTAMPDIFF(SECOND, d.created_at, COALESCE(d.completed_at, NOW())) as duration_seconds
FROM deployments d
ORDER BY d.created_at DESC;

-- View for deployment statistics
CREATE OR REPLACE VIEW deployment_stats AS
SELECT 
    COUNT(*) as total_deployments,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_deployments,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_deployments,
    SUM(CASE WHEN status = 'STARTED' THEN 1 ELSE 0 END) as running_deployments,
    ROUND(AVG(CASE WHEN status IN ('SUCCESS', 'FAILED') THEN TIMESTAMPDIFF(SECOND, created_at, completed_at) END), 2) as avg_duration_seconds,
    MAX(created_at) as last_deployment_time
FROM deployments
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- View for recent logs with deployment correlation
CREATE OR REPLACE VIEW recent_logs AS
SELECT 
    dl.*,
    d.id as deployment_id,
    d.status as deployment_status,
    d.branch as deployment_branch
FROM deployment_logs dl
LEFT JOIN deployments d ON d.created_at <= dl.created_at 
    AND ABS(TIMESTAMPDIFF(SECOND, dl.created_at, d.created_at)) <= 300
WHERE dl.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY dl.created_at DESC;
`;

const insertConfigSQL = `
-- Insert default configuration values
INSERT IGNORE INTO deployment_config (config_key, config_value, description) VALUES
('max_deployments_per_hour', '10', 'Maximum number of deployments allowed per hour'),
('deployment_timeout_seconds', '300', 'Maximum time allowed for a deployment to complete'),
('auto_cleanup_enabled', 'true', 'Whether to automatically clean up old logs and deployments'),
('cleanup_retention_days', '90', 'Number of days to retain deployment logs'),
('webhook_rate_limit', '50', 'Maximum webhook requests per 15-minute window'),
('backup_retention_count', '5', 'Number of deployment backups to retain'),
('notification_enabled', 'false', 'Whether to send deployment notifications'),
('maintenance_mode', 'false', 'Whether the deployment system is in maintenance mode');
`;

async function initializeDatabase() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    console.log('Database:', dbConfig.database);
    console.log('User:', dbConfig.user);
    
    connection = mysql.createConnection(dbConfig);
    
    // Test connection
    await new Promise((resolve, reject) => {
      connection.connect((err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    console.log('✅ Database connection established');
    
    // Create tables
    console.log('🏗️ Creating deployment tables...');
    await new Promise((resolve, reject) => {
      connection.query(createTablesSQL, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    console.log('✅ Tables created successfully');
    
    // Create views
    console.log('📊 Creating deployment views...');
    await new Promise((resolve, reject) => {
      connection.query(createViewsSQL, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    console.log('✅ Views created successfully');
    
    // Insert configuration
    console.log('⚙️ Inserting default configuration...');
    await new Promise((resolve, reject) => {
      connection.query(insertConfigSQL, (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    console.log('✅ Configuration inserted successfully');
    
    // Verify tables
    console.log('🔍 Verifying table creation...');
    const tables = await new Promise((resolve, reject) => {
      connection.query(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = ? 
        AND TABLE_NAME IN ('deployments', 'deployment_logs', 'webhook_events', 'manual_deployments', 'deployment_config')
      `, [dbConfig.database], (err, results) => {
        if (err) reject(err);
        else resolve(results);
      });
    });
    
    console.log('📊 Created tables:');
    tables.forEach(table => {
      console.log(`  ✅ ${table.TABLE_NAME}`);
    });
    
    // Test log insertion
    console.log('🧪 Testing log insertion...');
    await new Promise((resolve, reject) => {
      connection.query(
        'INSERT INTO deployment_logs (level, message, data) VALUES (?, ?, ?)',
        ['INFO', 'Database initialization completed', JSON.stringify({ 
          timestamp: new Date().toISOString(),
          tables_created: tables.length
        })],
        (err, result) => {
          if (err) reject(err);
          else resolve(result);
        }
      );
    });
    console.log('✅ Test log entry created');
    
    console.log('\n🎉 Database initialization completed successfully!');
    console.log(`📊 Tables created: ${tables.length}`);
    console.log('🚀 Webhook deployment system is ready!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n🔧 Troubleshooting:');
      console.error('  • Check if MySQL server is running');
      console.error('  • Verify database credentials in .env file');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n🔧 Troubleshooting:');
      console.error('  • Check database username and password');
      console.error('  • Verify user has proper permissions');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the initialization
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
