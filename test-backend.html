<!DOCTYPE html>
<html>
<head>
    <title>Backend Test</title>
</head>
<body>
    <h1>StreamDB Backend Test</h1>
    <p>If you can see this, the backend Nginx is working!</p>
    <script>
        // Test API connection
        fetch('/api/categories')
            .then(response => response.json())
            .then(data => {
                document.body.innerHTML += '<h2>API Test Result:</h2><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.body.innerHTML += '<h2>API Test Failed:</h2><p>' + error + '</p>';
            });
    </script>
</body>
</html>
