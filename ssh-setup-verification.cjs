#!/usr/bin/env node

/**
 * SSH Setup Verification Script for StreamDB Online Backend
 * Comprehensive testing and troubleshooting for SSH key authentication
 */

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class SSHSetupVerifier {
  constructor() {
    this.serverIP = '***********';
    this.serverUser = 'root';
    this.sshKeyPath = path.join(os.homedir(), '.ssh', 'id_rsa');
    this.publicKeyPath = path.join(os.homedir(), '.ssh', 'id_rsa.pub');
    this.results = [];
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    
    const logMessage = `${colors[type]}${icons[type]} ${message}${colors.reset}`;
    console.log(logMessage);
    this.results.push({ type, message });
  }

  async execCommand(command, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const child = exec(command, { timeout }, (error, stdout, stderr) => {
        if (error) {
          resolve({ success: false, error: error.message, stdout, stderr });
        } else {
          resolve({ success: true, stdout, stderr });
        }
      });
    });
  }

  async checkSSHKeys() {
    this.log('Checking SSH key configuration...', 'info');
    
    // Check if private key exists
    if (fs.existsSync(this.sshKeyPath)) {
      this.log('Private SSH key found', 'success');
      
      // Check key permissions
      const stats = fs.statSync(this.sshKeyPath);
      const mode = (stats.mode & parseInt('777', 8)).toString(8);
      if (mode === '600' || mode === '400') {
        this.log('Private key permissions are secure', 'success');
      } else {
        this.log(`Private key permissions: ${mode} (should be 600)`, 'warning');
      }
    } else {
      this.log('Private SSH key not found', 'error');
      return false;
    }
    
    // Check if public key exists
    if (fs.existsSync(this.publicKeyPath)) {
      this.log('Public SSH key found', 'success');
      
      // Display key fingerprint
      const result = await this.execCommand(`ssh-keygen -l -f "${this.publicKeyPath}"`);
      if (result.success) {
        this.log(`Key fingerprint: ${result.stdout.trim()}`, 'info');
      }
    } else {
      this.log('Public SSH key not found', 'error');
      return false;
    }
    
    return true;
  }

  async testBasicConnection() {
    this.log('Testing basic SSH connectivity...', 'info');
    
    const result = await this.execCommand(
      `ssh -o ConnectTimeout=10 -o BatchMode=yes ${this.serverUser}@${this.serverIP} "echo 'Connection test successful'"`,
      15000
    );
    
    if (result.success && result.stdout.includes('Connection test successful')) {
      this.log('SSH key authentication is working!', 'success');
      return true;
    } else if (result.error && result.error.includes('Permission denied')) {
      this.log('SSH key authentication failed - key may not be installed', 'warning');
      return false;
    } else if (result.error && result.error.includes('Connection timed out')) {
      this.log('Connection timed out - server may be unreachable', 'error');
      return false;
    } else {
      this.log(`Connection test failed: ${result.error || result.stderr}`, 'error');
      return false;
    }
  }

  async testPasswordConnection() {
    this.log('Testing password-based connection (for comparison)...', 'info');
    
    // This will prompt for password if key auth fails
    const result = await this.execCommand(
      `ssh -o ConnectTimeout=10 -o PreferredAuthentications=password ${this.serverUser}@${this.serverIP} "echo 'Password auth available'"`,
      20000
    );
    
    if (result.success) {
      this.log('Password authentication is available as fallback', 'info');
      return true;
    } else {
      this.log('Password authentication test inconclusive', 'warning');
      return false;
    }
  }

  async checkServerKeyInstallation() {
    this.log('Checking if public key is properly installed on server...', 'info');
    
    if (!fs.existsSync(this.publicKeyPath)) {
      this.log('Cannot check - public key file not found locally', 'error');
      return false;
    }
    
    const publicKeyContent = fs.readFileSync(this.publicKeyPath, 'utf8').trim();
    
    const result = await this.execCommand(
      `ssh ${this.serverUser}@${this.serverIP} "cat ~/.ssh/authorized_keys 2>/dev/null | grep -F '${publicKeyContent.split(' ')[1]}' && echo 'KEY_FOUND' || echo 'KEY_NOT_FOUND'"`,
      15000
    );
    
    if (result.success && result.stdout.includes('KEY_FOUND')) {
      this.log('Public key is properly installed on server', 'success');
      return true;
    } else if (result.success && result.stdout.includes('KEY_NOT_FOUND')) {
      this.log('Public key is NOT found on server', 'warning');
      return false;
    } else {
      this.log('Could not verify key installation on server', 'warning');
      return false;
    }
  }

  async installPublicKey() {
    this.log('Installing public key on server...', 'info');
    
    const result = await this.execCommand(
      `ssh-copy-id -i "${this.publicKeyPath}" ${this.serverUser}@${this.serverIP}`,
      30000
    );
    
    if (result.success) {
      this.log('Public key installation completed', 'success');
      return true;
    } else {
      this.log(`Public key installation failed: ${result.error}`, 'error');
      return false;
    }
  }

  async generateSSHConfig() {
    this.log('Generating SSH config for easier access...', 'info');
    
    const sshConfigPath = path.join(os.homedir(), '.ssh', 'config');
    const configEntry = `
# StreamDB Online Backend Server
Host streamdb-backend
    HostName ${this.serverIP}
    User ${this.serverUser}
    IdentityFile ${this.sshKeyPath}
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
`;
    
    try {
      let existingConfig = '';
      if (fs.existsSync(sshConfigPath)) {
        existingConfig = fs.readFileSync(sshConfigPath, 'utf8');
      }
      
      if (!existingConfig.includes('Host streamdb-backend')) {
        fs.appendFileSync(sshConfigPath, configEntry);
        this.log('SSH config entry added - you can now use: ssh streamdb-backend', 'success');
      } else {
        this.log('SSH config entry already exists', 'info');
      }
      
      return true;
    } catch (error) {
      this.log(`Failed to create SSH config: ${error.message}`, 'error');
      return false;
    }
  }

  async runFullDiagnostic() {
    this.log('🔐 SSH Setup Verification for StreamDB Online Backend', 'info');
    this.log('=================================================', 'info');
    
    const keysOK = await this.checkSSHKeys();
    
    if (!keysOK) {
      this.log('SSH keys need to be generated first', 'error');
      this.log('Run: ssh-keygen -t rsa -b 4096 -C "streamdb-backend"', 'info');
      return false;
    }
    
    const connectionOK = await this.testBasicConnection();
    
    if (!connectionOK) {
      this.log('Key authentication not working, checking server installation...', 'warning');
      
      const keyInstalled = await this.checkServerKeyInstallation();
      
      if (!keyInstalled) {
        this.log('Installing public key on server...', 'info');
        const installed = await this.installPublicKey();
        
        if (installed) {
          this.log('Retesting connection...', 'info');
          await this.testBasicConnection();
        }
      }
    }
    
    await this.generateSSHConfig();
    
    this.generateReport();
    return true;
  }

  generateReport() {
    this.log('=================================================', 'info');
    this.log('🎯 SSH Setup Summary', 'info');
    this.log('=================================================', 'info');
    
    const successes = this.results.filter(r => r.type === 'success').length;
    const warnings = this.results.filter(r => r.type === 'warning').length;
    const errors = this.results.filter(r => r.type === 'error').length;
    
    this.log(`✅ Successful checks: ${successes}`, 'success');
    this.log(`⚠️ Warnings: ${warnings}`, warnings > 0 ? 'warning' : 'success');
    this.log(`❌ Errors: ${errors}`, errors > 0 ? 'error' : 'success');
    
    this.log('\n🚀 Next Steps:', 'info');
    this.log('1. Test connection: ssh streamdb-backend', 'info');
    this.log('2. Run backend diagnostics: node secure-backend-connection.cjs test', 'info');
    this.log('3. Deploy your application: node secure-backend-connection.cjs deploy', 'info');
  }
}

// Run the verification
if (require.main === module) {
  const verifier = new SSHSetupVerifier();
  verifier.runFullDiagnostic().catch(error => {
    console.error('Verification failed:', error);
    process.exit(1);
  });
}

module.exports = SSHSetupVerifier;
