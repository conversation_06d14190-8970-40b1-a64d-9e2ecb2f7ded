#
# The MySQL database server configuration file.
# Optimized for StreamDB Production Environment
#
# For explanations see
# http://dev.mysql.com/doc/mysql/en/server-system-variables.html

# Here is entries for some specific programs
# The following values assume you have at least 32M ram

[mysqld]
#
# * Basic Settings
#
user            = mysql
pid-file        = /var/run/mysqld/mysqld.pid
socket          = /var/run/mysqld/mysqld.sock
port            = 3306
datadir         = /var/lib/mysql

# Security and Network Settings
bind-address            = 127.0.0.1
mysqlx-bind-address     = 127.0.0.1
skip-networking         = false
skip-name-resolve       = true

# Character Set and Collation
character-set-server    = utf8mb4
collation-server        = utf8mb4_unicode_ci

#
# * Fine Tuning for Production
#
key_buffer_size         = 32M
max_allowed_packet      = 64M
thread_stack            = 256K
thread_cache_size       = 8

# Connection Settings
max_connections         = 100
connect_timeout         = 10
wait_timeout            = 600
interactive_timeout     = 600

# InnoDB Settings for Performance
innodb_buffer_pool_size = 256M
innodb_log_file_size    = 64M
innodb_log_buffer_size  = 16M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50

# MyISAM Settings
myisam-recover-options  = BACKUP

# Table Settings
table_open_cache        = 2000
table_definition_cache  = 1400

# Query Cache (disabled in MySQL 8.0+)
# query_cache_type       = 1
# query_cache_size       = 32M

# Temporary Tables
tmp_table_size          = 32M
max_heap_table_size     = 32M

# Slow Query Log
slow_query_log          = 1
slow_query_log_file     = /var/log/mysql/mysql-slow.log
long_query_time         = 2
log-queries-not-using-indexes = 1

#
# * Logging and Replication
#
# Error log - should be very few entries.
log_error = /var/log/mysql/error.log

# Binary Logging
max_binlog_size   = 100M
binlog_expire_logs_seconds = 2592000

# General Log (disabled for performance)
# general_log_file        = /var/log/mysql/query.log
# general_log             = 1

#
# * Security Settings
#
# Disable LOAD DATA LOCAL INFILE for security
local-infile = 0

# SSL Settings (optional)
# ssl-ca = /etc/mysql/ssl/ca-cert.pem
# ssl-cert = /etc/mysql/ssl/server-cert.pem
# ssl-key = /etc/mysql/ssl/server-key.pem

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
