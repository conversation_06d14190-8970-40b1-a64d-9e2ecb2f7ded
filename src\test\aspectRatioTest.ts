/**
 * Test script for aspect ratio functionality
 * This validates the new dynamic aspect ratio detection features
 */

import { 
  detectOptimalAspectRatio, 
  getAspectRatioConfig, 
  ASPECT_RATIOS,
  PLATFORM_ASPECT_RATIOS 
} from '../utils/aspectRatio';
import { detectVideoPlatform } from '../utils/videoSecurity';

// Test URLs for different platforms
const testUrls = [
  'https://www.youtube.com/embed/dQw4w9WgXcQ',
  'https://player.vimeo.com/video/123456789',
  'https://www.dailymotion.com/embed/video/x7tgad0',
  'https://player.twitch.tv/video/123456789',
  'https://2embed.cc/embed/movie/123456',
  'https://streamable.com/e/abc123',
  'https://unknown-platform.com/embed/video123'
];

// Test different content types
const contentTypes = ['movie', 'series', 'live', 'short'];

/**
 * Test aspect ratio detection for different platforms
 */
export function testPlatformAspectRatios(): boolean {
  console.group('🎬 Testing Platform Aspect Ratio Detection');
  
  let allPassed = true;
  
  testUrls.forEach(url => {
    const platform = detectVideoPlatform(url);
    const aspectRatio = detectOptimalAspectRatio(url);
    
    console.log(`URL: ${url}`);
    console.log(`  Platform: ${platform}`);
    console.log(`  Detected Aspect Ratio: ${aspectRatio}`);
    
    // Verify aspect ratio is valid
    const isValidRatio = Object.values(ASPECT_RATIOS).includes(aspectRatio);
    if (!isValidRatio) {
      console.error(`  ❌ Invalid aspect ratio: ${aspectRatio}`);
      allPassed = false;
    } else {
      console.log(`  ✅ Valid aspect ratio`);
    }
    
    console.log('');
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test aspect ratio configuration for different content types
 */
export function testContentTypeAspectRatios(): boolean {
  console.group('📺 Testing Content Type Aspect Ratios');
  
  let allPassed = true;
  
  contentTypes.forEach(contentType => {
    testUrls.forEach(url => {
      const config = getAspectRatioConfig(url, contentType);
      
      console.log(`Content: ${contentType}, URL: ${url}`);
      console.log(`  Config:`, config);
      
      // Verify config has required properties
      if (!config.aspectRatio || !config.className || !config.style) {
        console.error(`  ❌ Invalid config structure`);
        allPassed = false;
      } else {
        console.log(`  ✅ Valid config`);
      }
    });
  });
  
  console.groupEnd();
  return allPassed;
}

/**
 * Test platform-specific aspect ratio overrides
 */
export function testPlatformOverrides(): boolean {
  console.group('🔧 Testing Platform Override Logic');
  
  let allPassed = true;
  
  // Test YouTube (should prefer 16:9)
  const youtubeRatio = detectOptimalAspectRatio('https://www.youtube.com/embed/test');
  if (youtubeRatio !== ASPECT_RATIOS.WIDESCREEN) {
    console.error(`❌ YouTube should default to 16:9, got ${youtubeRatio}`);
    allPassed = false;
  } else {
    console.log(`✅ YouTube correctly defaults to 16:9`);
  }
  
  // Test Twitch (should prefer 16:9 for streams)
  const twitchRatio = detectOptimalAspectRatio('https://player.twitch.tv/video/123');
  if (twitchRatio !== ASPECT_RATIOS.WIDESCREEN) {
    console.error(`❌ Twitch should default to 16:9, got ${twitchRatio}`);
    allPassed = false;
  } else {
    console.log(`✅ Twitch correctly defaults to 16:9`);
  }
  
  console.groupEnd();
  return allPassed;
}

/**
 * Run all aspect ratio tests
 */
export function runAllAspectRatioTests(): boolean {
  console.log('🎯 Starting Aspect Ratio Tests');
  console.log('================================');
  
  const results = [
    testPlatformAspectRatios(),
    testContentTypeAspectRatios(),
    testPlatformOverrides()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('================================');
  if (allPassed) {
    console.log('✅ All aspect ratio tests passed!');
  } else {
    console.log('❌ Some aspect ratio tests failed!');
  }
  
  return allPassed;
}

// Export for use in other test files
export default {
  testPlatformAspectRatios,
  testContentTypeAspectRatios,
  testPlatformOverrides,
  runAllAspectRatioTests
};
