#!/usr/bin/env node

/**
 * Environment File Synchronization Script
 * Ensures both .env files are synchronized while maintaining environment-specific differences
 */

const fs = require('fs');
const path = require('path');

class EnvSynchronizer {
  constructor() {
    this.rootEnvPath = '.env';
    this.serverEnvPath = 'server/.env';
    this.differences = [];
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    
    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
  }

  parseEnvFile(filePath) {
    if (!fs.existsSync(filePath)) {
      this.log(`File not found: ${filePath}`, 'error');
      return {};
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }

    return env;
  }

  compareEnvFiles() {
    this.log('Comparing environment files...', 'info');
    
    const rootEnv = this.parseEnvFile(this.rootEnvPath);
    const serverEnv = this.parseEnvFile(this.serverEnvPath);

    const allKeys = new Set([...Object.keys(rootEnv), ...Object.keys(serverEnv)]);

    for (const key of allKeys) {
      const rootValue = rootEnv[key];
      const serverValue = serverEnv[key];

      if (rootValue !== serverValue) {
        this.differences.push({
          key,
          rootValue: rootValue || 'MISSING',
          serverValue: serverValue || 'MISSING'
        });
      }
    }

    if (this.differences.length === 0) {
      this.log('Environment files are synchronized', 'success');
    } else {
      this.log(`Found ${this.differences.length} differences`, 'warning');
      this.differences.forEach(diff => {
        console.log(`  ${diff.key}:`);
        console.log(`    Root:   ${diff.rootValue}`);
        console.log(`    Server: ${diff.serverValue}`);
      });
    }
  }

  synchronizeFiles() {
    this.log('Synchronizing environment files...', 'info');
    
    if (!fs.existsSync(this.rootEnvPath)) {
      this.log('Root .env file not found', 'error');
      return;
    }

    // Copy root .env to server/.env
    const rootContent = fs.readFileSync(this.rootEnvPath, 'utf8');
    
    // Ensure server directory exists
    const serverDir = path.dirname(this.serverEnvPath);
    if (!fs.existsSync(serverDir)) {
      fs.mkdirSync(serverDir, { recursive: true });
    }

    fs.writeFileSync(this.serverEnvPath, rootContent);
    this.log('Environment files synchronized', 'success');
  }

  run() {
    this.log('Starting environment file synchronization...', 'info');
    this.compareEnvFiles();
    
    if (process.argv.includes('--sync')) {
      this.synchronizeFiles();
    } else if (this.differences.length > 0) {
      this.log('Use --sync flag to synchronize files', 'info');
    }
  }
}

// Run the synchronizer
const synchronizer = new EnvSynchronizer();
synchronizer.run();
