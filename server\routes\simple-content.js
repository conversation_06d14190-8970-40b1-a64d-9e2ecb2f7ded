const express = require('express');
const router = express.Router();
const db = require('../config/database');

// Get all content with pagination
router.get('/', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;
    const page = parseInt(req.query.page) || 1;
    const offset = (page - 1) * limit;
    const type = req.query.type;
    const category = req.query.category;

    let query = `
      SELECT
        c.id,
        c.title,
        c.description,
        c.type,
        c.year,
        c.image,
        c.poster_url as posterUrl,
        c.cover_image as coverImage,
        c.tags,
        c.imdb_rating,
        c.runtime,
        c.studio,
        c.is_published,
        c.is_featured,
        c.created_at,
        cat.name as category_name,
        cat.slug as category_slug
      FROM content c
      LEFT JOIN categories cat ON c.category = cat.slug
      WHERE c.is_published = 1
    `;

    const params = [];

    // Add type filter
    if (type) {
      query += ' AND c.type = ?';
      params.push(type);
    }

    // Add category filter
    if (category) {
      query += ' AND cat.slug = ?';
      params.push(category);
    }

    // Add ordering and pagination with hardcoded values for now
    query += ` ORDER BY c.created_at DESC LIMIT ${limit} OFFSET ${offset}`;

    const content = await db.execute(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM content c WHERE c.is_published = 1';
    const countParams = [];

    if (type) {
      countQuery += ' AND c.type = ?';
      countParams.push(type);
    }

    if (category) {
      countQuery += ' AND c.category = ?';
      countParams.push(category);
    }

    const [countResult] = await db.execute(countQuery, countParams);
    const total = countResult.total;

    res.json({
      success: true,
      data: content,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching content',
      error: error.message
    });
  }
});

module.exports = router;
