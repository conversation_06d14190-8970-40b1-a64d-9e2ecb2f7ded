# 🔧 StreamDB Automated Maintenance Quick Reference

## 📋 SYSTEM OVERVIEW

**Maintenance Schedule:** Every Thursday at Midnight
- **Backend Server (45.93.8.197)**: 00:00 - FastPanel, MySQL, PM2, StreamDB
- **Reverse Proxy (91.208.197.50)**: 00:30 - <PERSON><PERSON><PERSON>, SSL, Firewall

**Key Safety Features:**
- ✅ Service state preservation
- ✅ Automatic rollback on failures
- ✅ Configuration backups
- ✅ No automatic reboots
- ✅ Reverse proxy architecture protection

---

## 🚀 QUICK COMMANDS

### Check Maintenance Status
```bash
# View latest maintenance logs
tail -f /var/log/streamdb-maintenance/cron-backend.log     # Backend
tail -f /var/log/streamdb-maintenance/cron-proxy.log      # Proxy

# Check if maintenance is currently running
ps aux | grep maintenance
cat /var/run/streamdb-*-maintenance.lock 2>/dev/null
```

### Manual Maintenance Execution
```bash
# Backend server
sudo /usr/local/bin/backend-maintenance.sh

# Reverse proxy server  
sudo /usr/local/bin/reverse-proxy-maintenance.sh
```

### Validation and Testing
```bash
# Run full validation suite
sudo /usr/local/bin/validation-scripts.sh --full-test

# Individual tests
sudo /usr/local/bin/validation-scripts.sh --prerequisites
sudo /usr/local/bin/validation-scripts.sh --connectivity
sudo /usr/local/bin/validation-scripts.sh --syntax
sudo /usr/local/bin/validation-scripts.sh --dry-run
```

### View Maintenance History
```bash
# List all maintenance logs
ls -la /var/log/streamdb-maintenance/

# View specific maintenance session
cat /var/log/streamdb-maintenance/backend-maintenance-YYYYMMDD-HHMMSS.log
cat /var/log/streamdb-maintenance/proxy-maintenance-YYYYMMDD-HHMMSS.log
```

### Check Cron Jobs
```bash
# View scheduled maintenance
crontab -l

# Check cron service status
systemctl status cron

# View cron logs
tail -f /var/log/cron.log
```

---

## 🔍 MONITORING

### Service Health Check
```bash
# Check all critical services
systemctl status nginx mysql fastpanel
pm2 status

# Test website connectivity
curl -I https://streamdb.online
curl -I https://fastpanel.streamdb.online
curl https://streamdb.online/api/health
```

### System Resources
```bash
# Check disk space
df -h

# Check memory usage
free -h

# Check system load
htop
```

### Backup Verification
```bash
# List configuration backups
ls -la /var/backups/streamdb-maintenance/

# List database backups (backend only)
ls -la /var/backups/streamdb-maintenance/mysql-backup-*.sql.gz
```

---

## 🚨 TROUBLESHOOTING

### Maintenance Script Issues
```bash
# Check script permissions
ls -la /usr/local/bin/*maintenance.sh

# Fix permissions if needed
chmod +x /usr/local/bin/backend-maintenance.sh
chmod +x /usr/local/bin/reverse-proxy-maintenance.sh

# Test script syntax
bash -n /usr/local/bin/backend-maintenance.sh
bash -n /usr/local/bin/reverse-proxy-maintenance.sh
```

### Service Recovery
```bash
# Restart all services (backend)
systemctl restart nginx mysql fastpanel
pm2 restart streamdb-online

# Restart services (proxy)
systemctl restart nginx fail2ban
ufw --force enable
```

### Cron Job Issues
```bash
# Restart cron service
systemctl restart cron

# Check cron configuration
cat /etc/cron.d/streamdb-maintenance-env

# Manual cron test
sudo -u root /usr/local/bin/backend-maintenance.sh
```

### Lock File Issues
```bash
# Remove stale lock files
rm -f /var/run/streamdb-*-maintenance.lock

# Check for running maintenance processes
ps aux | grep maintenance
```

---

## 📅 MAINTENANCE CALENDAR

### Next Scheduled Maintenance
```bash
# Check next Thursday
date -d "next thursday"

# Calculate time until next maintenance
echo "Next maintenance: $(date -d 'next thursday 00:00')"
```

### Maintenance Windows
- **Primary**: Thursday 00:00-01:00 (Backend + Proxy)
- **Emergency**: Can be run manually anytime
- **Testing**: Use validation scripts first

---

## 📊 LOG ANALYSIS

### Common Log Patterns
```bash
# Check for successful completion
grep "SUCCESS" /var/log/streamdb-maintenance/*.log

# Check for errors
grep "ERROR" /var/log/streamdb-maintenance/*.log

# Check for warnings
grep "WARN" /var/log/streamdb-maintenance/*.log

# View maintenance summary
grep "Maintenance Completed" /var/log/streamdb-maintenance/*.log
```

### Performance Metrics
```bash
# Check maintenance duration
grep -E "(Starting maintenance|Maintenance Completed)" /var/log/streamdb-maintenance/*.log

# Check update counts
grep "updates available" /var/log/streamdb-maintenance/*.log

# Check cleanup results
grep "cleanup completed" /var/log/streamdb-maintenance/*.log
```

---

## 🔧 CONFIGURATION FILES

### Script Locations
```
/usr/local/bin/backend-maintenance.sh          # Backend maintenance
/usr/local/bin/reverse-proxy-maintenance.sh    # Proxy maintenance  
/usr/local/bin/validation-scripts.sh           # Testing/validation
/usr/local/bin/cron-configuration.sh           # Cron setup
```

### Log Locations
```
/var/log/streamdb-maintenance/cron-backend.log     # Backend cron log
/var/log/streamdb-maintenance/cron-proxy.log       # Proxy cron log
/var/log/streamdb-maintenance/backend-maintenance-*.log  # Detailed backend logs
/var/log/streamdb-maintenance/proxy-maintenance-*.log    # Detailed proxy logs
```

### Backup Locations
```
/var/backups/streamdb-maintenance/config-backup-*.tar.gz    # Config backups
/var/backups/streamdb-maintenance/mysql-backup-*.sql.gz     # Database backups
/var/backups/streamdb-maintenance/proxy-config-backup-*.tar.gz  # Proxy backups
```

### Configuration Files
```
/etc/cron.d/streamdb-maintenance-env           # Cron environment
/etc/apt/apt.conf.d/50unattended-upgrades      # Security updates config
/etc/logrotate.d/streamdb-maintenance          # Log rotation config
```

---

## ⚠️ SAFETY REMINDERS

### Before Manual Maintenance
1. **Check system load**: Ensure low traffic period
2. **Verify backups**: Confirm recent backups exist
3. **Test connectivity**: Ensure all services are healthy
4. **Run validation**: Use validation scripts first

### During Maintenance
1. **Monitor logs**: Watch for errors in real-time
2. **Check services**: Verify services remain running
3. **Avoid interruption**: Don't stop maintenance mid-process
4. **Have rollback ready**: Know how to restore from backups

### After Maintenance
1. **Verify functionality**: Test all website features
2. **Check logs**: Review maintenance logs for issues
3. **Monitor performance**: Watch system resources
4. **Document issues**: Note any problems for future reference

---

## 📞 EMERGENCY PROCEDURES

### If Maintenance Fails
```bash
# 1. Stop any running maintenance
pkill -f maintenance

# 2. Remove lock files
rm -f /var/run/streamdb-*-maintenance.lock

# 3. Restore services
systemctl restart nginx mysql fastpanel
pm2 restart streamdb-online

# 4. Check website functionality
curl -I https://streamdb.online
```

### If Website Goes Down
```bash
# 1. Check all services
systemctl status nginx mysql fastpanel
pm2 status

# 2. Check reverse proxy connectivity
ssh root@91.208.197.50 "systemctl status nginx"

# 3. Restart services if needed
systemctl restart nginx
pm2 restart streamdb-online

# 4. Check logs for errors
tail -f /var/log/nginx/error.log
pm2 logs streamdb-online
```

### Contact Information
- **VPS Provider**: [Your provider's emergency contact]
- **Cloudflare Support**: [If DNS/SSL issues]
- **Backup Contact**: [Secondary admin contact]

---

**Quick Reference Version:** 1.0  
**Last Updated:** 2025-01-02  
**For:** StreamDB.online Automated Maintenance System
