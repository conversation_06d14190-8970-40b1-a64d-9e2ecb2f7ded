<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Admin Setup - StreamDB</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0a0a;
            color: #e6cb8e;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 12px;
            padding: 30px;
            max-width: 400px;
            width: 100%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #e6cb8e;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px;
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 6px;
            color: #e6cb8e;
            font-size: 16px;
        }
        
        input:focus {
            outline: none;
            border-color: #e6cb8e;
        }
        
        button {
            width: 100%;
            padding: 12px;
            background: #e6cb8e;
            color: #0a0a0a;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #d4b876;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }
        
        .success {
            background: #1a4a1a;
            border: 1px solid #2a6a2a;
            color: #4ade80;
        }
        
        .error {
            background: #4a1a1a;
            border: 1px solid #6a2a2a;
            color: #f87171;
        }
        
        .warning {
            background: #4a3a1a;
            border: 1px solid #6a5a2a;
            color: #fbbf24;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Manual Admin Setup</h1>
        
        <div class="warning">
            <strong>⚠️ One-Time Setup</strong><br>
            This will create the ONLY admin account for StreamDB. Choose your credentials carefully.
        </div>
        
        <form id="setupForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm Password:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            
            <button type="submit" id="submitBtn">Create Admin Account</button>
        </form>
        
        <div id="message"></div>
    </div>

    <script>
        document.getElementById('setupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            
            const formData = {
                username: document.getElementById('username').value.trim(),
                email: document.getElementById('email').value.trim(),
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value
            };
            
            // Validation
            if (formData.password !== formData.confirmPassword) {
                messageDiv.innerHTML = '<div class="message error">Passwords do not match!</div>';
                return;
            }
            
            if (formData.password.length < 8) {
                messageDiv.innerHTML = '<div class="message error">Password must be at least 8 characters long!</div>';
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating...';
            messageDiv.innerHTML = '';
            
            try {
                const response = await fetch('/api/auth/setup-admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: formData.username,
                        email: formData.email,
                        password: formData.password
                    }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    messageDiv.innerHTML = '<div class="message success">✅ Admin account created successfully! You can now login at <a href="/login" style="color: #4ade80;">/login</a></div>';
                    document.getElementById('setupForm').reset();
                } else {
                    messageDiv.innerHTML = `<div class="message error">❌ ${data.message || 'Setup failed'}</div>`;
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="message error">❌ Network error. Please try again.</div>';
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Create Admin Account';
            }
        });
    </script>
</body>
</html>
