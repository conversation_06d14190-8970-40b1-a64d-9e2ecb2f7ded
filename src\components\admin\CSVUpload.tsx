import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Upload, File, X, Download, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface CSVUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  selectedFile: File | null;
  isProcessing?: boolean;
  onDownloadTemplate: () => void;
}

export default function CSVUpload({ 
  onFileSelect, 
  onFileRemove, 
  selectedFile, 
  isProcessing = false,
  onDownloadTemplate 
}: CSVUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'idle' | 'valid' | 'invalid'>('idle');

  const validateCSVFile = (file: File): boolean => {
    // Check file extension
    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast({
        title: "Invalid file type",
        description: "Please select a CSV file (.csv extension)",
        variant: "destructive",
      });
      return false;
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      toast({
        title: "File too large",
        description: "Please select a file smaller than 10MB",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleFileSelect = useCallback((file: File) => {
    if (validateCSVFile(file)) {
      setValidationStatus('valid');
      onFileSelect(file);
      toast({
        title: "File selected",
        description: `${file.name} is ready for processing`,
      });
    } else {
      setValidationStatus('invalid');
    }
  }, [onFileSelect]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleRemoveFile = () => {
    setValidationStatus('idle');
    onFileRemove();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* Template Download Section */}
      <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border border-border">
        <div>
          <h4 className="font-medium text-sm">Need a template?</h4>
          <p className="text-xs text-muted-foreground">
            Download a sample CSV file with the correct format and headers
          </p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onDownloadTemplate}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Download Template
        </Button>
      </div>

      {/* File Upload Area */}
      <Card className="border-2 border-dashed border-border hover:border-primary/50 transition-colors">
        <CardContent className="p-6">
          {!selectedFile ? (
            <div
              className={`text-center space-y-4 ${
                isDragOver ? 'bg-primary/5 border-primary' : ''
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <div className="flex justify-center">
                <Upload className="h-12 w-12 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-medium">Upload CSV File</h3>
                <p className="text-sm text-muted-foreground">
                  Drag and drop your CSV file here, or click to browse
                </p>
              </div>
              <div className="flex justify-center">
                <Button variant="outline" asChild>
                  <label htmlFor="csv-upload" className="cursor-pointer">
                    <File className="h-4 w-4 mr-2" />
                    Choose File
                  </label>
                </Button>
                <input
                  id="csv-upload"
                  type="file"
                  accept=".csv"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Supports CSV files up to 10MB
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div className="flex items-center gap-3">
                  <File className="h-8 w-8 text-primary" />
                  <div>
                    <p className="font-medium text-sm">{selectedFile.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(selectedFile.size)}
                    </p>
                  </div>
                  {validationStatus === 'valid' && (
                    <Badge variant="default" className="bg-green-500/20 text-green-700 dark:text-green-300">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Valid
                    </Badge>
                  )}
                  {validationStatus === 'invalid' && (
                    <Badge variant="destructive">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Invalid
                    </Badge>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveFile}
                  disabled={isProcessing}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              {validationStatus === 'valid' && (
                <div className="text-center">
                  <p className="text-sm text-green-600 dark:text-green-400">
                    ✓ File is ready for processing
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* File Format Information */}
      <div className="text-xs text-muted-foreground space-y-2">
        <p><strong>Supported format:</strong> CSV files with comma-separated values</p>
        <p><strong>Required fields:</strong> Title, Type, Year (minimum)</p>
        <p><strong>Multi-value fields:</strong> Use semicolons (;) for genres, languages, quality, audio tracks</p>
        <p><strong>Embed links:</strong> Use pipe (|) to separate multiple video links</p>
      </div>
    </div>
  );
}
