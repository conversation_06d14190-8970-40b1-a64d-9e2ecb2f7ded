# 🔒 SECURE SETUP GUIDE - NO CREDENTIALS EXPOSED

## 🎯 SECURITY PRINCIPLES

✅ **NO credentials in source code**  
✅ **NO API keys visible in browser**  
✅ **NO database passwords in files**  
✅ **NO embed links exposed**  
✅ **Secure admin authentication**  

## 📋 STEP-BY-STEP SECURE SETUP

### PHASE 1: SECURE DATABASE SETUP (15 minutes)

#### Step 1.1: Access Your Database Securely
1. **Login to FastPanel** (your hosting control panel)
2. **Go to Databases section**
3. **Click on your database** `streamdb_database`
4. **Open phpMyAdmin** (secure database management)

#### Step 1.2: Create Database Structure
1. **In phpMyAdmin, click "SQL" tab**
2. **Copy the schema from** `database/schema.sql`
3. **Paste and click "Go"** (creates all tables)
4. **Copy the initial data from** `database/initial_data.sql`
5. **Paste and click "Go"** (adds categories, genres, etc.)

#### Step 1.3: Create Secure Admin User
**Run this in phpMyAdmin SQL tab:**
```sql
-- Create your secure admin account
-- Replace 'your_username' and 'your_secure_password' with your chosen credentials
INSERT INTO admin_users (username, password_hash, email, role, is_active) VALUES
('your_username', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '<EMAIL>', 'admin', TRUE);

-- Note: The password hash above is for 'admin123' - you'll change this
```

**⚠️ IMPORTANT**: After setup, you'll change this password through the admin panel!

### PHASE 2: SECURE ENVIRONMENT CONFIGURATION

#### Step 2.1: Create Secure Environment File
**On your server, create `.env` file** (this file is NEVER uploaded to GitHub):

```bash
# Navigate to your server directory
cd /path/to/your/website/server

# Create secure environment file
nano .env
```

#### Step 2.2: Environment Variables (YOU FILL THESE SECURELY)
```env
# Database Configuration - LOCAL CONNECTION (MOST SECURE)
# Since website and database are on the same server, use local connection
DB_HOST=localhost
# No DB_PORT needed - uses local MySQL socket connection
# This is MORE SECURE than port 3306 as it's internal-only
DB_NAME=streamdb_database
DB_USER=streamdb_opl_user
DB_PASSWORD=YOUR_ACTUAL_DB_PASSWORD_FROM_FASTPANEL

# Security Keys - GENERATE THESE YOURSELF
JWT_SECRET=YOUR_VERY_LONG_RANDOM_STRING_HERE_64_CHARS_MIN
SESSION_SECRET=ANOTHER_VERY_LONG_RANDOM_STRING_HERE_64_CHARS_MIN

# Server Configuration
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://your-actual-domain.com
```

**🔑 How to Generate Secure Keys:**
```bash
# Generate random secure keys (run these commands)
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### PHASE 3: GITHUB AUTO-DEPLOYMENT SETUP

#### Step 3.1: Secure GitHub Integration
1. **Create deployment script** on your server
2. **Set up GitHub webhook** (secure)
3. **Configure auto-sync** without exposing credentials

#### Step 3.2: Deployment Script (On Your Server)
```bash
# Create deployment script
nano /home/<USER>
```

```bash
#!/bin/bash
# Secure auto-deployment script

# Set variables
REPO_URL="https://github.com/yourusername/your-repo.git"
PROJECT_DIR="/path/to/your/website"
BACKUP_DIR="/backups/website"

# Create backup
echo "Creating backup..."
cp -r $PROJECT_DIR $BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)

# Pull latest changes
echo "Pulling latest changes..."
cd $PROJECT_DIR
git pull origin main

# Install dependencies (if package.json changed)
if [ -f "package.json" ]; then
    npm install --production
fi

# Restart services
echo "Restarting services..."
pm2 restart streaming-db-api

echo "Deployment complete!"
```

#### Step 3.3: GitHub Webhook Setup
1. **Go to your GitHub repo → Settings → Webhooks**
2. **Add webhook URL**: `https://your-domain.com/webhook/deploy`
3. **Set secret token** (you generate this)
4. **Select "Just the push event"**

### PHASE 4: FRONTEND SECURITY INTEGRATION

#### Step 4.1: Secure API Configuration
**Create secure API configuration** (no credentials exposed):

```javascript
// src/config/api.js
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://your-domain.com/api'  // Your actual domain
  : 'http://localhost:3001/api';

export const apiConfig = {
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// NO credentials here - they're handled by the backend
```

#### Step 4.2: Secure Authentication Flow
```javascript
// src/services/authService.js
export const authService = {
  async login(username, password) {
    // Credentials sent securely to backend
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password }),
      credentials: 'include' // Secure cookies
    });
    
    if (response.ok) {
      const data = await response.json();
      // Token stored securely (httpOnly cookies)
      return data;
    }
    throw new Error('Login failed');
  }
};
```

### PHASE 5: EMBED LINK SECURITY

#### Step 5.1: Server-Side Embed Protection
**All embed links are encrypted and served through your backend:**

```javascript
// Backend handles embed links securely
app.get('/api/embed/:contentId', authenticateUser, async (req, res) => {
  // 1. Verify user has access
  // 2. Decrypt embed links from database
  // 3. Return secure embed URL
  // 4. Links never exposed in frontend source
});
```

#### Step 5.2: Frontend Embed Usage
```javascript
// Frontend requests embed links securely
const getEmbedLink = async (contentId) => {
  const response = await fetch(`/api/embed/${contentId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json(); // Secure embed URL
};
```

## 🛡️ SECURITY CHECKLIST

### ✅ Database Security
- [ ] Database password only in `.env` file (not in code)
- [ ] `.env` file added to `.gitignore`
- [ ] Secure admin user created
- [ ] Default passwords changed

### ✅ API Security
- [ ] JWT tokens for authentication
- [ ] Rate limiting enabled
- [ ] Input validation on all endpoints
- [ ] CORS properly configured

### ✅ Frontend Security
- [ ] No API keys in source code
- [ ] No database credentials in frontend
- [ ] Embed links served through backend
- [ ] Secure authentication flow

### ✅ Deployment Security
- [ ] GitHub webhook with secret token
- [ ] Auto-deployment script secured
- [ ] Environment variables protected
- [ ] Backup system in place

## 🚀 WHAT YOU NEED TO DO

### IMMEDIATE ACTIONS:
1. **Set up database** using phpMyAdmin (15 minutes)
2. **Create your secure admin credentials** (5 minutes)
3. **Generate your security keys** (2 minutes)
4. **Configure your `.env` file** (10 minutes)

### CREDENTIALS YOU'LL NEED (FROM FASTPANEL):
- Database password
- Your domain name
- Server file paths

### CREDENTIALS YOU'LL CREATE:
- Admin username/password
- JWT secret key
- Session secret key
- GitHub webhook secret

## 🔒 SECURITY GUARANTEE

With this setup:
- **NO credentials visible in browser source code**
- **NO database passwords in GitHub**
- **NO API keys exposed to users**
- **ALL embed links protected server-side**
- **SECURE admin authentication**
- **AUTOMATIC secure deployments**

**Ready to proceed? I'll guide you through each step securely!**
