#!/usr/bin/env node

/**
 * StreamDB Online - Check PM2 Status and Start Server
 * 
 * This script checks PM2 status and starts the server if needed
 */

const { exec } = require('child_process');
const util = require('util');
const path = require('path');

const execAsync = util.promisify(exec);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bright: '\x1b[1m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

async function checkPM2Status() {
  log('🔍 StreamDB Online - PM2 Status Check', 'bright');
  log('====================================', 'bright');
  
  try {
    // Check if PM2 is installed
    logInfo('Checking PM2 installation...');
    await execAsync('pm2 --version');
    logSuccess('PM2 is installed');
    
    // List all PM2 processes
    logInfo('Listing PM2 processes...');
    const { stdout } = await execAsync('pm2 list');
    console.log(stdout);
    
    // Check for common process names
    const commonNames = ['streamdb-online', 'streamdb', 'server', 'index', 'app'];
    
    for (const name of commonNames) {
      try {
        const { stdout: statusOutput } = await execAsync(`pm2 describe ${name}`);
        if (statusOutput.includes('online')) {
          logSuccess(`Found running process: ${name}`);
          return name;
        }
      } catch (error) {
        // Process not found, continue checking
      }
    }
    
    logWarning('No StreamDB processes found running in PM2');
    return null;
    
  } catch (error) {
    logError(`PM2 check failed: ${error.message}`);
    return null;
  }
}

async function startServer() {
  log('\n🚀 Starting StreamDB Server...', 'cyan');
  
  try {
    // Check if ecosystem.config.js exists
    const ecosystemPath = path.join(__dirname, '../../ecosystem.config.js');
    
    try {
      require('fs').accessSync(ecosystemPath);
      logInfo('Found ecosystem.config.js, starting with PM2...');
      
      const { stdout } = await execAsync('pm2 start ecosystem.config.js');
      console.log(stdout);
      logSuccess('Server started with ecosystem.config.js');
      
    } catch (error) {
      logWarning('ecosystem.config.js not found, starting index.js directly...');
      
      // Start index.js directly
      const { stdout } = await execAsync('pm2 start index.js --name streamdb-online');
      console.log(stdout);
      logSuccess('Server started as streamdb-online');
    }
    
    // Wait a moment and check status
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const { stdout: statusOutput } = await execAsync('pm2 list');
    console.log('\n📊 Current PM2 Status:');
    console.log(statusOutput);
    
  } catch (error) {
    logError(`Failed to start server: ${error.message}`);
    
    logInfo('Manual start options:');
    logInfo('  1. pm2 start index.js --name streamdb-online');
    logInfo('  2. pm2 start ecosystem.config.js');
    logInfo('  3. node index.js (for testing)');
  }
}

async function main() {
  const runningProcess = await checkPM2Status();
  
  if (runningProcess) {
    logSuccess(`StreamDB is already running as: ${runningProcess}`);
    logInfo('To restart: pm2 restart ' + runningProcess);
    logInfo('To stop: pm2 stop ' + runningProcess);
    logInfo('To view logs: pm2 logs ' + runningProcess);
  } else {
    logWarning('StreamDB is not running in PM2');
    logInfo('Attempting to start the server...');
    await startServer();
  }
  
  log('\n📋 Useful PM2 Commands:', 'cyan');
  log('======================', 'cyan');
  logInfo('pm2 list                 - List all processes');
  logInfo('pm2 logs                 - View all logs');
  logInfo('pm2 restart all          - Restart all processes');
  logInfo('pm2 stop all             - Stop all processes');
  logInfo('pm2 delete all           - Delete all processes');
}

// Run if called directly
if (require.main === module) {
  main()
    .then(() => {
      log('\n🎉 PM2 status check completed!', 'green');
    })
    .catch(error => {
      logError(`Script failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { checkPM2Status, startServer };
