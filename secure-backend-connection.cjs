#!/usr/bin/env node

/**
 * Secure Backend Connection Setup for StreamDB Online
 * Establishes secure connection to VPS server (***********)
 */

const fs = require('fs');
const path = require('path');
const { exec, spawn } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

class SecureBackendConnection {
  constructor() {
    this.serverIP = '***********';
    this.serverUser = 'root';
    this.serverPath = '/var/www/streamdb_onl_usr/data/www/streamdb.online';
    this.localPath = process.cwd();
    this.issues = [];
    this.fixes = [];
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    
    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
  }

  async checkSSHConnection() {
    this.log('Testing SSH connection to backend server...', 'info');
    
    try {
      const { stdout, stderr } = await execAsync(`ssh -o ConnectTimeout=10 -o BatchMode=yes ${this.serverUser}@${this.serverIP} "echo 'SSH connection successful'"`);
      
      if (stdout.includes('SSH connection successful')) {
        this.log('SSH connection to backend server is working', 'success');
        this.fixes.push('SSH connection verified');
        return true;
      } else {
        this.log('SSH connection failed - authentication required', 'warning');
        this.issues.push('SSH authentication needed');
        return false;
      }
    } catch (error) {
      this.log(`SSH connection failed: ${error.message}`, 'error');
      this.issues.push('SSH connection failed');
      return false;
    }
  }

  async checkServerStatus() {
    this.log('Checking backend server status...', 'info');
    
    try {
      const commands = [
        'systemctl is-active nginx',
        'systemctl is-active mysql',
        'pm2 list | grep streamdb || echo "PM2 not running"',
        'netstat -tlnp | grep :3001 || echo "Port 3001 not listening"'
      ];
      
      for (const command of commands) {
        try {
          const { stdout } = await execAsync(`ssh ${this.serverUser}@${this.serverIP} "${command}"`);
          this.log(`Server check: ${command} - ${stdout.trim()}`, 'info');
        } catch (error) {
          this.log(`Server check failed: ${command}`, 'warning');
        }
      }
      
      this.fixes.push('Server status checked');
      return true;
    } catch (error) {
      this.log(`Server status check failed: ${error.message}`, 'error');
      this.issues.push('Server status check failed');
      return false;
    }
  }

  async testDatabaseConnection() {
    this.log('Testing database connection on backend server...', 'info');
    
    try {
      const testCommand = `cd ${this.serverPath}/server && node -e "
        const mysql = require('mysql2/promise');
        require('dotenv').config();
        
        async function test() {
          try {
            const connection = await mysql.createConnection({
              host: process.env.DB_HOST || 'localhost',
              user: process.env.DB_USER,
              password: process.env.DB_PASSWORD,
              database: process.env.DB_NAME,
              socketPath: process.env.DB_SOCKET
            });
            
            await connection.execute('SELECT 1');
            console.log('Database connection successful');
            await connection.end();
          } catch (error) {
            console.error('Database connection failed:', error.message);
            process.exit(1);
          }
        }
        
        test();
      "`;
      
      const { stdout, stderr } = await execAsync(`ssh ${this.serverUser}@${this.serverIP} "${testCommand}"`);
      
      if (stdout.includes('Database connection successful')) {
        this.log('Database connection is working', 'success');
        this.fixes.push('Database connection verified');
        return true;
      } else {
        this.log('Database connection failed', 'error');
        this.issues.push('Database connection failed');
        return false;
      }
    } catch (error) {
      this.log(`Database test failed: ${error.message}`, 'error');
      this.issues.push('Database test failed');
      return false;
    }
  }

  async deployToServer() {
    this.log('Deploying latest code to backend server...', 'info');
    
    try {
      // Build frontend
      this.log('Building frontend...', 'info');
      await execAsync('npm run build');
      
      // Deploy frontend
      this.log('Deploying frontend files...', 'info');
      await execAsync(`scp -r dist/* ${this.serverUser}@${this.serverIP}:${this.serverPath}/`);
      
      // Deploy backend
      this.log('Deploying backend files...', 'info');
      await execAsync(`scp -r server/* ${this.serverUser}@${this.serverIP}:${this.serverPath}/server/`);
      
      // Install dependencies on server
      this.log('Installing backend dependencies...', 'info');
      await execAsync(`ssh ${this.serverUser}@${this.serverIP} "cd ${this.serverPath}/server && npm install --production"`);
      
      // Restart services
      this.log('Restarting services...', 'info');
      await execAsync(`ssh ${this.serverUser}@${this.serverIP} "pm2 restart streamdb-online || pm2 start ${this.serverPath}/server/index.js --name streamdb-online"`);
      await execAsync(`ssh ${this.serverUser}@${this.serverIP} "systemctl reload nginx"`);
      
      this.log('Deployment completed successfully', 'success');
      this.fixes.push('Code deployed to server');
      return true;
    } catch (error) {
      this.log(`Deployment failed: ${error.message}`, 'error');
      this.issues.push('Deployment failed');
      return false;
    }
  }

  async setupSSHKey() {
    this.log('Setting up SSH key authentication...', 'info');
    
    const sshKeyPath = path.join(require('os').homedir(), '.ssh', 'id_rsa');
    
    if (!fs.existsSync(sshKeyPath)) {
      this.log('Generating SSH key pair...', 'info');
      try {
        await execAsync(`ssh-keygen -t rsa -b 4096 -f ${sshKeyPath} -N ""`);
        this.log('SSH key pair generated', 'success');
      } catch (error) {
        this.log(`SSH key generation failed: ${error.message}`, 'error');
        return false;
      }
    }
    
    try {
      this.log('Copying SSH key to server...', 'info');
      await execAsync(`ssh-copy-id -i ${sshKeyPath}.pub ${this.serverUser}@${this.serverIP}`);
      this.log('SSH key authentication setup complete', 'success');
      this.fixes.push('SSH key authentication configured');
      return true;
    } catch (error) {
      this.log(`SSH key setup failed: ${error.message}`, 'error');
      this.issues.push('SSH key setup failed');
      return false;
    }
  }

  async runDiagnostics() {
    this.log('🔧 Starting Backend Server Connection Diagnostics...', 'info');
    this.log('================================================', 'info');
    
    const sshConnected = await this.checkSSHConnection();
    
    if (!sshConnected) {
      this.log('Setting up SSH authentication...', 'warning');
      await this.setupSSHKey();
    }
    
    if (sshConnected || await this.checkSSHConnection()) {
      await this.checkServerStatus();
      await this.testDatabaseConnection();
    }
    
    this.generateReport();
    
    return this.issues.length === 0;
  }

  async deployAndTest() {
    this.log('🚀 Starting Deployment and Testing...', 'info');
    this.log('=====================================', 'info');
    
    const deployed = await this.deployToServer();
    
    if (deployed) {
      // Test the deployed application
      this.log('Testing deployed application...', 'info');
      try {
        const { stdout } = await execAsync(`curl -s -o /dev/null -w "%{http_code}" https://streamdb.online/api/health`);
        if (stdout.trim() === '200') {
          this.log('Application is responding correctly', 'success');
          this.fixes.push('Application deployment verified');
        } else {
          this.log(`Application returned status: ${stdout.trim()}`, 'warning');
        }
      } catch (error) {
        this.log('Could not test application endpoint', 'warning');
      }
    }
    
    this.generateReport();
    return deployed;
  }

  generateReport() {
    this.log('================================================', 'info');
    this.log('🎯 Backend Connection Report', 'info');
    this.log('================================================', 'info');
    
    this.log(`✅ Fixes Applied: ${this.fixes.length}`, 'success');
    this.log(`❌ Issues Found: ${this.issues.length}`, this.issues.length > 0 ? 'error' : 'success');
    
    if (this.fixes.length > 0) {
      this.log('\n🔧 SUCCESSFUL OPERATIONS:', 'success');
      this.fixes.forEach((fix, index) => {
        this.log(`   ${index + 1}. ${fix}`, 'success');
      });
    }
    
    if (this.issues.length > 0) {
      this.log('\n❌ ISSUES TO RESOLVE:', 'error');
      this.issues.forEach((issue, index) => {
        this.log(`   ${index + 1}. ${issue}`, 'error');
      });
      
      this.log('\n🔧 RECOMMENDED ACTIONS:', 'warning');
      this.log('   1. Ensure SSH access to server (***********)', 'warning');
      this.log('   2. Verify MySQL database is running', 'warning');
      this.log('   3. Check server .env configuration', 'warning');
      this.log('   4. Restart PM2 and nginx services', 'warning');
    } else {
      this.log('\n🎉 All systems operational!', 'success');
      this.log('   Your StreamDB Online backend is ready!', 'success');
    }
  }
}

// Command line interface
if (require.main === module) {
  const connection = new SecureBackendConnection();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'test':
      connection.runDiagnostics();
      break;
    case 'deploy':
      connection.deployAndTest();
      break;
    case 'ssh':
      connection.setupSSHKey();
      break;
    default:
      console.log('Usage:');
      console.log('  node secure-backend-connection.cjs test    - Test server connection');
      console.log('  node secure-backend-connection.cjs deploy  - Deploy and test');
      console.log('  node secure-backend-connection.cjs ssh     - Setup SSH key');
      break;
  }
}

module.exports = SecureBackendConnection;
