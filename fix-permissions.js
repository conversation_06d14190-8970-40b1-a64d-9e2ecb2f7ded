#!/usr/bin/env node

/**
 * Fix File Permissions Script
 * Sets proper security permissions for production deployment
 */

import fs from 'fs';
import path from 'path';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function fixPermissions() {
  log('🔒 Fixing File Permissions for Production Security', 'cyan');
  log('================================================', 'cyan');
  
  const filesToFix = [
    { file: '.env', mode: 0o600, description: 'Frontend environment file' },
    { file: 'server/.env', mode: 0o600, description: 'Server environment file' },
    { file: 'server/setup-production.js', mode: 0o755, description: 'Production setup script' },
    { file: 'server/security-audit.js', mode: 0o755, description: 'Security audit script' },
    { file: 'deployment/deploy.sh', mode: 0o755, description: 'Deployment script' },
    { file: 'deployment/webhook-handler.js', mode: 0o755, description: 'Webhook handler' },
    { file: 'fix-permissions.js', mode: 0o755, description: 'This permissions script' }
  ];
  
  let fixed = 0;
  let errors = 0;
  
  for (const { file, mode, description } of filesToFix) {
    try {
      // Check if file exists
      if (!fs.existsSync(file)) {
        log(`⚠️  File not found: ${file} (${description})`, 'yellow');
        continue;
      }
      
      // Get current permissions
      const stats = fs.statSync(file);
      const currentMode = stats.mode & parseInt('777', 8);
      const expectedMode = mode & parseInt('777', 8);
      
      if (currentMode === expectedMode) {
        log(`✅ ${file} already has correct permissions (${expectedMode.toString(8)})`, 'green');
      } else {
        // Fix permissions
        fs.chmodSync(file, mode);
        log(`🔧 Fixed permissions for ${file}: ${currentMode.toString(8)} → ${expectedMode.toString(8)}`, 'green');
        fixed++;
      }
      
    } catch (error) {
      log(`❌ Failed to fix permissions for ${file}: ${error.message}`, 'red');
      errors++;
    }
  }
  
  // Create secure directories if they don't exist
  const dirsToCreate = [
    { dir: 'server/uploads', mode: 0o755 },
    { dir: 'server/uploads/images', mode: 0o755 },
    { dir: 'server/uploads/videos', mode: 0o755 },
    { dir: 'server/uploads/subtitles', mode: 0o755 },
    { dir: 'server/uploads/temp', mode: 0o777 },
    { dir: 'server/logs', mode: 0o755 }
  ];
  
  for (const { dir, mode } of dirsToCreate) {
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode });
        log(`📁 Created directory: ${dir} with permissions ${mode.toString(8)}`, 'green');
      } else {
        // Check and fix directory permissions
        const stats = fs.statSync(dir);
        const currentMode = stats.mode & parseInt('777', 8);
        const expectedMode = mode & parseInt('777', 8);
        
        if (currentMode !== expectedMode) {
          fs.chmodSync(dir, mode);
          log(`🔧 Fixed directory permissions: ${dir} (${expectedMode.toString(8)})`, 'green');
        }
      }
    } catch (error) {
      log(`❌ Failed to create/fix directory ${dir}: ${error.message}`, 'red');
      errors++;
    }
  }
  
  // Summary
  log('\n📊 Permission Fix Summary:', 'cyan');
  log(`✅ Files fixed: ${fixed}`, 'green');
  log(`❌ Errors: ${errors}`, errors > 0 ? 'red' : 'green');
  
  if (errors === 0) {
    log('\n🎉 All file permissions have been set correctly for production!', 'green');
    log('🔒 Your files are now secure for deployment.', 'green');
  } else {
    log('\n⚠️  Some permission fixes failed. Please check the errors above.', 'yellow');
    log('💡 You may need to run this script with elevated privileges.', 'yellow');
  }
  
  // Additional security recommendations
  log('\n🛡️  Additional Security Recommendations:', 'cyan');
  log('1. Never commit .env files to version control', 'yellow');
  log('2. Regularly rotate your secrets and API keys', 'yellow');
  log('3. Use HTTPS in production', 'yellow');
  log('4. Monitor your security logs regularly', 'yellow');
  log('5. Keep dependencies updated', 'yellow');
}

// Run the permission fix
fixPermissions().catch(error => {
  console.error('❌ Permission fix failed:', error);
  process.exit(1);
});
