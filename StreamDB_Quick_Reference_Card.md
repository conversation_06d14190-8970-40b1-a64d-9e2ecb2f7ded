# 🚀 StreamDB.online Quick Reference Card

## 📋 ESSENTIAL INFORMATION

**Your Servers:**
- Reverse Proxy: `*************`
- Backend Server: `***********`

**Your Domains:**
- Main Website: `https://streamdb.online`
- Admin Panel: `https://streamdb.online/admin`
- FastPanel: `https://fastpanel.streamdb.online`

**Database:**
- Name: `streamdb_database`
- User: `dbadmin_streamdb`
- Connection: Local socket only

---

## 🔧 QUICK COMMANDS

### Check Application Status
```bash
# SSH into backend server
ssh root@***********

# Check all services
pm2 status && systemctl status nginx && systemctl status mysql

# Check application logs
pm2 logs streamdb-online

# Check system resources
htop
```

### Restart Services
```bash
# Restart Node.js application
pm2 restart streamdb-online

# Restart Nginx
systemctl restart nginx

# Restart MySQL
systemctl restart mysql

# Restart FastPanel
systemctl restart fastpanel
```

### Test Connectivity
```bash
# Test main website
curl -I https://streamdb.online

# Test admin panel
curl -I https://streamdb.online/admin

# Test FastPanel
curl -I https://fastpanel.streamdb.online

# Test API health
curl https://streamdb.online/api/health

# Test database connection
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
node test-db-connection.js
```

### View Logs
```bash
# Application logs
pm2 logs streamdb-online

# Nginx access logs
tail -f /var/log/nginx/streamdb_backend_access.log

# Nginx error logs
tail -f /var/log/nginx/streamdb_backend_error.log

# MySQL logs
tail -f /var/log/mysql/error.log

# System logs
journalctl -f
```

### File Locations
```bash
# Project directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Server code
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Frontend files
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/dist

# Uploads directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/uploads

# Configuration files
nano /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env
```

---

## 🚨 TROUBLESHOOTING QUICK FIXES

### Website Not Loading (502 Error)
```bash
ssh root@***********
pm2 restart streamdb-online
systemctl restart nginx
```

### FastPanel Not Accessible
```bash
ssh root@***********
systemctl restart fastpanel
ufw status
netstat -tlnp | grep :8888
```

### Database Connection Issues
```bash
ssh root@***********
systemctl restart mysql
mysql -u dbadmin_streamdb -p streamdb_database
```

### SSL Certificate Issues
1. Check Cloudflare SSL mode is "Full (strict)"
2. Clear browser cache
3. Wait 5-10 minutes for propagation

### High Memory Usage
```bash
ssh root@***********
pm2 restart streamdb-online
pm2 monit
```

---

## 🔒 SECURITY CHECKLIST

**Firewall Status:**
```bash
# On reverse proxy (*************)
ufw status  # Should show: 22, 80, 443 open

# On backend (***********)
ufw status  # Should show: 22, 80, 443, 8888 + allow from *************
```

**Database Security:**
```bash
# Should work (local connection)
mysql -h localhost -u dbadmin_streamdb -p

# Should fail (external connection - this is good!)
telnet *********** 3306
```

**SSL Security:**
```bash
# Check SSL certificates
curl -I https://streamdb.online
curl -I https://fastpanel.streamdb.online
```

---

## 📊 MONITORING

### Performance Monitoring
```bash
# System resources
htop

# Disk usage
df -h

# Network connections
netstat -tlnp

# PM2 monitoring
pm2 monit
```

### Log Monitoring
```bash
# Real-time application logs
pm2 logs streamdb-online --lines 50

# Real-time nginx logs
tail -f /var/log/nginx/streamdb_backend_access.log

# Check for errors
grep -i error /var/log/nginx/streamdb_backend_error.log
```

---

## 🔄 UPDATE PROCEDURE

### Update Application Code
```bash
# On local machine
npm run build
zip -r streamdb-update.zip dist/ server/ -x "server/node_modules/*"

# Upload via FastPanel File Manager or SCP
# Then on server:
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
# Extract new files
pm2 restart streamdb-online
```

### Update Dependencies
```bash
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
npm update
npm audit fix
pm2 restart streamdb-online
```

---

## 📞 EMERGENCY CONTACTS

**If completely locked out:**
1. Contact VPS provider for console access
2. Disable firewall: `sudo ufw disable`
3. Check backup files in `/tmp/`

**Critical File Backups:**
- Nginx configs: `/etc/nginx/sites-available/`
- Environment file: `/var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env`
- Database: Regular mysqldump backups

---

## 🎯 QUICK HEALTH CHECK

Run this complete health check:
```bash
#!/bin/bash
echo "=== StreamDB Health Check ==="
echo "1. Testing main website..."
curl -I https://streamdb.online | head -1

echo "2. Testing admin panel..."
curl -I https://streamdb.online/admin | head -1

echo "3. Testing FastPanel..."
curl -I https://fastpanel.streamdb.online | head -1

echo "4. Testing API..."
curl -s https://streamdb.online/api/health | grep status

echo "5. Checking PM2..."
ssh root@*********** "pm2 list | grep streamdb-online"

echo "6. Checking services..."
ssh root@*********** "systemctl is-active nginx mysql fastpanel"

echo "=== Health Check Complete ==="
```

---

**Document Version:** 1.0  
**Last Updated:** 2025-01-02  
**For:** StreamDB.online Production Environment
