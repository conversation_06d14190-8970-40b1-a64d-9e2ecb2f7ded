/**
 * Episodes and Seasons Management API Routes
 * Handles CRUD operations for episodes and seasons of web series
 */
const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');

// Validation middleware for seasons
const seasonValidation = [
  body('seasonNumber').isInt({ min: 1 }).withMessage('Season number must be a positive integer'),
  body('title').optional().isLength({ max: 255 }).withMessage('Title must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('posterUrl').optional().custom((value) => {
    if (!value || value.trim() === '') return true; // Allow empty values
    try {
      new URL(value);
      return true;
    } catch {
      throw new Error('Poster URL must be a valid URL');
    }
  }),
];

// Validation middleware for episodes
const episodeValidation = [
  body('episodeNumber').isInt({ min: 1 }).withMessage('Episode number must be a positive integer'),
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Title is required and must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('secureVideoLinks').optional().isLength({ max: 2000 }).withMessage('Video links must be less than 2000 characters'),
  body('runtime').optional().isLength({ max: 20 }).withMessage('Runtime must be less than 20 characters'),
  body('airDate').optional().custom((value) => {
    if (!value || value.trim() === '') return true; // Allow empty values
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      throw new Error('Air date must be a valid date');
    }
    return true;
  }),
  body('thumbnailUrl').optional().custom((value) => {
    if (!value || value.trim() === '') return true; // Allow empty values
    try {
      new URL(value);
      return true;
    } catch {
      throw new Error('Thumbnail URL must be a valid URL');
    }
  }),
];

/**
 * Get all seasons for a content item
 * GET /api/episodes/content/:contentId/seasons
 */
router.get('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;

    const seasonsResult = await db.execute(`
      SELECT * FROM seasons
      WHERE content_id = ?
      ORDER BY season_number ASC
    `, [contentId]);

    // db.execute returns rows directly, not [rows, fields]
    const seasons = seasonsResult || [];

    // Get episodes for each season
    for (let season of seasons || []) {
      const episodesResult = await db.execute(`
        SELECT * FROM episodes
        WHERE season_id = ?
        ORDER BY episode_number ASC
      `, [season.id]);

      // db.execute returns rows directly, not [rows, fields]
      season.episodes = episodesResult || [];
    }

    res.json({
      success: true,
      data: seasons
    });

  } catch (error) {
    console.error('Error fetching seasons:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch seasons'
    });
  }
});

/**
 * Create new season
 * POST /api/episodes/content/:contentId/seasons
 */
router.post('/content/:contentId/seasons', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    console.log('Season creation request received:', {
      contentId: req.params.contentId,
      body: req.body,
      user: req.user?.username
    });

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Season validation errors:', errors.array());
      console.log('Request body that failed validation:', req.body);
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array(),
        receivedData: req.body
      });
    }

    const { contentId } = req.params;
    const { seasonNumber, title, description, posterUrl } = req.body;

    console.log('Creating season for content:', contentId, 'Season:', seasonNumber, 'Data:', { title, description, posterUrl });

    // Check if season number already exists for this content
    try {
      const existingSeasonResult = await db.execute(
        'SELECT id FROM seasons WHERE content_id = ? AND season_number = ?',
        [contentId, seasonNumber]
      );

      // Handle mysql2 result format properly
      // db.execute returns rows directly, not [rows, fields]
      const existingSeason = existingSeasonResult || [];

      if (existingSeason.length > 0) {
        console.log('Season already exists:', existingSeason);
        return res.status(400).json({
          success: false,
          error: 'Validation Error',
          message: 'Season number already exists for this content'
        });
      }

      // Generate unique ID for season
      const seasonId = `season_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      console.log('Inserting new season with ID:', seasonId);

      await db.execute(`
        INSERT INTO seasons (
          id, content_id, season_number, title, description, poster_url, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [seasonId, contentId, seasonNumber, title || null, description || null, posterUrl || null]);

      console.log('Season created successfully:', seasonId);

      // Update content total seasons count
      await db.execute(`
        UPDATE content SET
          total_seasons = (SELECT COUNT(*) FROM seasons WHERE content_id = ?),
          updated_at = NOW()
        WHERE id = ?
      `, [contentId, contentId]);

      res.status(201).json({
        success: true,
        message: 'Season created successfully',
        data: {
          id: seasonId,
          contentId,
          seasonNumber,
          title,
          description,
          posterUrl,
          episodes: []
        }
      });

    } catch (dbError) {
      console.error('Database error creating season:', dbError);
      return res.status(500).json({
        success: false,
        error: 'Database Error',
        message: 'Failed to create season in database'
      });
    }

  } catch (error) {
    console.error('Error creating season:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create season'
    });
  }
});

/**
 * Create new episode
 * POST /api/episodes/seasons/:seasonId/episodes
 */
router.post('/seasons/:seasonId/episodes', authenticateToken, requireModerator, episodeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Episode validation errors:', errors.array());
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { seasonId } = req.params;
    const {
      episodeNumber, title, description, secureVideoLinks,
      runtime, airDate, thumbnailUrl
    } = req.body;

    console.log('Creating episode for season:', seasonId, 'Episode:', episodeNumber);

    try {
      // Get season info
      const seasonResult = await db.execute('SELECT content_id FROM seasons WHERE id = ?', [seasonId]);

      // Handle mysql2 result format properly
      // db.execute returns rows directly, not [rows, fields]
      const season = seasonResult || [];

      if (season.length === 0) {
        console.log('Season not found:', seasonId);
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Season not found'
        });
      }

      const contentId = season[0].content_id;
      console.log('Found season for content:', contentId);

      // Check if episode number already exists for this season
      const existingEpisodeResult = await db.execute(
        'SELECT id FROM episodes WHERE season_id = ? AND episode_number = ?',
        [seasonId, episodeNumber]
      );

      // db.execute returns rows directly, not [rows, fields]
      const existingEpisode = existingEpisodeResult || [];

      if (existingEpisode.length > 0) {
        console.log('Episode already exists:', existingEpisode);
        return res.status(400).json({
          success: false,
          error: 'Validation Error',
          message: 'Episode number already exists for this season'
        });
      }

      // Generate unique ID for episode
      const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      console.log('Inserting new episode with ID:', episodeId);

      await db.execute(`
        INSERT INTO episodes (
          id, season_id, content_id, episode_number, title, description,
          secure_video_links, runtime, air_date, thumbnail_url, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        episodeId, seasonId, contentId, episodeNumber, title,
        description || null, secureVideoLinks || null, runtime || null,
        airDate || null, thumbnailUrl || null
      ]);

      console.log('Episode created successfully:', episodeId);

      // Update content total episodes count
      await db.execute(`
        UPDATE content SET
          total_episodes = (SELECT COUNT(*) FROM episodes WHERE content_id = ?),
          updated_at = NOW()
        WHERE id = ?
      `, [contentId, contentId]);

      res.status(201).json({
        success: true,
        message: 'Episode created successfully',
        data: {
          id: episodeId,
          seasonId,
          contentId,
          episodeNumber,
          title,
          description,
          secureVideoLinks,
          runtime,
          airDate,
          thumbnailUrl
        }
      });

    } catch (dbError) {
      console.error('Database error creating episode:', dbError);
      return res.status(500).json({
        success: false,
        error: 'Database Error',
        message: 'Failed to create episode in database'
      });
    }

  } catch (error) {
    console.error('Error creating episode:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create episode'
    });
  }
});

/**
 * Update episode
 * PUT /api/episodes/:episodeId
 */
router.put('/:episodeId', authenticateToken, requireModerator, episodeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { episodeId } = req.params;
    const { 
      episodeNumber, title, description, secureVideoLinks, 
      runtime, airDate, thumbnailUrl 
    } = req.body;

    // Check if episode exists
    const existingEpisodeResult = await db.execute('SELECT season_id, content_id FROM episodes WHERE id = ?', [episodeId]);
    // db.execute returns rows directly, not [rows, fields]
    const existingEpisode = existingEpisodeResult || [];
    if (existingEpisode.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Episode not found'
      });
    }

    const { season_id: seasonId, content_id: contentId } = existingEpisode[0];

    // Check if episode number conflicts with other episodes in the same season
    const conflictingEpisodeResult = await db.execute(
      'SELECT id FROM episodes WHERE season_id = ? AND episode_number = ? AND id != ?',
      [seasonId, episodeNumber, episodeId]
    );

    // db.execute returns rows directly, not [rows, fields]
    const conflictingEpisode = conflictingEpisodeResult || [];

    if (conflictingEpisode.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Episode number already exists for this season'
      });
    }

    await db.execute(`
      UPDATE episodes SET 
        episode_number = ?, title = ?, description = ?, secure_video_links = ?,
        runtime = ?, air_date = ?, thumbnail_url = ?, updated_at = NOW()
      WHERE id = ?
    `, [
      episodeNumber, title, description || null, secureVideoLinks || null,
      runtime || null, airDate || null, thumbnailUrl || null, episodeId
    ]);

    // Update content updated_at
    await db.execute('UPDATE content SET updated_at = NOW() WHERE id = ?', [contentId]);

    res.json({
      success: true,
      message: 'Episode updated successfully'
    });

  } catch (error) {
    console.error('Error updating episode:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update episode'
    });
  }
});

/**
 * Delete episode
 * DELETE /api/episodes/:episodeId
 */
router.delete('/:episodeId', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { episodeId } = req.params;

    // Get episode info before deletion
    const episodeResult = await db.execute('SELECT content_id FROM episodes WHERE id = ?', [episodeId]);
    // db.execute returns rows directly, not [rows, fields]
    const episode = episodeResult || [];
    if (episode.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Episode not found'
      });
    }

    const contentId = episode[0].content_id;

    await db.execute('DELETE FROM episodes WHERE id = ?', [episodeId]);

    // Update content total episodes count
    await db.execute(`
      UPDATE content SET 
        total_episodes = (SELECT COUNT(*) FROM episodes WHERE content_id = ?),
        updated_at = NOW()
      WHERE id = ?
    `, [contentId, contentId]);

    res.json({
      success: true,
      message: 'Episode deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting episode:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete episode'
    });
  }
});

/**
 * Update season
 * PUT /api/episodes/seasons/:seasonId
 */
router.put('/seasons/:seasonId', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { seasonId } = req.params;
    const { seasonNumber, title, description, posterUrl } = req.body;

    // Check if season exists
    const seasonResult = await db.execute('SELECT content_id FROM seasons WHERE id = ?', [seasonId]);

    // db.execute returns rows directly, not [rows, fields]
    const season = seasonResult || [];

    if (season.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Season not found'
      });
    }

    const contentId = season[0].content_id;

    // Check if season number already exists for this content (excluding current season)
    const existingSeasonResult = await db.execute(
      'SELECT id FROM seasons WHERE content_id = ? AND season_number = ? AND id != ?',
      [contentId, seasonNumber, seasonId]
    );

    // db.execute returns rows directly, not [rows, fields]
    const existingSeason = existingSeasonResult || [];

    if (existingSeason.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: `Season ${seasonNumber} already exists for this content`
      });
    }

    // Update the season
    await db.execute(`
      UPDATE seasons SET
        season_number = ?, title = ?, description = ?, poster_url = ?, updated_at = NOW()
      WHERE id = ?
    `, [seasonNumber, title || null, description || null, posterUrl || null, seasonId]);

    res.json({
      success: true,
      message: 'Season updated successfully',
      data: {
        id: seasonId,
        seasonNumber,
        title,
        description,
        posterUrl
      }
    });

  } catch (error) {
    console.error('Error updating season:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update season'
    });
  }
});

/**
 * Delete season
 * DELETE /api/episodes/seasons/:seasonId
 */
router.delete('/seasons/:seasonId', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { seasonId } = req.params;

    // Get season info before deletion
    const seasonResult = await db.execute('SELECT content_id FROM seasons WHERE id = ?', [seasonId]);

    // db.execute returns rows directly, not [rows, fields]
    const season = seasonResult || [];

    if (season.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Season not found'
      });
    }

    const contentId = season[0].content_id;

    // Delete the season (episodes will be deleted automatically due to CASCADE)
    await db.execute('DELETE FROM seasons WHERE id = ?', [seasonId]);

    // Update content total seasons and episodes count
    await db.execute(`
      UPDATE content SET
        total_seasons = (SELECT COUNT(*) FROM seasons WHERE content_id = ?),
        total_episodes = (SELECT COUNT(*) FROM episodes WHERE content_id = ?),
        updated_at = NOW()
      WHERE id = ?
    `, [contentId, contentId, contentId]);

    res.json({
      success: true,
      message: 'Season deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting season:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete season'
    });
  }
});

module.exports = router;
