import React, { useState, useEffect } from "react";

interface ImageDiagnosticsProps {
  imagePaths: string[];
  title: string;
}

interface ImageTestResult {
  path: string;
  status: 'loading' | 'success' | 'error';
  loadTime?: number;
  error?: string;
  dimensions?: { width: number; height: number };
}

export default function ImageDiagnostics({ imagePaths, title }: ImageDiagnosticsProps) {
  const [results, setResults] = useState<ImageTestResult[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const testImages = async () => {
      const testResults: ImageTestResult[] = [];
      
      for (const path of imagePaths) {
        const startTime = Date.now();
        const result: ImageTestResult = {
          path,
          status: 'loading'
        };
        
        try {
          const img = new Image();
          
          await new Promise<void>((resolve, reject) => {
            img.onload = () => {
              result.status = 'success';
              result.loadTime = Date.now() - startTime;
              result.dimensions = { width: img.naturalWidth, height: img.naturalHeight };
              resolve();
            };
            
            img.onerror = (error) => {
              result.status = 'error';
              result.error = `Failed to load: ${error}`;
              reject(error);
            };
            
            // Set timeout for slow loading
            setTimeout(() => {
              if (result.status === 'loading') {
                result.status = 'error';
                result.error = 'Timeout after 5 seconds';
                reject(new Error('Timeout'));
              }
            }, 5000);
          });
          
          img.src = path;
          
        } catch (error) {
          result.status = 'error';
          result.error = error instanceof Error ? error.message : 'Unknown error';
        }
        
        testResults.push(result);
        setResults([...testResults]);
      }
    };

    if (isVisible) {
      testImages();
    }
  }, [imagePaths, isVisible]);

  // Only show in development or when explicitly enabled
  if (process.env.NODE_ENV === 'production' && !window.location.search.includes('debug=true')) {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '20px', 
      right: '20px', 
      zIndex: 9999,
      background: '#1a1a1a',
      border: '1px solid #333',
      borderRadius: '8px',
      padding: '10px',
      color: '#e6cb8e',
      fontSize: '12px',
      maxWidth: '400px',
      fontFamily: 'monospace'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '10px'
      }}>
        <strong>{title} Diagnostics</strong>
        <button
          onClick={() => setIsVisible(!isVisible)}
          style={{
            background: '#333',
            border: 'none',
            color: '#e6cb8e',
            padding: '2px 6px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          {isVisible ? 'Hide' : 'Test'}
        </button>
      </div>
      
      {isVisible && (
        <div>
          <div style={{ marginBottom: '10px', fontSize: '11px', opacity: 0.8 }}>
            Environment: {process.env.NODE_ENV}<br/>
            Host: {window.location.host}<br/>
            Protocol: {window.location.protocol}
          </div>
          
          {results.map((result, index) => (
            <div key={index} style={{ 
              marginBottom: '8px', 
              padding: '6px',
              background: result.status === 'success' ? '#1a2a1a' : 
                         result.status === 'error' ? '#2a1a1a' : '#1a1a2a',
              borderRadius: '4px',
              border: `1px solid ${result.status === 'success' ? '#51cf66' : 
                                  result.status === 'error' ? '#ff6b6b' : '#339af0'}`
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
                {result.status === 'success' ? '✅' : 
                 result.status === 'error' ? '❌' : '⏳'} {result.path}
              </div>
              
              {result.status === 'success' && (
                <div style={{ fontSize: '10px', opacity: 0.8 }}>
                  Load time: {result.loadTime}ms<br/>
                  Dimensions: {result.dimensions?.width}x{result.dimensions?.height}
                </div>
              )}
              
              {result.status === 'error' && (
                <div style={{ fontSize: '10px', color: '#ff6b6b' }}>
                  Error: {result.error}
                </div>
              )}
            </div>
          ))}
          
          {results.length === 0 && (
            <div style={{ opacity: 0.6 }}>Testing images...</div>
          )}
        </div>
      )}
    </div>
  );
}
