# 🧹 StreamDB Maintenance Cleanup Operations Summary

## 📋 OVERVIEW

This document lists all files and data that get cleaned up during automated maintenance on both servers. The cleanup operations help maintain optimal disk space and system performance.

---

## 🖥️ BACKEND SERVER CLEANUP (45.93.8.197)

### **System-Level Cleanup**

#### **Package Management:**
- ✅ **APT package cache** (`/var/cache/apt/archives/`)
- ✅ **Unused packages** (via `apt autoremove`)
- ✅ **Orphaned package files**
- ✅ **Old kernel versions** (keeps current + 1 previous)

#### **Temporary Files:**
- ✅ **Files in /tmp** older than 7 days
- ✅ **Files in /var/tmp** older than 7 days
- ✅ **System temporary files**

#### **System Logs:**
- ✅ **Rotated system logs** (via logrotate)
- ✅ **Old syslog files**
- ✅ **Old auth.log files**

### **Application-Specific Cleanup**

#### **PM2 Logs:**
- ✅ **StreamDB application logs** (PM2 flush)
- ✅ **PM2 error logs**
- ✅ **PM2 output logs**

#### **Nginx Logs:**
- ✅ **Access logs** older than 30 days (`/var/log/nginx/*.log`)
- ✅ **Error logs** older than 30 days
- ✅ **Compressed log files** (`.gz` files)

#### **MySQL Logs:**
- ✅ **Error logs** older than 7 days (`/var/log/mysql/*.log`)
- ✅ **Slow query logs** older than 7 days
- ✅ **Binary logs** (if configured)

#### **StreamDB Application Logs:**
- ✅ **Application logs** older than 14 days (`$STREAMDB_PATH/logs/*.log`)
- ✅ **Debug logs**
- ✅ **Error logs**

### **Maintenance-Specific Cleanup (NEW - 2+ weeks)**

#### **Log Files (CLEANED):**
- ✅ **Maintenance logs** older than 14 days (`backend-maintenance-*.log`)
- ✅ **Cron execution logs** older than 14 days (`cron-*.log`)
- ✅ **Validation logs** older than 14 days (`streamdb-validation-*.log`)
- ✅ **Temporary files** older than 14 days (excluding backups)

#### **Backup Files (PRESERVED FOREVER):**
- 🔒 **Configuration backups** (`config-backup-*.tar.gz`) - **NEVER DELETED**
- 🔒 **Database backups** (`mysql-backup-*.sql.gz`) - **NEVER DELETED**

#### **File Locations:**
```
# CLEANED (>14 days):
/var/log/streamdb-maintenance/backend-maintenance-*.log
/var/log/streamdb-maintenance/cron-*.log
/tmp/streamdb-validation-*.log

# PRESERVED FOREVER:
/var/backups/streamdb-maintenance/config-backup-*.tar.gz
/var/backups/streamdb-maintenance/mysql-backup-*.sql.gz
```

---

## 🔄 REVERSE PROXY SERVER CLEANUP (91.208.197.50)

### **System-Level Cleanup**

#### **Package Management:**
- ✅ **APT package cache** (`/var/cache/apt/archives/`)
- ✅ **Unused packages** (via `apt autoremove`)
- ✅ **Orphaned package files**
- ✅ **Old kernel versions** (keeps current + 1 previous)

#### **Temporary Files:**
- ✅ **Files in /tmp** older than 7 days
- ✅ **Files in /var/tmp** older than 7 days
- ✅ **System temporary files**

#### **System Logs:**
- ✅ **Rotated system logs** (via logrotate)
- ✅ **Old syslog files**
- ✅ **Old auth.log files**

### **Nginx-Specific Cleanup**

#### **Nginx Logs:**
- ✅ **Access logs** older than 30 days (`/var/log/nginx/*.log`)
- ✅ **Error logs** older than 30 days
- ✅ **StreamDB access logs** older than 30 days
- ✅ **FastPanel access logs** older than 30 days
- ✅ **Compressed log files** older than 30 days (`.gz` files)
- ✅ **Large log files** compressed (>100MB)

#### **File Locations:**
```
/var/log/nginx/access.log (>30 days)
/var/log/nginx/error.log (>30 days)
/var/log/nginx/streamdb_access.log (>30 days)
/var/log/nginx/streamdb_error.log (>30 days)
/var/log/nginx/fastpanel_access.log (>30 days)
/var/log/nginx/fastpanel_error.log (>30 days)
/var/log/nginx/*.log.*.gz (>30 days)
```

### **Security-Specific Cleanup**

#### **Fail2ban Logs:**
- ✅ **Fail2ban logs** older than 14 days (`/var/log/fail2ban*`)
- ✅ **Jail logs**
- ✅ **Action logs**

#### **Authentication Logs:**
- ✅ **Auth logs** older than 30 days (`/var/log/auth.log*`)
- ✅ **SSH logs**
- ✅ **Login attempt logs**

### **Maintenance-Specific Cleanup (NEW - 2+ weeks)**

#### **Log Files (CLEANED):**
- ✅ **Maintenance logs** older than 14 days (`proxy-maintenance-*.log`)
- ✅ **Cron execution logs** older than 14 days (`cron-*.log`)
- ✅ **Validation logs** older than 14 days (`streamdb-validation-*.log`)
- ✅ **Temporary files** older than 14 days (excluding backups)

#### **Backup Files (PRESERVED FOREVER):**
- 🔒 **Proxy configuration backups** (`proxy-config-backup-*.tar.gz`) - **NEVER DELETED**
- 🔒 **SSL certificate backups** (`.pem`, `.key` files) - **NEVER DELETED**
- 🔒 **Nginx configuration backups** - **NEVER DELETED**

#### **File Locations:**
```
# CLEANED (>14 days):
/var/log/streamdb-maintenance/proxy-maintenance-*.log
/var/log/streamdb-maintenance/cron-*.log
/tmp/streamdb-validation-*.log

# PRESERVED FOREVER:
/var/backups/streamdb-maintenance/proxy-config-backup-*.tar.gz
/etc/ssl/certs/cloudflare-origin.pem
/etc/ssl/private/cloudflare-origin.key
```

---

## 📊 CLEANUP SUMMARY BY TIMEFRAME

### **Daily Cleanup (Every Maintenance Run):**
- System temporary files (>7 days)
- Package cache
- Unused packages
- Log rotation

### **Weekly Cleanup (Every Maintenance Run):**
- MySQL logs (>7 days)
- Old kernel versions

### **Bi-Weekly Cleanup (Every Maintenance Run):**
- StreamDB application logs (>14 days)
- Fail2ban logs (>14 days)

### **Monthly Cleanup (Every Maintenance Run):**
- Nginx logs (>30 days)
- Auth logs (>30 days)

### **NEW: 2-Week Log Cleanup (BACKUP FILES PRESERVED):**
- **Maintenance log files** (>14 days)
- **Cron execution logs** (>14 days)
- **Validation logs** (>14 days)
- **Temporary files** (>14 days)

### **PRESERVED (NEVER DELETED):**
- ✅ **Configuration backup files** (`config-backup-*.tar.gz`) - KEPT FOREVER
- ✅ **Database backup files** (`mysql-backup-*.sql.gz`) - KEPT FOREVER

---

## 💾 DISK SPACE IMPACT

### **Typical Space Freed Per Maintenance Run:**

#### **Backend Server:**
- **System cleanup**: 100-500 MB
- **Application logs**: 50-200 MB
- **Database logs**: 10-50 MB
- **Maintenance logs**: 5-20 MB (backup files preserved)
- **Temporary files**: 5-50 MB
- **Total**: ~170-820 MB per run

#### **Reverse Proxy Server:**
- **System cleanup**: 50-200 MB
- **Nginx logs**: 100-500 MB
- **Security logs**: 20-100 MB
- **Maintenance logs**: 5-20 MB (backup files preserved)
- **Temporary files**: 5-50 MB
- **Total**: ~180-870 MB per run

### **Annual Space Savings:**
- **Backend Server**: ~8-43 GB per year
- **Reverse Proxy**: ~9-45 GB per year
- **Combined**: ~17-88 GB per year

### **Backup Storage Growth (Preserved Files):**
- **Configuration backups**: ~50-100 MB per week (2.6-5.2 GB per year)
- **Database backups**: ~100-500 MB per week (5.2-26 GB per year)
- **SSL/Proxy backups**: ~10-50 MB per week (0.5-2.6 GB per year)

---

## 🔒 SAFETY MEASURES

### **What's NEVER Deleted:**
- ✅ **Current configuration files**
- ✅ **Active log files**
- ✅ **Recent backups** (less than 14 days)
- ✅ **Database data files**
- ✅ **Application source code**
- ✅ **SSL certificates**
- ✅ **User data**

### **Retention Policies:**
- **System logs**: Managed by logrotate
- **Application logs**: 14-30 days depending on type
- **Backup files**: 14 days (2 weeks)
- **Maintenance logs**: 14 days (2 weeks)
- **Database backups**: 14 days (2 weeks)

### **Recovery Options:**
- **Recent backups**: Available for 14 days
- **Configuration backups**: Created before each maintenance
- **Database backups**: Created before each maintenance
- **Log archives**: Compressed versions retained longer

---

## 📈 MONITORING CLEANUP OPERATIONS

### **Log Messages to Watch For:**
```
[INFO] Preserving configuration and database backup files (never deleted)
[INFO] Removed X old maintenance log files (>14 days)
[INFO] Removed X old cron log files (>14 days)
[INFO] Removed X old validation log files (>14 days)
[SUCCESS] Cleaned up X old log files (>14 days) - backup files preserved
```

### **Verification Commands:**
```bash
# Check backup file count
ls -la /var/backups/streamdb-maintenance/ | wc -l

# Check log file count
ls -la /var/log/streamdb-maintenance/ | wc -l

# Check disk space
df -h

# Check cleanup results in maintenance logs
grep "Cleaned up.*old log files.*backup files preserved" /var/log/streamdb-maintenance/*.log

# Verify backup files are preserved
ls -la /var/backups/streamdb-maintenance/config-backup-*.tar.gz
ls -la /var/backups/streamdb-maintenance/mysql-backup-*.sql.gz
ls -la /var/backups/streamdb-maintenance/proxy-config-backup-*.tar.gz
```

---

**Document Version:** 1.0  
**Last Updated:** 2025-01-02  
**New Feature:** 2-week backup and log cleanup automation
