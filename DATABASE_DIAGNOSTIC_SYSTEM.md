# 🔍 StreamDB MySQL Database Diagnostic and Monitoring System

## 📋 Overview

The StreamDB Database Diagnostic and Monitoring System is a comprehensive solution for monitoring, diagnosing, and maintaining your MySQL database health. It provides real-time monitoring, automated error detection, schema validation, and actionable recommendations for optimal database performance.

## 🎯 Key Features

### ✅ Database Schema Validation
- **Admin Panel Compatibility**: Verifies all required tables and fields exist for Admin Panel functionality
- **Index Optimization**: Checks for missing indexes and provides performance recommendations
- **Field Validation**: Ensures proper data types and constraints are in place
- **Relationship Integrity**: Validates foreign key relationships and constraints

### 🔄 Real-Time Health Monitoring
- **Connection Pool Monitoring**: Tracks connection pool health and utilization
- **Socket Connection Validation**: Monitors MySQL socket connectivity for production security
- **Performance Metrics**: Real-time tracking of query performance and response times
- **Automated Alerts**: Configurable alerting for critical issues and performance degradation

### 🚨 Advanced Error Detection
- **Error Classification**: Comprehensive categorization of MySQL errors with specific solutions
- **Context-Aware Solutions**: Provides environment-specific recommendations
- **Preventive Measures**: Suggests proactive steps to prevent future issues
- **Recovery Guidance**: Step-by-step recovery procedures for critical failures

### 📊 Performance Analysis
- **Query Performance**: Monitors slow queries and execution times
- **Resource Utilization**: Tracks memory, CPU, and disk usage
- **Connection Analysis**: Monitors connection patterns and bottlenecks
- **Lock Contention**: Detects and analyzes database locking issues

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    StreamDB Application                     │
├─────────────────────────────────────────────────────────────┤
│  Database Diagnostic API Routes (/api/database/*)          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ DatabaseDiagnostic│  │ DatabaseMonitor │  │ ErrorClassifier│ │
│  │   - Schema Check  │  │ - Health Monitor│  │ - Error Analysis│ │
│  │   - Performance   │  │ - Real-time     │  │ - Solutions    │ │
│  │   - Validation    │  │ - Alerting      │  │ - Prevention   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    MySQL Database                          │
│  Socket: /var/run/mysqld/mysqld.sock (Production)          │
│  Database: stream_db                                        │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start Guide

### 1. Command Line Usage

#### Run Comprehensive Diagnostic
```bash
# Full diagnostic (recommended for initial setup)
node server/scripts/run-database-diagnostic.js --full --save-report

# Quick health check
node server/scripts/run-database-diagnostic.js --quick

# Schema validation only
node server/scripts/run-database-diagnostic.js --schema-only

# Performance analysis only
node server/scripts/run-database-diagnostic.js --performance
```

#### Output Formats
```bash
# JSON output for automation
node server/scripts/run-database-diagnostic.js --quick --json

# Save detailed reports
node server/scripts/run-database-diagnostic.js --full --save-report
```

### 2. API Endpoints

#### Health Check (Public)
```bash
# Quick health status
curl https://streamdb.online/api/database/health
```

#### Admin Endpoints (Authentication Required)
```bash
# Comprehensive diagnostic
curl -H "Authorization: Bearer [token]" https://streamdb.online/api/database/diagnostic

# Schema validation
curl -H "Authorization: Bearer [token]" https://streamdb.online/api/database/schema

# Performance metrics
curl -H "Authorization: Bearer [token]" https://streamdb.online/api/database/performance

# Start monitoring service
curl -X POST -H "Authorization: Bearer [token]" https://streamdb.online/api/database/monitor/start

# Get monitoring status
curl -H "Authorization: Bearer [token]" https://streamdb.online/api/database/monitor/status
```

### 3. Continuous Monitoring

#### Start Monitoring Service
```javascript
// Via API
const response = await fetch('/api/database/monitor/start', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + token },
  body: JSON.stringify({
    options: {
      healthCheckInterval: 30000,     // 30 seconds
      performanceCheckInterval: 300000, // 5 minutes
      schemaCheckInterval: 3600000,   // 1 hour
      alertThreshold: 3               // Alert after 3 failures
    }
  })
});
```

#### Monitor Events
```javascript
// The monitoring service emits events for integration
monitor.on('alert', (alert) => {
  console.log(`🚨 ${alert.type}: ${alert.message}`);
});

monitor.on('recovery', (recovery) => {
  console.log(`✅ Database recovered after ${recovery.previousFailures} failures`);
});
```

## 📊 Diagnostic Reports

### Sample Health Check Response
```json
{
  "success": true,
  "healthy": true,
  "responseTime": 45,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "database": "stream_db",
  "connection": "socket"
}
```

### Sample Diagnostic Results
```json
{
  "success": true,
  "results": {
    "timestamp": "2024-01-15T10:30:00.000Z",
    "environment": "production",
    "summary": {
      "passed": 8,
      "failed": 0,
      "warnings": 2,
      "critical": 0
    },
    "recommendations": [
      {
        "priority": "MEDIUM",
        "category": "Performance",
        "issue": "Missing indexes on table content",
        "solution": "Add missing indexes: idx_category, idx_published for better query performance",
        "impact": "medium"
      }
    ]
  }
}
```

## 🔧 Configuration Options

### Environment Variables
```env
# Database Configuration
DB_HOST=localhost
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=stream_db
DB_USER=stream_db_admin
DB_PASSWORD=your_password

# Monitoring Configuration
DB_MONITOR_HEALTH_INTERVAL=30000
DB_MONITOR_PERFORMANCE_INTERVAL=300000
DB_MONITOR_SCHEMA_INTERVAL=3600000
DB_MONITOR_ALERT_THRESHOLD=3
```

### Monitoring Options
```javascript
const monitorOptions = {
  healthCheckInterval: 30000,        // Health check frequency (ms)
  performanceCheckInterval: 300000,  // Performance check frequency (ms)
  schemaCheckInterval: 3600000,      // Schema validation frequency (ms)
  maxRetries: 3,                     // Max retry attempts
  retryDelay: 5000,                  // Delay between retries (ms)
  alertThreshold: 3,                 // Consecutive failures before alert
  enableLogging: true,               // Enable file logging
  logPath: './logs/database-monitor.log' // Log file path
};
```

## 🚨 Error Classification

### Connection Errors
- **ECONNREFUSED**: MySQL server not running
- **ENOTFOUND**: Database host not found
- **ER_ACCESS_DENIED_ERROR**: Invalid credentials
- **ENOENT**: Socket file not found
- **ETIMEDOUT**: Connection timeout

### Performance Issues
- **Slow Queries**: Execution time > 1000ms
- **High Connection Usage**: > 80% of max connections
- **Lock Contention**: Lock wait time > 5 seconds

### Resource Problems
- **ER_TOO_MANY_CONNECTIONS**: Connection limit reached
- **ER_DISK_FULL**: Insufficient disk space
- **ER_OUT_OF_MEMORY**: Memory exhaustion

## 🛡️ Security Features

### Safe Operations
- **Read-Only Diagnostics**: No data modification during diagnostics
- **Admin Authentication**: All diagnostic endpoints require admin authentication
- **Socket Security**: Uses MySQL socket for maximum security in production
- **Error Sanitization**: Sensitive information filtered from error responses

### Access Control
```javascript
// All diagnostic endpoints require admin authentication
router.get('/diagnostic', authenticateAdmin, async (req, res) => {
  // Diagnostic logic
});
```

## 📈 Performance Optimization

### Recommended Monitoring Schedule
- **Health Checks**: Every 30 seconds
- **Performance Analysis**: Every 5 minutes
- **Schema Validation**: Every hour
- **Full Diagnostic**: Daily via cron job

### Cron Job Setup
```bash
# Add to crontab for daily comprehensive diagnostic
0 6 * * * cd /var/www/streamdb_onl_usr/data/www/streamdb.online && node server/scripts/run-database-diagnostic.js --full --save-report >> /var/log/streamdb-diagnostic.log 2>&1
```

## 🔄 Integration with Existing Systems

### Error Handling Integration
The diagnostic system integrates seamlessly with your existing error handling:

```javascript
// Enhanced database connection with diagnostic integration
const db = require('./config/database');
const DatabaseErrorClassifier = require('./services/database-error-classifier');

const classifier = new DatabaseErrorClassifier();

async function executeQuery(query, params) {
  try {
    return await db.execute(query, params);
  } catch (error) {
    const classification = classifier.classifyError(error);
    console.error('Database Error Classification:', classification);
    throw error;
  }
}
```

### Logging Integration
```javascript
// Integrate with existing logging systems
monitor.on('alert', (alert) => {
  // Send to your existing logging service
  logger.error('Database Alert', alert);
  
  // Send notifications if needed
  if (alert.type === 'HEALTH_CHECK_FAILURE') {
    notificationService.sendAlert(alert);
  }
});
```

## 🎯 Next Steps

1. **Initial Setup**: Run comprehensive diagnostic to establish baseline
2. **Start Monitoring**: Enable continuous monitoring service
3. **Review Reports**: Analyze diagnostic reports and implement recommendations
4. **Set Up Alerts**: Configure alerting for critical issues
5. **Schedule Maintenance**: Set up automated diagnostic runs via cron

## 📞 Support and Troubleshooting

### Common Issues
1. **Permission Denied**: Ensure proper database user privileges
2. **Socket Not Found**: Verify MySQL socket path in configuration
3. **Connection Timeout**: Check network connectivity and server load
4. **Schema Validation Failures**: Review database schema against requirements

### Getting Help
- Check diagnostic reports for specific recommendations
- Review error classifications for detailed solutions
- Monitor logs for detailed error information
- Use the error classification API for unknown issues

---

**🔒 Safety Guarantee**: All diagnostic operations are read-only and safe for production use. The system will never modify your database structure or data.
