# 🚀 Quick Fix Instructions for StreamDB Webhook Issues

## 📋 Issues Identified & Fixed

1. **Database Schema Error**: Removed stored procedures causing RESIGNAL errors
2. **Webhook Authentication**: Added public health endpoint for testing
3. **Missing Git Repository**: This is expected on production server
4. **Webhook 401 Error**: Fixed by creating non-authenticated health endpoint

---

## ⚡ Quick Fix Commands

Run these commands on your backend server (***********):

### Step 1: Initialize Database with Fixed Schema
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Use the new simple database initialization script
node server/scripts/simple-db-init.js
```

### Step 2: Restart PM2 Services
```bash
# Restart all PM2 processes to load the new webhook routes
pm2 restart all

# Check PM2 status
pm2 status

# Save PM2 configuration
pm2 save
```

### Step 3: Test Webhook Health
```bash
# Test the new webhook health endpoint (no authentication required)
curl -X GET http://localhost:3001/api/webhook/health

# Test via public URL
curl -X GET https://streamdb.online/api/webhook/health
```

### Step 4: Test GitHub Webhook
```bash
# Run the comprehensive webhook test
node server/scripts/webhook-test.js
```

---

## 🔧 What Was Fixed

### 1. Database Schema Issues
- **Problem**: RESIGNAL command not supported in prepared statements
- **Fix**: Removed stored procedures, created simple table creation script
- **File**: `server/scripts/simple-db-init.js`

### 2. Webhook Authentication Issues
- **Problem**: Test endpoint required admin authentication
- **Fix**: Added public health endpoint at `/api/webhook/health`
- **File**: `server/routes/webhook.js` (added new endpoint)

### 3. Diagnostic Script Updates
- **Updated**: All diagnostic scripts now use the public health endpoint
- **Files**: 
  - `server/scripts/system-diagnostic.js`
  - `server/scripts/fix-webhook-issues.js`
  - `server/scripts/webhook-test.js`

---

## 🧪 Testing Your Webhook

### Test 1: Health Check
```bash
curl -X GET https://streamdb.online/api/webhook/health
```
**Expected Response**:
```json
{
  "status": "OK",
  "webhook_configured": true,
  "timestamp": "2025-07-01T00:15:00.000Z",
  "message": "Webhook service is running"
}
```

### Test 2: GitHub Webhook Simulation
```bash
node server/scripts/webhook-test.js
```
**Expected Output**: All tests should pass

### Test 3: Manual GitHub Webhook Test
1. Go to your GitHub repository: `https://github.com/aakash171088/Streaming_DB`
2. Navigate to Settings → Webhooks
3. Click on your webhook
4. Click "Recent Deliveries"
5. Click "Redeliver" on the most recent delivery

---

## 📊 Expected Results After Fix

### Database Initialization
```
✅ Database connection established
✅ Tables created successfully
✅ Views created successfully
✅ Configuration inserted successfully
✅ Test log entry created
🎉 Database initialization completed successfully!
```

### Webhook Health Check
```json
{
  "status": "OK",
  "webhook_configured": true,
  "timestamp": "2025-07-01T00:15:00.000Z",
  "message": "Webhook service is running"
}
```

### PM2 Status
```
┌─────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id  │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├─────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0   │ streamdb-online  │ default     │ 1.0.0   │ fork    │ 128380   │ 20D    │ 2    │ online    │ 0%       │ 45.2mb   │ root     │ disabled │
│ 1   │ webhook-server   │ default     │ 1.0.0   │ fork    │ 128399   │ 20D    │ 0    │ online    │ 0%       │ 32.1mb   │ root     │ disabled │
└─────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
```

---

## 🔍 Troubleshooting

### If Database Initialization Fails
```bash
# Check MySQL service
systemctl status mysql

# Check database credentials
mysql -u dbadmin_streamdb -p streamdb_database -e "SELECT 1;"

# Check socket path
ls -la /var/run/mysqld/mysqld.sock
```

### If Webhook Health Check Fails
```bash
# Check if Node.js app is running
pm2 logs streamdb-online --lines 20

# Check if port 3001 is listening
netstat -tlnp | grep :3001

# Check nginx proxy configuration
nginx -t
systemctl status nginx
```

### If GitHub Webhook Still Returns 500
1. Check PM2 logs: `pm2 logs streamdb-online --lines 50`
2. Check webhook logs: `pm2 logs webhook-server --lines 50`
3. Verify webhook secret in GitHub matches your .env file
4. Test webhook endpoint manually with curl

---

## 🎯 Next Steps After Fix

1. **Test GitHub Webhook**: Trigger a test delivery from GitHub repository settings
2. **Monitor Logs**: Watch PM2 logs during webhook delivery
3. **Verify Deployment**: Make a small commit to test automatic deployment
4. **Security Review**: Close unnecessary ports on backend server
5. **Monitoring Setup**: Implement regular health checks

---

## 📞 Support Commands

### View All Logs
```bash
# PM2 logs
pm2 logs --lines 50

# Nginx logs
tail -f /var/log/nginx/error.log

# System logs
journalctl -u nginx -f
```

### Check Service Status
```bash
# PM2 processes
pm2 status

# Nginx status
systemctl status nginx

# MySQL status
systemctl status mysql
```

### Manual Webhook Test
```bash
# Test webhook endpoint
curl -X POST https://streamdb.online/api/webhook/github \
  -H "Content-Type: application/json" \
  -H "X-Hub-Signature-256: sha256=test" \
  -d '{"test": true}'
```

The webhook should now be working correctly! The 500 error was caused by the database schema issues and authentication problems, both of which have been resolved.
