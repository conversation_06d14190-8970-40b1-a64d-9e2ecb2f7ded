/**
 * StreamDB Database Diagnostic System Integration Tests
 * Comprehensive tests for database diagnostic and monitoring functionality
 * 
 * Test Categories:
 * - Basic connectivity and health checks
 * - Schema validation for Admin Panel compatibility
 * - Error classification and solution recommendations
 * - Performance monitoring and metrics
 * - API endpoint functionality
 * 
 * Safety: All tests are read-only and safe for production
 */

const DatabaseDiagnostic = require('../services/database-diagnostic');
const DatabaseMonitor = require('../services/database-monitor');
const DatabaseErrorClassifier = require('../services/database-error-classifier');

describe('StreamDB Database Diagnostic System', () => {
  let diagnostic;
  let monitor;
  let classifier;

  beforeAll(() => {
    diagnostic = new DatabaseDiagnostic();
    classifier = new DatabaseErrorClassifier();
  });

  afterAll(async () => {
    // Clean up monitor if running
    if (monitor && monitor.isRunning) {
      await monitor.stop();
    }
  });

  describe('Database Connectivity', () => {
    test('should connect to database successfully', async () => {
      const result = await diagnostic.testDatabaseConnection();
      expect(result).toBe(true);
      
      // Check that diagnostic results contain connection info
      const lastTest = diagnostic.diagnosticResults.tests[diagnostic.diagnosticResults.tests.length - 1];
      expect(lastTest.level).toBe('PASS');
      expect(lastTest.test).toBe('Database Connection');
    }, 30000);

    test('should handle connection failures gracefully', async () => {
      // Create diagnostic with invalid config
      const invalidDiagnostic = new DatabaseDiagnostic();
      invalidDiagnostic.dbConfig.password = 'invalid_password';
      
      const result = await invalidDiagnostic.testDatabaseConnection();
      expect(result).toBe(false);
      
      // Check that recommendations were generated
      expect(invalidDiagnostic.diagnosticResults.recommendations.length).toBeGreaterThan(0);
    }, 30000);
  });

  describe('Schema Validation', () => {
    test('should validate required tables exist', async () => {
      const result = await diagnostic.validateDatabaseSchema();
      
      // Should pass if database is properly set up
      if (result) {
        expect(result).toBe(true);
        
        // Check that all required tables were validated
        const schemaTests = diagnostic.diagnosticResults.tests.filter(
          test => test.test === 'Schema Validation'
        );
        expect(schemaTests.length).toBeGreaterThan(0);
      } else {
        // If schema validation fails, should have recommendations
        expect(diagnostic.diagnosticResults.recommendations.length).toBeGreaterThan(0);
      }
    }, 30000);

    test('should identify missing tables', async () => {
      // This test checks the diagnostic logic for missing tables
      const requiredTables = Object.keys(diagnostic.requiredSchema);
      expect(requiredTables).toContain('content');
      expect(requiredTables).toContain('admin_users');
      expect(requiredTables).toContain('categories');
      expect(requiredTables).toContain('seasons');
      expect(requiredTables).toContain('episodes');
    });
  });

  describe('Error Classification', () => {
    test('should classify connection refused error', () => {
      const error = { code: 'ECONNREFUSED', message: 'Connection refused' };
      const classification = classifier.classifyError(error);
      
      expect(classification.code).toBe('ECONNREFUSED');
      expect(classification.category).toBe('connection');
      expect(classification.severity).toBe('CRITICAL');
      expect(classification.solutions).toContain('Check if MySQL service is running: sudo systemctl status mysql');
    });

    test('should classify access denied error', () => {
      const error = { code: 'ER_ACCESS_DENIED_ERROR', message: 'Access denied' };
      const classification = classifier.classifyError(error);
      
      expect(classification.code).toBe('ER_ACCESS_DENIED_ERROR');
      expect(classification.category).toBe('authentication');
      expect(classification.severity).toBe('HIGH');
      expect(classification.solutions.length).toBeGreaterThan(0);
    });

    test('should handle unknown errors', () => {
      const error = { code: 'UNKNOWN_ERROR', message: 'Unknown error' };
      const classification = classifier.classifyError(error);
      
      expect(classification.code).toBe('UNKNOWN_ERROR');
      expect(classification.category).toBe('unknown');
      expect(classification.title).toBe('Unknown Database Error');
    });

    test('should extract error codes from various formats', () => {
      expect(classifier.extractErrorCode('ECONNREFUSED')).toBe('ECONNREFUSED');
      expect(classifier.extractErrorCode({ code: 'ER_ACCESS_DENIED_ERROR' })).toBe('ER_ACCESS_DENIED_ERROR');
      expect(classifier.extractErrorCode({ errno: 'ETIMEDOUT' })).toBe('ETIMEDOUT');
    });
  });

  describe('Performance Monitoring', () => {
    test('should check database performance metrics', async () => {
      const result = await diagnostic.checkDatabasePerformance();
      
      // Should return boolean result
      expect(typeof result).toBe('boolean');
      
      // Check that performance tests were recorded
      const performanceTests = diagnostic.diagnosticResults.tests.filter(
        test => test.test === 'Performance Check'
      );
      expect(performanceTests.length).toBeGreaterThan(0);
    }, 30000);

    test('should monitor connection pool health', async () => {
      const result = await diagnostic.monitorConnectionPool();
      
      // Should return boolean result
      expect(typeof result).toBe('boolean');
      
      // Check that connection pool tests were recorded
      const poolTests = diagnostic.diagnosticResults.tests.filter(
        test => test.test === 'Connection Pool'
      );
      expect(poolTests.length).toBeGreaterThan(0);
    }, 30000);

    test('should analyze performance metrics', () => {
      const metrics = {
        averageQueryTime: 1500, // Above threshold
        connectionUsage: 85,     // Above threshold
        averageLockWaitTime: 2   // Below threshold
      };
      
      const analysis = classifier.analyzePerformanceMetrics(metrics);
      
      expect(analysis.issues.length).toBeGreaterThan(0);
      expect(analysis.summary.total_issues).toBeGreaterThan(0);
      
      // Should detect slow query issue
      const slowQueryIssue = analysis.issues.find(issue => issue.type === 'SLOW_QUERY');
      expect(slowQueryIssue).toBeDefined();
      
      // Should detect high connection usage
      const connectionIssue = analysis.issues.find(issue => issue.type === 'HIGH_CONNECTION_USAGE');
      expect(connectionIssue).toBeDefined();
    });
  });

  describe('Comprehensive Diagnostic', () => {
    test('should run full diagnostic suite', async () => {
      const results = await diagnostic.runComprehensiveDiagnostic();
      
      expect(results).toBeDefined();
      expect(results.timestamp).toBeDefined();
      expect(results.environment).toBeDefined();
      expect(results.tests).toBeInstanceOf(Array);
      expect(results.summary).toBeDefined();
      
      // Should have run multiple test categories
      expect(results.tests.length).toBeGreaterThan(0);
      
      // Summary should have proper structure
      expect(results.summary).toHaveProperty('passed');
      expect(results.summary).toHaveProperty('failed');
      expect(results.summary).toHaveProperty('warnings');
      expect(results.summary).toHaveProperty('critical');
    }, 60000);
  });

  describe('Database Monitor', () => {
    test('should create monitor instance', () => {
      monitor = new DatabaseMonitor({
        healthCheckInterval: 5000,
        performanceCheckInterval: 10000,
        enableLogging: false
      });
      
      expect(monitor).toBeDefined();
      expect(monitor.isRunning).toBe(false);
    });

    test('should start and stop monitoring', async () => {
      if (!monitor) {
        monitor = new DatabaseMonitor({
          healthCheckInterval: 5000,
          enableLogging: false
        });
      }
      
      // Start monitoring
      await monitor.start();
      expect(monitor.isRunning).toBe(true);
      
      // Wait a moment for initial checks
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check status
      const status = monitor.getStatus();
      expect(status.isRunning).toBe(true);
      expect(status.metrics.totalChecks).toBeGreaterThan(0);
      
      // Stop monitoring
      await monitor.stop();
      expect(monitor.isRunning).toBe(false);
    }, 15000);

    test('should emit events during monitoring', (done) => {
      if (!monitor) {
        monitor = new DatabaseMonitor({
          healthCheckInterval: 2000,
          enableLogging: false
        });
      }
      
      let eventReceived = false;
      
      monitor.on('healthCheck', (data) => {
        expect(data).toHaveProperty('isHealthy');
        expect(data).toHaveProperty('responseTime');
        expect(data).toHaveProperty('timestamp');
        
        if (!eventReceived) {
          eventReceived = true;
          monitor.stop().then(() => done());
        }
      });
      
      monitor.start();
      
      // Timeout after 10 seconds
      setTimeout(() => {
        if (!eventReceived) {
          monitor.stop().then(() => done(new Error('No health check event received')));
        }
      }, 10000);
    }, 15000);
  });

  describe('Error Categories', () => {
    test('should provide error category information', () => {
      const categories = classifier.getErrorCategories();
      
      expect(categories).toBeInstanceOf(Array);
      expect(categories.length).toBeGreaterThan(0);
      
      // Check that all expected categories exist
      const categoryValues = categories.map(cat => cat.value);
      expect(categoryValues).toContain('connection');
      expect(categoryValues).toContain('authentication');
      expect(categoryValues).toContain('performance');
      expect(categoryValues).toContain('resource');
    });

    test('should provide category descriptions', () => {
      const description = classifier.getCategoryDescription('connection');
      expect(description).toBe('Issues related to establishing database connections');
      
      const unknownDescription = classifier.getCategoryDescription('unknown_category');
      expect(unknownDescription).toBe('Unknown category');
    });
  });

  describe('Integration Safety', () => {
    test('should not modify database during diagnostics', async () => {
      // Record initial state
      const initialResults = await diagnostic.runComprehensiveDiagnostic();
      
      // Run diagnostic again
      const secondResults = await diagnostic.runComprehensiveDiagnostic();
      
      // Results should be consistent (no data modification)
      expect(secondResults.tests.length).toBeGreaterThan(0);
      expect(secondResults.summary).toBeDefined();
    }, 60000);

    test('should handle errors gracefully without crashing', async () => {
      // Test with various error conditions
      const testErrors = [
        'ECONNREFUSED',
        { code: 'ER_ACCESS_DENIED_ERROR' },
        { errno: 'ETIMEDOUT' },
        'Unknown error string',
        null,
        undefined
      ];
      
      for (const error of testErrors) {
        expect(() => {
          classifier.classifyError(error);
        }).not.toThrow();
      }
    });
  });
});

// Helper function for manual testing
if (require.main === module) {
  console.log('🧪 Running Database Diagnostic Tests Manually...');
  
  async function runManualTests() {
    const diagnostic = new DatabaseDiagnostic();
    
    console.log('\n1. Testing database connection...');
    const connected = await diagnostic.testDatabaseConnection();
    console.log(`Connection result: ${connected}`);
    
    console.log('\n2. Validating schema...');
    const schemaValid = await diagnostic.validateDatabaseSchema();
    console.log(`Schema validation result: ${schemaValid}`);
    
    console.log('\n3. Testing error classification...');
    const classifier = new DatabaseErrorClassifier();
    const errorClassification = classifier.classifyError({ code: 'ECONNREFUSED' });
    console.log('Error classification:', errorClassification.title);
    
    console.log('\n4. Running comprehensive diagnostic...');
    const results = await diagnostic.runComprehensiveDiagnostic();
    console.log(`Diagnostic completed: ${results.summary.passed} passed, ${results.summary.failed} failed`);
    
    console.log('\n✅ Manual tests completed successfully!');
  }
  
  runManualTests().catch(error => {
    console.error('❌ Manual tests failed:', error);
    process.exit(1);
  });
}
