
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

export default function Disclaimer() {
  const navigate = useNavigate();
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />
      <main className="flex-1 flex flex-col items-center px-2 py-10">
        <h1
          className="text-3xl sm:text-4xl md:text-5xl font-bold mb-7 text-center uppercase"
          style={{ color: '#e6cb8e', textShadow: '0 0 10px #e6cb8e, 0 0 20px #e6cb8e60' }}
        >
          Disclaimer
        </h1>
        <div className="max-w-3xl w-full bg-card rounded-2xl shadow-xl p-6 sm:p-8 mb-8 border border-border">
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            We index the content that is already available on other websites. StreamDB does not host or store any files on its server. All contents are provided by non-affiliated third parties. StreamDB does not accept responsibility for content hosted on third-party websites and does not have any involvement in the same.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            They are only indexed much like how GOOGLE works.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            We do not store any copyright-protected content on our website/servers. All the posts are indexed only for educational purposes and any linked content is stored only in third-party websites.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            This is a promotional website only, all the content indexed on this site (All materials) is for testing/promotion purposes only.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            This site merely indexes other sites’ contents. The hosting server or the administrator cannot be held responsible for the contents of any linked sites or any link contained in a linked site, or changes / updates to such sites. All materials on this website are for Educational Purposes ONLY.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            This Website/Blog does not accept any responsibility for content hosted on third party websites and does not have any involvement in the downloading/uploading of content. We just index links available on internet.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            We highly ENCOURAGE users to BUY the CDs or DVDs of the content. Please, buy original contents from author or developer site!
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            If You do not agree to all the terms, please disconnect from this site now itself.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            By remaining at this site, you affirm your understanding and compliance of the above disclaimer and absolve this site of any responsibility henceforth.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            All content found on this site have been collected from various sources across the web and are believed to be in the "public domain".
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-4">
            For any copyright issues, you should contact the hosting site itself who’ve stored content.
          </p>
          <p className="text-foreground text-base sm:text-lg leading-relaxed mb-0">
            If you want to remove any post from StreamDB please contact us with the necessary information.
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => navigate("/")}
          className="mb-2 text-lg border-primary text-primary hover:bg-primary hover:text-primary-foreground"
        >
          Back to Home
        </Button>
      </main>
      <Footer />
    </div>
  );
}
