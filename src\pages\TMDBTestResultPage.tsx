import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Film, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/Header';
import { scrollToTop } from '@/utils/scrollToTop';
import { runComprehensiveTMDBTests } from '@/utils/tmdbTestUtils';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
  data?: any;
  error?: any;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
}

interface TMDBTestResults {
  suites: TestSuite[];
  summary: {
    totalTests: number;
    totalPassed: number;
    totalFailed: number;
    totalDuration: number;
    successRate: number;
  };
}

const TMDBTestResultPage: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TMDBTestResults | null>(null);
  const [testStarted, setTestStarted] = useState(false);

  const runTMDBTests = async () => {
    setIsRunning(true);
    setTestStarted(true);

    try {
      const testResults = await runComprehensiveTMDBTests();
      setResults(testResults);
    } catch (error) {
      console.error('TMDB tests failed:', error);
      setResults({
        summary: {
          total: 0,
          passed: 0,
          failed: 1,
          duration: 0
        },
        suites: [{
          name: 'TMDB Integration Tests',
          tests: [{
            name: 'Test Execution',
            passed: false,
            message: `Error running tests: ${error instanceof Error ? error.message : 'Unknown error'}`,
            duration: 0,
            error
          }]
        }]
      });
    } finally {
      setIsRunning(false);
    }
  };

  const resetTest = () => {
    setResults(null);
    setTestStarted(false);
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusBadge = (passed: boolean) => {
    return (
      <Badge variant={passed ? "default" : "destructive"} className="ml-2">
        {passed ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  const formatDuration = (ms: number) => {
    return ms < 1000 ? `${ms}ms` : `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-primary mb-2">
              TMDB API Integration Tests
            </h1>
            <p className="text-muted-foreground">
              Comprehensive testing of TMDB API connectivity and data fetching
            </p>
          </div>
          
          <Link to="/admin" onClick={scrollToTop}>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Admin
            </Button>
          </Link>
        </div>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Film className="w-5 h-5" />
              TMDB Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={runTMDBTests}
                disabled={isRunning}
                className="flex-1"
              >
                {isRunning ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Running TMDB Tests...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Run TMDB API Tests
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={resetTest}
                disabled={isRunning}
                className="flex-1"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset Test
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testStarted && results && (
          <>
            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {results.summary.successRate === 100 ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : results.summary.successRate >= 80 ? (
                    <AlertCircle className="w-5 h-5 text-yellow-500" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500" />
                  )}
                  Test Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-green-500">{results.summary?.passed || 0}</div>
                    <div className="text-sm text-muted-foreground">Tests Passed</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-red-500">{results.summary?.failed || 0}</div>
                    <div className="text-sm text-muted-foreground">Tests Failed</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-primary">{Math.round(((results.summary?.passed || 0) / (results.summary?.total || 1)) * 100)}%</div>
                    <div className="text-sm text-muted-foreground">Success Rate</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-blue-500">{formatDuration(results.summary?.duration || 0)}</div>
                    <div className="text-sm text-muted-foreground">Total Duration</div>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="w-4 h-4" />
                  <AlertDescription>
                    {(() => {
                      const successRate = Math.round(((results.summary?.passed || 0) / (results.summary?.total || 1)) * 100);
                      return successRate === 100 ? (
                        <span className="text-green-600">🎉 All TMDB integration tests passed!</span>
                      ) : successRate >= 80 ? (
                        <span className="text-yellow-600">✅ Most TMDB tests passed. Minor issues may exist.</span>
                      ) : (
                        <span className="text-red-600">⚠️ Some TMDB tests failed. Please review the implementation.</span>
                      );
                    })()}
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Detailed Test Suites */}
            <div className="space-y-6">
              {results.suites?.map((suite, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        {suite.tests?.every(test => test.passed) ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500" />
                        )}
                        {suite.name}
                      </span>
                      <div className="flex items-center gap-2 text-sm">
                        <Badge variant="outline">
                          {suite.tests?.filter(test => test.passed).length || 0}/{suite.tests?.length || 0} passed
                        </Badge>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatDuration(suite.tests?.reduce((total, test) => total + test.duration, 0) || 0)}
                        </Badge>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {suite.tests?.map((result, resultIndex) => (
                        <div key={resultIndex} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(result?.passed || false)}
                            <div>
                              <div className="font-medium">{result?.name || 'Unknown Test'}</div>
                              <div className="text-sm text-muted-foreground">{result?.message || 'No message available'}</div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {formatDuration(result?.duration || 0)}
                            </Badge>
                            {getStatusBadge(result?.passed || false)}
                          </div>
                        </div>
                      )) || (
                        <div className="text-center text-muted-foreground py-4">
                          No test results available
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="w-4 h-4" />
              <AlertDescription>
                <strong>How to run TMDB API tests:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Click "Run TMDB API Tests" to test all TMDB integration functionality</li>
                  <li>• Tests include ID validation, movie/TV data fetching, search, and image URLs</li>
                  <li>• Each test suite shows individual test results with timing information</li>
                  <li>• Green indicators show passing tests, red indicators show failures</li>
                  <li>• Check browser console for detailed technical information and error logs</li>
                  <li>• Ensure TMDB API key is properly configured in environment variables</li>
                </ul>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TMDBTestResultPage;
