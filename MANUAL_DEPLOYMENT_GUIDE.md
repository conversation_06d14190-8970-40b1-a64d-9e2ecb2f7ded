# 🚀 StreamDB Manual Deployment Guide

## ✅ Build Completed Successfully!

Your application has been built successfully with all fixes applied:
- ✅ 6 JavaScript files generated
- ✅ 1 CSS file generated  
- ✅ index.html correctly references built assets
- ✅ localStorage fixes included
- ✅ All production optimizations applied

---

## 📁 Files Ready for Upload

The following files in your local `dist/` directory are ready for production:

```
dist/
├── index.html                    # Main HTML file
├── assets/
│   ├── index-k432b-bl.css       # Main CSS file
│   ├── index-C_8tibvJ.js        # Main JavaScript bundle
│   ├── tmdbVerification-CILB-YO1.js
│   ├── adminTestUtils-Dx1W2vVI.js
│   ├── tmdbTestUtils-C2VObkkf.js
│   ├── apiService-CtX12-L_.js
│   └── authTestUtils-DcLdsRSj.js
├── favicon.ico
├── android-chrome-192x192.png
├── android-chrome-512x512.png
├── apple-touch-icon.png
├── favicon-16x16.png
├── favicon-32x32.png
├── site.webmanifest
├── robots.txt
└── [other static assets]
```

---

## 🔧 Step-by-Step Deployment

### Step 1: Upload Files to Your Server

**Option A: Using FastPanel File Manager**
1. Log into your FastPanel at your Alexhost VPS
2. Navigate to `/var/www/streamdb_onl_usr/data/www/streamdb.online/`
3. **Backup current files** (rename `dist` to `dist_backup`)
4. Upload the entire `dist/` folder from your local machine
5. Upload the entire `server/` folder (if updated)

**Option B: Using FTP/SFTP Client**
1. Connect to `***********` with your credentials
2. Navigate to `/var/www/streamdb_onl_usr/data/www/streamdb.online/`
3. Upload `dist/` and `server/` folders

**Option C: Using SCP (if you have SSH access)**
```bash
# Upload dist folder
scp -r dist/ streamdb_onl_usr@***********:/var/www/streamdb_onl_usr/data/www/streamdb.online/

# Upload server folder  
scp -r server/ streamdb_onl_usr@***********:/var/www/streamdb_onl_usr/data/www/streamdb.online/
```

### Step 2: Configure Apache (.htaccess)

Create or update `.htaccess` file in `/var/www/streamdb_onl_usr/data/www/streamdb.online/`:

```apache
# StreamDB Production Apache Configuration
# Fixes MIME type errors and enables proper static file serving

RewriteEngine On

# Proper MIME types for static assets
<FilesMatch "\.(js|mjs)$">
    Header set Content-Type "application/javascript; charset=utf-8"
    Header set X-Content-Type-Options "nosniff"
</FilesMatch>

<FilesMatch "\.css$">
    Header set Content-Type "text/css; charset=utf-8"
</FilesMatch>

<FilesMatch "\.json$">
    Header set Content-Type "application/json; charset=utf-8"
</FilesMatch>

# Cache headers for assets
<LocationMatch "^/assets/">
    Header set Cache-Control "public, max-age=********"
</LocationMatch>

# HYBRID SETUP: Proxy API requests to Node.js (internal port 3001)
# This allows Apache to serve static files while Node.js handles API
RewriteCond %{REQUEST_URI} ^/api/(.*)$
RewriteRule ^api/(.*)$ http://127.0.0.1:3001/api/$1 [P,L]

# Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]

# Security headers
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-Content-Type-Options "nosniff"
Header always set X-XSS-Protection "1; mode=block"
```

### Step 3: Install Server Dependencies

SSH into your server or use FastPanel terminal:

```bash
# Navigate to server directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Install production dependencies
npm install --production
```

### Step 4: Start/Restart Services

```bash
# If using PM2 (recommended)
pm2 restart streamdb-online

# Or if starting for the first time
pm2 start index.js --name streamdb-online --env production
pm2 save

# Restart Apache
sudo systemctl restart apache2
# OR
sudo service apache2 restart
```

### Step 5: Verify Deployment

1. **Visit your website**: https://streamdb.online
2. **Check browser console** (F12 → Console tab):
   - ✅ Should see no MIME type errors
   - ✅ Should see no localStorage errors
   - ✅ Should see "localStorage is available and working"
3. **Test functionality**:
   - ✅ Dark theme should be visible
   - ✅ Navigation should work
   - ✅ Admin panel should be accessible
   - ✅ Mobile responsiveness should work

---

## 🔍 Troubleshooting

### If MIME Type Errors Persist:

1. **Check Apache modules**:
   ```bash
   apache2ctl -M | grep -E "(rewrite|proxy|headers)"
   ```

2. **Enable required modules**:
   ```bash
   sudo a2enmod rewrite
   sudo a2enmod proxy
   sudo a2enmod proxy_http
   sudo a2enmod headers
   sudo systemctl restart apache2
   ```

3. **Verify .htaccess permissions**:
   ```bash
   chmod 644 /var/www/streamdb_onl_usr/data/www/streamdb.online/.htaccess
   ```

### If localStorage Errors Continue:

1. **Clear browser cache** completely
2. **Test in incognito/private mode**
3. **Check browser privacy settings**
4. **Verify the safe localStorage wrapper is working**

### If Node.js API Doesn't Work:

1. **Check PM2 status**:
   ```bash
   pm2 list
   pm2 logs streamdb-online
   ```

2. **Test database connection**:
   ```bash
   cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
   node test-db-connection.js
   ```

---

## ✅ Success Checklist

After deployment, verify these items:

- [ ] Website loads at https://streamdb.online
- [ ] No console errors in browser F12 tools
- [ ] CSS loads properly (dark theme visible)
- [ ] JavaScript functionality works
- [ ] Admin panel accessible at https://streamdb.online/admin
- [ ] Mobile responsiveness works (test on phone)
- [ ] All navigation links work
- [ ] Content displays correctly

---

## 📞 Need Help?

If you encounter any issues:

1. **Check logs**:
   - Apache: `/var/log/apache2/error.log`
   - Node.js: `pm2 logs streamdb-online`

2. **Verify file permissions**:
   ```bash
   ls -la /var/www/streamdb_onl_usr/data/www/streamdb.online/
   ```

3. **Test individual components**:
   - Static files: https://streamdb.online/assets/index-k432b-bl.css
   - API: https://streamdb.online/api/health (if endpoint exists)

The deployment should resolve all the production issues you were experiencing! 🎉
