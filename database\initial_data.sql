-- Initial data for Streaming Database
-- Run this after creating the schema

-- Insert default categories (matching your current category system)
INSERT INTO categories (name, type, slug, description) VALUES
('Hindi Movies', 'movie', 'hindi-movies', 'Bollywood and Hindi cinema'),
('English Movies', 'movie', 'english-movies', 'Hollywood and English language films'),
('Telugu Movies', 'movie', 'telugu-movies', 'Tollywood and Telugu cinema'),
('Tamil Movies', 'movie', 'tamil-movies', 'Kollywood and Tamil cinema'),
('Malayalam Movies', 'movie', 'malayalam-movies', 'Mollywood and Malayalam cinema'),
('Korean Movies', 'movie', 'korean-movies', 'Korean cinema and K-movies'),
('Japanese Movies', 'movie', 'japanese-movies', 'Japanese cinema and J-movies'),
('Hindi Web Series', 'series', 'hindi-web-series', 'Hindi language web series and shows'),
('English Web Series', 'series', 'english-web-series', 'English language web series and shows'),
('Telugu Web Series', 'series', 'telugu-web-series', 'Telugu language web series and shows'),
('Tamil Web Series', 'series', 'tamil-web-series', 'Tamil language web series and shows'),
('Malayalam Web Series', 'series', 'malayalam-web-series', 'Malayalam language web series and shows'),
('Korean Web Series', 'series', 'korean-web-series', 'Korean dramas and web series'),
('Japanese Web Series', 'series', 'japanese-web-series', 'Japanese dramas and web series'),
('Anime', 'both', 'anime', 'Animated series and movies from Japan'),
('Hindi Dubbed', 'both', 'hindi-dubbed', 'Foreign content dubbed in Hindi'),
('English Dubbed', 'both', 'english-dubbed', 'Foreign content dubbed in English'),
('Animation', 'both', 'animation', 'Animated movies and series');

-- Insert default genres
INSERT INTO genres (name, slug) VALUES
('Action', 'action'),
('Adventure', 'adventure'),
('Comedy', 'comedy'),
('Drama', 'drama'),
('Fantasy', 'fantasy'),
('Thriller', 'thriller'),
('Horror', 'horror'),
('Sci-Fi', 'sci-fi'),
('Romance', 'romance'),
('Crime', 'crime'),
('Mystery', 'mystery'),
('Animation', 'animation'),
('Family', 'family'),
('Documentary', 'documentary'),
('Biography', 'biography'),
('History', 'history'),
('War', 'war'),
('Western', 'western'),
('Musical', 'musical'),
('Sport', 'sport');

-- Insert default languages
INSERT INTO languages (name, code) VALUES
('Hindi', 'hi'),
('English', 'en'),
('Tamil', 'ta'),
('Telugu', 'te'),
('Malayalam', 'ml'),
('Korean', 'ko'),
('Japanese', 'ja'),
('Spanish', 'es'),
('French', 'fr'),
('German', 'de'),
('Italian', 'it'),
('Portuguese', 'pt'),
('Russian', 'ru'),
('Chinese', 'zh'),
('Arabic', 'ar');

-- Insert default quality options
INSERT INTO quality_options (name, sort_order) VALUES
('480p', 1),
('720p', 2),
('1080p', 3),
('4K', 4),
('HD', 5),
('Full HD', 6),
('Ultra HD', 7),
('Blu-ray', 8),
('Web-DL', 9),
('WEB-RIP', 10);

-- Insert default audio tracks
INSERT INTO audio_tracks (name, code) VALUES
('Hindi', 'hi'),
('English', 'en'),
('Tamil', 'ta'),
('Telugu', 'te'),
('Malayalam', 'ml'),
('Korean', 'ko'),
('Japanese', 'ja'),
('Spanish', 'es'),
('French', 'fr'),
('German', 'de'),
('Dual Audio', 'dual'),
('Multi Audio', 'multi');

-- Create default admin user (password: admin123 - CHANGE THIS!)
-- Password hash for 'admin123' using bcrypt with 12 rounds
INSERT INTO admin_users (username, password_hash, email, role, is_active) VALUES
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', '<EMAIL>', 'admin', TRUE);

-- Note: The above password hash is for 'admin123'
-- You should change this password immediately after setup!
-- To generate a new hash, use: bcrypt.hash('your_new_password', 12)

-- Sample content data (based on your current movies.ts)
-- This will be populated from your CSV file and existing data

-- Insert sample movie
INSERT INTO content (
    id, title, description, year, type, category_id, image, cover_image,
    tmdb_id, poster_url, imdb_rating, runtime, studio, tags,
    is_published, is_featured, add_to_carousel, created_at
) VALUES (
    '1',
    'Edge of Tomorrow',
    'A soldier fighting aliens gets to relive the same day over and over again, the day restarting every time he dies.',
    2014,
    'movie',
    (SELECT id FROM categories WHERE slug = 'english-movies'),
    'https://image.tmdb.org/t/p/w300/2uHiFbinrCbRzXd0NUATqMoQmZC.jpg',
    'https://image.tmdb.org/t/p/original/vf8wSMFGAU2x7l099GSuF3CH9jB.jpg',
    '137113',
    'https://image.tmdb.org/t/p/w300/2uHiFbinrCbRzXd0NUATqMoQmZC.jpg',
    7.9,
    '113 min',
    'Warner Bros.',
    'time loop, aliens, action, sci-fi',
    TRUE,
    TRUE,
    TRUE,
    '2024-01-15 10:30:00'
);

-- Insert genres for the sample movie
INSERT INTO content_genres (content_id, genre_id) VALUES
('1', (SELECT id FROM genres WHERE slug = 'action')),
('1', (SELECT id FROM genres WHERE slug = 'sci-fi'));

-- Insert languages for the sample movie
INSERT INTO content_languages (content_id, language_id) VALUES
('1', (SELECT id FROM languages WHERE code = 'en'));

-- Insert quality options for the sample movie
INSERT INTO content_quality (content_id, quality_id) VALUES
('1', (SELECT id FROM quality_options WHERE name = '720p')),
('1', (SELECT id FROM quality_options WHERE name = '1080p'));

-- Insert audio tracks for the sample movie
INSERT INTO content_audio (content_id, audio_id) VALUES
('1', (SELECT id FROM audio_tracks WHERE code = 'en'));

-- Sample web series
INSERT INTO content (
    id, title, description, year, type, category_id, image, cover_image,
    tmdb_id, imdb_rating, runtime, studio, tags,
    is_published, is_featured, add_to_carousel, total_seasons, total_episodes, created_at
) VALUES (
    '2',
    'Breaking Bad',
    'A high school chemistry teacher turned methamphetamine manufacturer.',
    2008,
    'series',
    (SELECT id FROM categories WHERE slug = 'english-web-series'),
    'https://image.tmdb.org/t/p/w300/ggFHVNu6YYI5L9pCfOacjizRGt.jpg',
    'https://image.tmdb.org/t/p/original/tsRy63Mu5cu8etL1X20ZZeMVdfU.jpg',
    '1396',
    9.5,
    '47 min',
    'Sony Pictures',
    'crime, drama, thriller',
    TRUE,
    TRUE,
    FALSE,
    5,
    62,
    '2024-01-10 09:00:00'
);

-- Insert genres for the sample series
INSERT INTO content_genres (content_id, genre_id) VALUES
('2', (SELECT id FROM genres WHERE slug = 'drama')),
('2', (SELECT id FROM genres WHERE slug = 'crime')),
('2', (SELECT id FROM genres WHERE slug = 'thriller'));

-- Insert languages for the sample series
INSERT INTO content_languages (content_id, language_id) VALUES
('2', (SELECT id FROM languages WHERE code = 'en'));

-- Insert quality options for the sample series
INSERT INTO content_quality (content_id, quality_id) VALUES
('2', (SELECT id FROM quality_options WHERE name = '1080p')),
('2', (SELECT id FROM quality_options WHERE name = '4K'));

-- Insert audio tracks for the sample series
INSERT INTO content_audio (content_id, audio_id) VALUES
('2', (SELECT id FROM audio_tracks WHERE code = 'en'));

-- Sample season for Breaking Bad
INSERT INTO seasons (id, content_id, season_number, title, description, created_at) VALUES
('season-2-1', '2', 1, 'Season 1', 'Walter White begins his transformation.', '2024-01-10 09:00:00');

-- Sample episodes for Breaking Bad Season 1
INSERT INTO episodes (id, season_id, content_id, episode_number, title, description, runtime, air_date, created_at) VALUES
('episode-2-1-1', 'season-2-1', '2', 1, 'Pilot', 'Walter White, a struggling high school chemistry teacher, is diagnosed with inoperable lung cancer.', '58 min', '2008-01-20', '2024-01-10 09:00:00'),
('episode-2-1-2', 'season-2-1', '2', 2, 'Cat\'s in the Bag...', 'Walt and Jesse attempt to tie up loose ends.', '48 min', '2008-01-27', '2024-01-10 09:00:00'),
('episode-2-1-3', 'season-2-1', '2', 3, '...And the Bag\'s in the River', 'Walter deals with the aftermath of his first kill.', '48 min', '2008-02-10', '2024-01-10 09:00:00');

-- Create indexes for better performance
CREATE INDEX idx_content_search ON content(title, year, type);
CREATE INDEX idx_episodes_content ON episodes(content_id, episode_number);
CREATE INDEX idx_seasons_content ON seasons(content_id, season_number);
