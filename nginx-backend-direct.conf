# Nginx Configuration for StreamDB Backend Server (***********)
# Direct serving (not reverse proxy)
# Deploy to: /etc/nginx/sites-available/streamdb.online

server {
    listen 80;
    server_name streamdb.online www.streamdb.online ***********;
    
    # Document root for static files
    root /var/www/streamdb_onl_usr/data/www/streamdb.online/dist;
    index index.html;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # API Routes - Proxy to Node.js backend
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Static Assets with Caching
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # Proper MIME types
        location ~* \.(js|mjs)$ {
            add_header Content-Type "application/javascript; charset=utf-8";
        }
        location ~* \.css$ {
            add_header Content-Type "text/css; charset=utf-8";
        }
    }
    
    # Favicon and Icons
    location ~* \.(ico|png|jpg|jpeg|gif|svg|webp)$ {
        expires 30d;
        add_header Cache-Control "public";
    }
    
    # Fonts
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Prevent serving source files
    location ~* \.(tsx?|jsx?|map)$ {
        deny all;
    }
    
    # SPA Fallback - Serve index.html for all routes
    location / {
        try_files $uri $uri/ /index.html;
        
        # No cache for HTML files
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # Health check endpoint (bypass for monitoring)
    location = /health {
        proxy_pass http://localhost:3001/api/health;
        access_log off;
    }
    
    # Robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ /(\.env|\.git|node_modules|server|database) {
        deny all;
    }
    
    # Logging
    access_log /var/log/nginx/streamdb_access.log;
    error_log /var/log/nginx/streamdb_error.log;
}

# HTTPS Configuration (when you get SSL certificate)
server {
    listen 443 ssl http2;
    server_name streamdb.online www.streamdb.online;
    
    # SSL Configuration (uncomment when you have certificates)
    # ssl_certificate /etc/letsencrypt/live/streamdb.online/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/streamdb.online/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # For now, just redirect to HTTP (remove this when you have SSL)
    return 301 http://$server_name$request_uri;
    
    # Copy all the location blocks from the HTTP server above
    # (uncomment and use when SSL is configured)
}
