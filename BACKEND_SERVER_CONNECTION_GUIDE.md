# Backend Server Connection Guide for StreamDB Online

## 🔧 Comprehensive Backend Server Setup & Connection Fix

### Current Server Configuration
- **Backend VPS IP**: ***********
- **Frontend Domain**: streamdb.online
- **Database**: MySQL with FastPanel
- **Server Stack**: Node.js + Express + MySQL + PM2

---

## 🚨 Critical Issues Identified & Solutions

### 1. Database Connection Issues

**Problem**: Socket connection failing on local development
**Solution**: Configure proper database connection for both development and production

#### Fix Database Configuration:

1. **Update server/.env file** on your VPS (***********):
```bash
# Database Configuration for Alexhost VPS
DB_HOST=localhost
DB_USER=dbadmin_streamdb
DB_PASSWORD=your_mysql_password
DB_NAME=streamdb_online
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_PORT=3306
NODE_ENV=production

# Security
JWT_SECRET=your_secure_jwt_secret_change_this
SESSION_SECRET=your_secure_session_secret_change_this

# Server Configuration
PORT=3001
FRONTEND_URL=https://streamdb.online
```

2. **Test Database Connection** on VPS:
```bash
# SSH into your VPS
ssh root@***********

# Navigate to server directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Test database connection
node test-db-connection.js
```

### 2. PM2 Process Manager Setup

**Problem**: PM2 not installed or configured
**Solution**: Install and configure PM2 for production

```bash
# Install PM2 globally on VPS
npm install -g pm2

# Start your application
pm2 start index.js --name "streamdb-online"

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

### 3. Nginx Proxy Configuration

**Problem**: API routes not properly proxied
**Solution**: Configure nginx to proxy /api/* requests to Node.js

Create/update nginx configuration:
```nginx
# /etc/nginx/fastpanel2-sites/streamdb_onl_usr/streamdb.online.conf

server {
    listen 80;
    server_name streamdb.online www.streamdb.online;
    root /var/www/streamdb_onl_usr/data/www/streamdb.online/dist;
    index index.html;

    # Serve static files
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Proxy API requests to Node.js backend
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 4. File Permissions Fix

**Problem**: Incorrect file permissions
**Solution**: Set proper permissions for security

```bash
# On your VPS, set correct permissions
chmod 600 /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env
chmod 755 /var/www/streamdb_onl_usr/data/www/streamdb.online/server/uploads
chmod 755 /var/www/streamdb_onl_usr/data/www/streamdb.online/server/logs
```

---

## 🔐 Secure Connection Setup for Augment Code

### Option 1: SSH Key Authentication (Recommended)

1. **Generate SSH Key Pair** (if not already done):
```bash
ssh-keygen -t rsa -b 4096 -C "augment-code-access"
```

2. **Add Public Key to VPS**:
```bash
# Copy public key to VPS
ssh-copy-id -i ~/.ssh/id_rsa.pub root@***********

# Or manually add to authorized_keys
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
```

3. **Test Connection**:
```bash
ssh -i ~/.ssh/id_rsa root@***********
```

### Option 2: Secure Tunnel for Database Access

1. **Create SSH Tunnel**:
```bash
# Forward local port 3307 to remote MySQL
ssh -L 3307:localhost:3306 root@***********
```

2. **Connect via Tunnel**:
```bash
# Use localhost:3307 to connect to remote MySQL
mysql -h 127.0.0.1 -P 3307 -u dbadmin_streamdb -p streamdb_online
```

### Option 3: VPN Access (Most Secure)

1. **Setup WireGuard VPN** on VPS
2. **Configure client access** for secure connection
3. **Access internal services** through VPN tunnel

---

## 🚀 Deployment Steps

### 1. Build and Deploy Frontend
```bash
# Local development machine
npm run build

# Upload dist/ folder to VPS
scp -r dist/* root@***********:/var/www/streamdb_onl_usr/data/www/streamdb.online/
```

### 2. Deploy Backend
```bash
# Upload server files
scp -r server/* root@***********:/var/www/streamdb_onl_usr/data/www/streamdb.online/server/

# SSH into VPS and install dependencies
ssh root@***********
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
npm install --production
```

### 3. Start Services
```bash
# Start Node.js application
pm2 start index.js --name streamdb-online

# Reload nginx
systemctl reload nginx

# Check status
pm2 status
systemctl status nginx
```

---

## 🔍 Troubleshooting Commands

### Check Service Status
```bash
# Check PM2 processes
pm2 list
pm2 logs streamdb-online

# Check nginx status
systemctl status nginx
nginx -t

# Check MySQL status
systemctl status mysql
mysql -u dbadmin_streamdb -p -e "SHOW DATABASES;"
```

### Test Connectivity
```bash
# Test API endpoint
curl -X GET https://streamdb.online/api/health

# Test database connection
node server/test-db-connection.js

# Check port availability
netstat -tlnp | grep :3001
```

### Monitor Logs
```bash
# PM2 logs
pm2 logs streamdb-online --lines 50

# Nginx logs
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log

# MySQL logs
tail -f /var/log/mysql/error.log
```

---

## 📞 Emergency Recovery

If services fail:

1. **Restart PM2**:
```bash
pm2 restart streamdb-online
```

2. **Restart Nginx**:
```bash
systemctl restart nginx
```

3. **Restart MySQL**:
```bash
systemctl restart mysql
```

4. **Full System Restart**:
```bash
reboot
```

---

## 🎯 Next Steps

1. **Run the comprehensive fix**: `node comprehensive-codebase-fix.js`
2. **Deploy to VPS**: Follow deployment steps above
3. **Test all endpoints**: Verify API functionality
4. **Monitor performance**: Set up logging and monitoring
5. **Backup configuration**: Create system backups

For immediate assistance with server connection, please provide:
- SSH access details (if comfortable)
- Current error messages
- Server logs from PM2/nginx/MySQL
