<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StreamDB - Movie & Series Database</title>
    <meta name="description" content="Your ultimate streaming database for movies and web series. Discover, explore, and enjoy your favorite content." />
    <meta name="author" content="StreamDB" />
    <meta name="keywords" content="movies, series, streaming, database, entertainment, films, tv shows" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <meta name="theme-color" content="#e6cb8e" />
    <meta name="msapplication-TileColor" content="#0a0a0a" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://streamdb.online/" />
    <meta property="og:title" content="StreamDB - Movie & Series Database" />
    <meta property="og:description" content="Your ultimate streaming database for movies and web series. Discover, explore, and enjoy your favorite content." />
    <meta property="og:image" content="https://streamdb.online/android-chrome-512x512.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://streamdb.online/" />
    <meta property="twitter:title" content="StreamDB - Movie & Series Database" />
    <meta property="twitter:description" content="Your ultimate streaming database for movies and web series. Discover, explore, and enjoy your favorite content." />
    <meta property="twitter:image" content="https://streamdb.online/android-chrome-512x512.png" />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://api.themoviedb.org" />
    <link rel="preconnect" href="https://image.tmdb.org" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
