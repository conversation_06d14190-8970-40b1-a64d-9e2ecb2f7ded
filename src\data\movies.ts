import { MediaItem } from "../types/media";

/**
 * Production-ready media data file
 * 
 * This file serves as a fallback and type definition for the frontend.
 * In production, all content is loaded from the database via API calls.
 * 
 * The mediaData array is intentionally empty as content is now:
 * - Stored securely in MySQL database
 * - Loaded via secure API endpoints
 * - Protected with authentication
 * - No dummy/test data in production
 */

export const mediaData: MediaItem[] = [
  // This array will be populated from the database via API
  // All content is now stored securely in the database
  // Fallback data to prevent undefined errors while API is loading
  {
    id: "fallback-1",
    title: "Loading Content...",
    description: "Content is being loaded from the database",
    year: 2024,
    genres: ["Loading"],
    type: "movie",
    image: "/placeholder-image.jpg",
    coverImage: "/placeholder-image.jpg",
    createdAt: new Date().toISOString(),
    isPublished: true,
    isFeatured: false,
    addToCarousel: false
  }
];

/**
 * Helper function to get content from API
 * This replaces the static data approach
 */
export const getContentFromAPI = async () => {
  try {
    // This will be implemented with the API service
    const response = await fetch('/api/content');
    const data = await response.json();
    return data.success ? data.data : [];
  } catch (error) {
    console.error('Error fetching content from API:', error);
    return [];
  }
};

/**
 * Content categories for reference
 * These match the database categories
 */
export const contentCategories = [
  'Hindi Movies',
  'English Movies', 
  'Telugu Movies',
  'Tamil Movies',
  'Malayalam Movies',
  'Korean Movies',
  'Japanese Movies',
  'Hindi Web Series',
  'English Web Series',
  'Telugu Web Series', 
  'Tamil Web Series',
  'Malayalam Web Series',
  'Korean Web Series',
  'Japanese Web Series',
  'Anime',
  'Hindi Dubbed',
  'English Dubbed',
  'Animation'
];

/**
 * Content types for reference
 */
export const contentTypes = ['movie', 'series', 'requested'] as const;

/**
 * Export for backward compatibility
 * Components expecting mediaData will get empty array
 * and should use API calls instead
 */
export default mediaData;
