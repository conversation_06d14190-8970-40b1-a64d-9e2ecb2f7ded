const mysql = require('mysql2/promise');
require('dotenv').config();

async function testConnection() {
  try {
    console.log('Testing database connection...');
    
    const connection = await mysql.createConnection({
      socketPath: process.env.DB_SOCKET,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4'
    });
    
    console.log('✅ Database connection successful!');
    
    // Test a simple query
    const [rows] = await connection.execute('SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = ?', [process.env.DB_NAME]);
    console.log(`✅ Database has ${rows[0].table_count} tables`);
    
    // Test character set
    const [charset] = await connection.execute('SELECT @@character_set_database, @@collation_database');
    console.log(`✅ Character set: ${charset[0]['@@character_set_database']}, Collation: ${charset[0]['@@collation_database']}`);
    
    await connection.end();
    console.log('✅ All database tests passed!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
}

testConnection();
