#!/usr/bin/env node

/**
 * MySQL Health Check Service
 * Provides a simple HTTP endpoint to check MySQL connectivity
 * Runs on port 3307 (NOT direct MySQL access)
 */

const http = require('http');
const mysql = require('mysql2/promise');
require('dotenv').config();

const PORT = 3307;

// Database configuration (same as your main app)
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timeout: 10000,
  acquireTimeout: 10000,
  reconnect: true
};

// Use socket connection for production (most secure)
if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
  dbConfig.socketPath = process.env.DB_SOCKET;
} else {
  dbConfig.host = process.env.DB_HOST || 'localhost';
  dbConfig.port = process.env.DB_PORT || 3306;
}

// Health check function
async function checkMySQLHealth() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    await connection.execute('SELECT 1 as health_check');
    await connection.end();
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: process.env.DB_NAME,
      connection: process.env.DB_SOCKET ? 'socket' : 'tcp'
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: process.env.DB_NAME
    };
  }
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  // Set CORS headers for monitoring tools
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET');
  res.setHeader('Content-Type', 'application/json');

  if (req.method === 'GET' && req.url === '/health') {
    try {
      const healthStatus = await checkMySQLHealth();
      const statusCode = healthStatus.status === 'healthy' ? 200 : 503;
      
      res.writeHead(statusCode);
      res.end(JSON.stringify(healthStatus, null, 2));
    } catch (error) {
      res.writeHead(500);
      res.end(JSON.stringify({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check service error'
      }, null, 2));
    }
  } else if (req.method === 'GET' && req.url === '/') {
    res.writeHead(200);
    res.end(JSON.stringify({
      service: 'MySQL Health Check',
      endpoints: ['/health'],
      timestamp: new Date().toISOString()
    }, null, 2));
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({
      error: 'Not Found',
      message: 'Use /health endpoint'
    }, null, 2));
  }
});

// Start server
server.listen(PORT, '127.0.0.1', () => {
  console.log(`✅ MySQL Health Check service running on port ${PORT}`);
  console.log(`🔗 Health endpoint: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down MySQL health check service');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down MySQL health check service');
  server.close(() => {
    process.exit(0);
  });
});
