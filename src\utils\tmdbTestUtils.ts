/**
 * TMDB Integration Test Utilities
 * Comprehensive testing for TMDB API integration
 */

// TMDB API configuration
const TMDB_API_KEY = import.meta.env.VITE_TMDB_API_KEY;
const TMDB_BASE_URL = import.meta.env.VITE_TMDB_BASE_URL || 'https://api.themoviedb.org/3';
const TMDB_IMAGE_BASE_URL = import.meta.env.VITE_TMDB_IMAGE_BASE_URL || 'https://image.tmdb.org/t/p';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
  data?: any;
  error?: any;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
}

/**
 * Test TMDB API key configuration
 */
export async function testTMDBApiKey(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    if (!TMDB_API_KEY) {
      return {
        name: 'TMDB API Key Configuration',
        passed: false,
        message: 'TMDB API key is not configured',
        duration: Date.now() - startTime
      };
    }

    if (TMDB_API_KEY.length < 20) {
      return {
        name: 'TMDB API Key Configuration',
        passed: false,
        message: 'TMDB API key appears to be invalid (too short)',
        duration: Date.now() - startTime
      };
    }

    return {
      name: 'TMDB API Key Configuration',
      passed: true,
      message: 'TMDB API key is properly configured',
      duration: Date.now() - startTime,
      data: { keyLength: TMDB_API_KEY.length }
    };
  } catch (error) {
    return {
      name: 'TMDB API Key Configuration',
      passed: false,
      message: 'Error checking TMDB API key configuration',
      duration: Date.now() - startTime,
      error
    };
  }
}

/**
 * Test TMDB API connectivity
 */
export async function testTMDBConnectivity(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${TMDB_BASE_URL}/configuration?api_key=${TMDB_API_KEY}`);
    
    if (!response.ok) {
      return {
        name: 'TMDB API Connectivity',
        passed: false,
        message: `TMDB API returned ${response.status}: ${response.statusText}`,
        duration: Date.now() - startTime
      };
    }

    const data = await response.json();
    
    return {
      name: 'TMDB API Connectivity',
      passed: true,
      message: 'Successfully connected to TMDB API',
      duration: Date.now() - startTime,
      data: { 
        baseUrl: data.images?.base_url,
        posterSizes: data.images?.poster_sizes?.length || 0
      }
    };
  } catch (error) {
    return {
      name: 'TMDB API Connectivity',
      passed: false,
      message: 'Failed to connect to TMDB API',
      duration: Date.now() - startTime,
      error
    };
  }
}

/**
 * Test TMDB movie search
 */
export async function testTMDBMovieSearch(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const searchQuery = 'The Matrix';
    const response = await fetch(
      `${TMDB_BASE_URL}/search/movie?api_key=${TMDB_API_KEY}&query=${encodeURIComponent(searchQuery)}`
    );
    
    if (!response.ok) {
      return {
        name: 'TMDB Movie Search',
        passed: false,
        message: `Movie search failed with ${response.status}: ${response.statusText}`,
        duration: Date.now() - startTime
      };
    }

    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      return {
        name: 'TMDB Movie Search',
        passed: false,
        message: 'Movie search returned no results',
        duration: Date.now() - startTime,
        data
      };
    }

    return {
      name: 'TMDB Movie Search',
      passed: true,
      message: `Found ${data.results.length} movies for "${searchQuery}"`,
      duration: Date.now() - startTime,
      data: { 
        totalResults: data.total_results,
        firstResult: data.results[0]?.title
      }
    };
  } catch (error) {
    return {
      name: 'TMDB Movie Search',
      passed: false,
      message: 'Error during movie search',
      duration: Date.now() - startTime,
      error
    };
  }
}

/**
 * Test TMDB TV series search
 */
export async function testTMDBTVSearch(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const searchQuery = 'Breaking Bad';
    const response = await fetch(
      `${TMDB_BASE_URL}/search/tv?api_key=${TMDB_API_KEY}&query=${encodeURIComponent(searchQuery)}`
    );
    
    if (!response.ok) {
      return {
        name: 'TMDB TV Search',
        passed: false,
        message: `TV search failed with ${response.status}: ${response.statusText}`,
        duration: Date.now() - startTime
      };
    }

    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      return {
        name: 'TMDB TV Search',
        passed: false,
        message: 'TV search returned no results',
        duration: Date.now() - startTime,
        data
      };
    }

    return {
      name: 'TMDB TV Search',
      passed: true,
      message: `Found ${data.results.length} TV series for "${searchQuery}"`,
      duration: Date.now() - startTime,
      data: { 
        totalResults: data.total_results,
        firstResult: data.results[0]?.name
      }
    };
  } catch (error) {
    return {
      name: 'TMDB TV Search',
      passed: false,
      message: 'Error during TV search',
      duration: Date.now() - startTime,
      error
    };
  }
}

/**
 * Test TMDB image URLs
 */
export async function testTMDBImageUrls(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    // Test a known poster path
    const testPosterPath = '/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg'; // The Matrix poster
    const imageUrl = `${TMDB_IMAGE_BASE_URL}/w500${testPosterPath}`;
    
    const response = await fetch(imageUrl, { method: 'HEAD' });
    
    if (!response.ok) {
      return {
        name: 'TMDB Image URLs',
        passed: false,
        message: `Image URL test failed with ${response.status}`,
        duration: Date.now() - startTime
      };
    }

    return {
      name: 'TMDB Image URLs',
      passed: true,
      message: 'TMDB image URLs are accessible',
      duration: Date.now() - startTime,
      data: { 
        testUrl: imageUrl,
        contentType: response.headers.get('content-type')
      }
    };
  } catch (error) {
    return {
      name: 'TMDB Image URLs',
      passed: false,
      message: 'Error testing TMDB image URLs',
      duration: Date.now() - startTime,
      error
    };
  }
}

/**
 * Test TMDB rate limiting
 */
export async function testTMDBRateLimit(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    // Make multiple rapid requests to test rate limiting
    const requests = Array(5).fill(null).map(() => 
      fetch(`${TMDB_BASE_URL}/configuration?api_key=${TMDB_API_KEY}`)
    );
    
    const responses = await Promise.all(requests);
    const statusCodes = responses.map(r => r.status);
    
    // Check if any requests were rate limited (429)
    const rateLimited = statusCodes.some(code => code === 429);
    
    return {
      name: 'TMDB Rate Limiting',
      passed: true,
      message: rateLimited ? 
        'Rate limiting is working (some requests were throttled)' : 
        'All requests succeeded (rate limit not reached)',
      duration: Date.now() - startTime,
      data: { statusCodes, rateLimited }
    };
  } catch (error) {
    return {
      name: 'TMDB Rate Limiting',
      passed: false,
      message: 'Error testing TMDB rate limiting',
      duration: Date.now() - startTime,
      error
    };
  }
}

/**
 * Run comprehensive TMDB tests
 */
export async function runComprehensiveTMDBTests(): Promise<{
  summary: {
    total: number;
    passed: number;
    failed: number;
    duration: number;
  };
  suites: TestSuite[];
}> {
  const startTime = Date.now();
  
  console.log('🎬 Starting TMDB Integration Tests');
  console.log('==================================');
  
  const tests = [
    testTMDBApiKey,
    testTMDBConnectivity,
    testTMDBMovieSearch,
    testTMDBTVSearch,
    testTMDBImageUrls,
    testTMDBRateLimit
  ];
  
  const results: TestResult[] = [];
  
  for (const test of tests) {
    console.log(`Running ${test.name}...`);
    const result = await test();
    results.push(result);
    
    if (result.passed) {
      console.log(`✅ ${result.name}: ${result.message}`);
    } else {
      console.log(`❌ ${result.name}: ${result.message}`);
      if (result.error) {
        console.error('Error details:', result.error);
      }
    }
  }
  
  const summary = {
    total: results.length,
    passed: results.filter(r => r.passed).length,
    failed: results.filter(r => !r.passed).length,
    duration: Date.now() - startTime
  };
  
  console.log('==================================');
  console.log(`📊 Test Summary: ${summary.passed}/${summary.total} passed`);
  console.log(`⏱️ Total duration: ${summary.duration}ms`);
  
  return {
    summary,
    suites: [{
      name: 'TMDB Integration Tests',
      tests: results
    }]
  };
}

// Export individual test functions
export default {
  testTMDBApiKey,
  testTMDBConnectivity,
  testTMDBMovieSearch,
  testTMDBTVSearch,
  testTMDBImageUrls,
  testTMDBRateLimit,
  runComprehensiveTMDBTests
};
