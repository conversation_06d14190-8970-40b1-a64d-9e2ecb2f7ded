
@import url('https://fonts.googleapis.com/css2?family=Koulen&family=Manrope:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 20% 8%;
    --foreground: 210 40% 94%;

    /* Updated theme colors: primary, accent and their foregrounds */
    --primary: 43 67% 75%;   /* #e6cb8e from screenshot */
    --primary-foreground: 40 14% 13%; /* #232323 */
    --accent: 43 67% 75%;    /* #e6cb8e */
    --accent-foreground: 40 14% 13%;

    --muted: 227 17% 16%;
    --muted-foreground: 220 14% 63%;

    --secondary: 222 17% 22%;
    --secondary-foreground: 210 40% 94%;

    --border: 223 14% 24%;
    --input: 223 14% 24%;
    --ring: 43 67% 75%;
    --card: 222.2 15% 12%;
    --card-foreground: 210 40% 94%;

    /* Popover colors for dropdown menus */
    --popover: 222.2 15% 12%;
    --popover-foreground: 210 40% 94%;

    /* Destructive colors */
    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 94%;

    --radius: 1rem;
  }

  .dark {
    --background: 0 0% 0%;          /* TRUE BLACK for dark theme */
    --foreground: 210 40% 94%;
    --card: 0 0% 3%;                /* Almost black for cards */
    --popover: 0 0% 3%;             /* Almost black for popovers */
    --popover-foreground: 210 40% 94%;
    --primary: 43 67% 75%; /* #e6cb8e for dark as well */
    --primary-foreground: 40 14% 13%;
    --accent: 43 67% 75%;
    --accent-foreground: 40 14% 13%;
    --border: 223 14% 24%;
    --input: 223 14% 24%;
    --ring: 43 67% 75%;
    --muted: 227 17% 16%;
    --muted-foreground: 220 14% 63%;
    --secondary: 222 17% 22%;
    --secondary-foreground: 210 40% 94%;
    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 94%;
  }

  html {
    font-size: 87.5%; /* was 97%, now about 10% smaller */
    font-family: 'Manrope', Arial, sans-serif;
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    min-height: 100vh;
  }

  body {
    @apply bg-background text-foreground min-h-screen font-manrope;
    letter-spacing: 0.02em;
    font-weight: 400;
    /* font-size handled by html */
  }

  a {
    @apply transition-colors duration-150;
  }

  h1, h2, h3, h4, .stdb-heading, .card-title, .main-nav {
    font-family: 'Koulen', Impact, Arial, sans-serif !important;
    font-weight: 400 !important;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    text-shadow: 0 2px 16px #1113, 0 1px 2px #0002;
  }

  .card-title {
    font-size: 1.17rem; /* was 1.27rem */
    margin-top: 0.58rem;
    margin-bottom: 0.18rem;
    display: block;
    line-height: 1.02;
    text-align: center;
    padding: 0.01em 0.06em;
    background: none !important;
    border-radius: 0 !important;
    color: hsl(var(--foreground));
    opacity: 0.99;
    text-shadow: 0 2px 14px #8be7b630, 0 1px 2px #0007;
  }

  .main-nav-link {
    font-family: 'Manrope', Arial, sans-serif !important;
    text-transform: uppercase;
    font-size: 1.05rem; /* was 1.28rem */
    letter-spacing: 0.03em;
    font-weight: 500;
    padding: 0.13em 0.32em;
    transition: color 0.14s, background 0.14s, box-shadow 0.14s;
    border-radius: 0.4em;
  }

  /* Stylish genres styling */
  .card-genres {
    font-size: 0.87rem;
    color: #e6cb8e !important;
    font-family: 'Manrope', Arial, sans-serif;
    font-weight: 400;
    letter-spacing: 0.009em;
    text-align: center;
    opacity: 0.90;
    margin-bottom: 0.04em;
    /* mimic the screenshot color and weight */
    text-shadow: 0 1px 2px #222c, 0 1px 6px #000a;
  }

  .card-year {
    font-size: 0.83rem; /* was 0.92rem */
    color: #f05723;
    font-family: 'Koulen', Impact, Arial, sans-serif;
    font-weight: 400;
    text-align: center;
    letter-spacing: 0.08em;
    opacity: 0.93;
    margin-bottom: 0.02em;
    text-shadow: 0 2px 10px #0001;
  }

  .streamdb-logo {
    filter: drop-shadow(0 2px 8px #19e950a5);
    height: 2.2rem !important; /* was 2.6rem */
    width: auto !important;
  }

  svg {
    stroke-width: 1.7 !important;
    width: 1em !important;
    height: 1em !important;
    vertical-align: middle;
  }

  /* New headline/section style for impact, especially h1, h2 */
  h1, h2 {
    font-size: 2.3rem; /* was 2.7rem */
    line-height: 1.01;
    color: #191917;
    text-shadow: 0 2px 16px #e3d1a180, 0 1px 2px #0002;
  }
}

/* CardShadow & Rounded, Section tweaks */
section {
  @apply rounded-2xl bg-card bg-opacity-85 shadow-[0_5px_32px_0_rgba(12,20,29,0.10)] p-6 mb-10 border border-border;
}

/* Grid card hover effect */
.card-grid-item,
.shadcn-card {
  @apply rounded-2xl bg-background/85 shadow-xl hover:shadow-2xl hover:scale-[1.035] transition border border-border;
  border-width: 1.3px;
  box-shadow: 0 7px 32px 0 #a67e4438;
}

/* Responsive for homepage sections */
@media (max-width: 900px) {
  section {
    @apply p-1.5 mb-5 rounded-xl;
  }
}

::-webkit-scrollbar {
  width: 7px;
  background: #22252b;
}
::-webkit-scrollbar-thumb {
  background: #e0ca8b88;
  border-radius: 8px;
}

/* Enhanced dropdown styling for dark theme consistency */
select {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  min-height: 2.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

select:hover {
  background-color: hsl(var(--muted)) !important;
  border-color: hsl(var(--primary)) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

select:focus {
  outline: none;
  border-color: hsl(var(--primary)) !important;
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2) !important;
  background-color: hsl(var(--card)) !important;
}

/* Enhanced option styling for better visibility */
select option {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  padding: 0.75rem 1rem;
  border: none;
  font-weight: 500;
  line-height: 1.5;
  min-height: 2.5rem;
  cursor: pointer;
}

select option:hover,
select option:focus {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

select option:checked,
select option:selected {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

/* Radix UI Select component styling */
[data-radix-select-content] {
  background-color: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  z-index: 50;
  min-width: 8rem;
  overflow: hidden;
  padding: 0.25rem;
}

[data-radix-select-item] {
  color: hsl(var(--popover-foreground)) !important;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  outline: none;
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
}

[data-radix-select-item]:hover,
[data-radix-select-item]:focus,
[data-radix-select-item][data-highlighted] {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

[data-radix-select-item][data-state="checked"] {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

[data-radix-select-item][data-disabled] {
  opacity: 0.5;
  pointer-events: none;
}

/* Dropdown menu component styling */
.dropdown-menu {
  background-color: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  z-index: 50;
  min-width: 8rem;
  overflow: hidden;
  padding: 0.25rem;
}

.dropdown-menu-item {
  color: hsl(var(--popover-foreground)) !important;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  outline: none;
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
}

.dropdown-menu-item:hover,
.dropdown-menu-item:focus {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

.dropdown-menu-item:active {
  background-color: hsl(var(--primary) / 0.9) !important;
}

.dropdown-menu-item[data-disabled] {
  opacity: 0.5;
  pointer-events: none;
}

/* Mobile responsiveness for dropdowns */
@media (max-width: 640px) {
  select {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    min-height: 2.25rem;
  }

  select option {
    padding: 0.6rem 0.8rem;
    min-height: 2.25rem;
  }

  [data-radix-select-item],
  .dropdown-menu-item {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }
}

/* Enhanced video player aspect ratio support */
.video-player-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 0.5rem;
}

/* Fallback aspect ratio support for older browsers */
.video-player-container.aspect-fallback {
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-player-container.aspect-fallback.ratio-4-3 {
  padding-bottom: 75%; /* 4:3 aspect ratio */
}

.video-player-container.aspect-fallback.ratio-21-9 {
  padding-bottom: 42.86%; /* 21:9 aspect ratio */
}

.video-player-container.aspect-fallback.ratio-1-1 {
  padding-bottom: 100%; /* 1:1 aspect ratio */
}

.video-player-container.aspect-fallback.ratio-9-16 {
  padding-bottom: 177.78%; /* 9:16 aspect ratio */
}

.video-player-container.aspect-fallback.ratio-3-2 {
  padding-bottom: 66.67%; /* 3:2 aspect ratio */
}

/* Responsive video player adjustments */
@media (max-width: 768px) {
  /* Force 16:9 for ultra-wide content on mobile for better usability */
  .video-player-container.mobile-friendly {
    aspect-ratio: 16 / 9;
  }

  .video-player-container.aspect-fallback.mobile-friendly {
    padding-bottom: 56.25% !important;
  }

  /* Ensure vertical videos don't become too tall on mobile */
  .video-player-container.ratio-9-16 {
    max-height: 70vh;
  }
}

/* Enhanced content page animations and effects */
.content-page-hero {
  animation: fadeInUp 0.8s ease-out;
}

.content-page-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.content-page-card:nth-child(1) { animation-delay: 0.1s; }
.content-page-card:nth-child(2) { animation-delay: 0.2s; }
.content-page-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 0.375rem;
}

/* Backdrop blur support fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-sm {
    background-color: hsl(var(--card) / 0.95) !important;
  }
}

/* Desktop Layout Fixes (1024px+) */
@media (min-width: 1024px) {
  /* Admin panel authentication card positioning fix */
  .admin-auth-card {
    position: relative;
    z-index: 15;
    margin-left: auto;
    /* Ensure proper spacing from container edges */
    margin-top: 0;
    margin-bottom: 1rem;
    /* Prevent width overflow */
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Admin panel container improvements */
  .admin-panel-container {
    position: relative;
    overflow: visible;
    /* Remove problematic top padding */
    padding-top: 2rem;
  }

  /* Admin panel top controls container - non-overlapping layout */
  .admin-panel-container > div:first-child {
    /* Ensure proper spacing and no overlap */
    width: 100%;
    margin-bottom: 2rem;
  }

  /* Ensure proper header navigation spacing */
  .header-nav {
    z-index: 50;
  }

  /* Content sections proper spacing */
  .content-section {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  /* Hero section desktop optimization */
  .content-page-hero {
    padding: 2rem 0;
  }

  /* Desktop poster positioning */
  .content-page-poster {
    position: relative;
    z-index: 5;
  }
}

/* Tablet Layout Fixes (768px-1024px) */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Grid layout transitions */
  .card-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  /* Navigation menu behavior */
  .tablet-nav {
    padding: 1rem;
  }

  /* Card grid responsive adjustments */
  .card-grid-item {
    max-width: 250px;
    margin: 0 auto;
  }

  /* Tablet-specific hero carousel */
  .hero-carousel-content {
    max-width: 70%;
  }

  /* Admin panel tablet optimizations */
  .admin-auth-card {
    min-width: 200px;
  }

  /* Content page hero for tablets */
  .content-page-hero {
    padding: 1.5rem 0;
  }

  /* Tablet portrait optimizations */
  @media (orientation: portrait) {
    .card-grid {
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 1rem;
    }

    .hero-carousel-content {
      max-width: 80%;
    }
  }

  /* Tablet landscape optimizations */
  @media (orientation: landscape) {
    .card-grid {
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1.5rem;
    }

    .hero-carousel-content {
      max-width: 60%;
    }
  }

  /* Ensure proper touch targets on tablets */
  button, a[role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Mobile optimizations for content pages */
@media (max-width: 768px) {
  .content-page-hero {
    animation-duration: 0.6s;
    padding: 1rem;
    /* Fix mobile poster bleeding */
    overflow: hidden;
  }

  .content-page-card {
    animation-duration: 0.4s;
  }

  /* Mobile poster container fix - Improved responsive approach */
  .content-page-poster-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    padding: 0;
    /* Ensure container doesn't exceed viewport */
    box-sizing: border-box;
  }

  /* Mobile poster sizing fix - Dynamic responsive sizing */
  .content-page-poster {
    /* Use viewport-based sizing with safe minimums and maximums */
    width: min(30vw, 120px) !important;
    height: min(46vw, 186px) !important;
    max-width: calc(100vw - 1.5rem) !important; /* Account for container px-3 (0.75rem each side) */
    object-fit: cover;
    border-radius: 0.75rem;
    margin: 0 auto;
    /* Ensure poster doesn't cause horizontal overflow */
    box-sizing: border-box;
  }

  /* Mobile content page hero positioning fix */
  .content-page-hero {
    padding-top: 1rem;
    padding-bottom: 1rem;
    min-height: auto;
    justify-content: center;
    align-items: center;
  }

  /* Additional mobile layout fixes */
  .mobile-safe-area {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  /* Mobile navigation improvements */
  .mobile-menu {
    z-index: 40;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
  }

  /* Mobile form improvements */
  .mobile-form {
    padding: 1rem;
  }

  .mobile-form input,
  .mobile-form select,
  .mobile-form textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
  }

  /* Reduce motion for mobile performance */
  @media (prefers-reduced-motion: reduce) {
    .content-page-hero,
    .content-page-card {
      animation: none;
    }

    * {
      transition-duration: 0.1s !important;
    }
  }

  /* Touch-friendly sizing */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved mobile spacing */
  .mobile-spacing {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  /* Mobile hero adjustments - Dynamic text scaling */
  .mobile-hero-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem) !important;
    line-height: 1.1 !important;
    margin-bottom: 0.75rem !important;
    text-align: left;
  }

  /* Mobile hero description scaling */
  .mobile-hero-description {
    font-size: clamp(0.75rem, 2.5vw, 0.875rem) !important;
    line-height: 1.25rem !important;
    margin-bottom: 0.75rem !important;
    text-align: left;
  }

  /* Mobile hero carousel content positioning */
  .hero-carousel-content {
    max-width: 85% !important;
    padding-left: 3.5rem !important; /* Increased padding to prevent overlap with nav button */
    padding-right: 2rem !important;
    padding-top: 1rem;
    padding-bottom: 1rem;
    /* Keep left alignment for better aesthetics */
  }

  /* Mobile video player optimizations */
  .video-player-mobile {
    border-radius: 0.375rem;
  }

  .video-player-mobile iframe {
    border-radius: 0.375rem;
  }

  /* Admin panel mobile improvements */
  .admin-panel-container {
    padding: 0.75rem;
  }

  /* Form elements mobile optimization */
  input, select, textarea, button {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Improved button spacing on mobile */
  .button-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }

  .button-group button {
    width: 100%;
    justify-content: center;
  }

  /* Mobile card improvements */
  .mobile-card {
    margin: 0.5rem;
    padding: 1rem;
    border-radius: 0.75rem;
  }

  /* Prevent horizontal scroll */
  body {
    overflow-x: hidden;
  }

  /* Mobile-specific text sizing */
  .mobile-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .mobile-text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

/* Cross-Breakpoint Testing and Smooth Transitions */
@media (min-width: 320px) and (max-width: 374px) {
  /* Extra small mobile devices */
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  h1 {
    font-size: 1.75rem !important;
  }

  h2 {
    font-size: 1.5rem !important;
  }

  .mobile-hero-title {
    font-size: 1.5rem !important;
  }

  .mobile-hero-description {
    font-size: 0.75rem !important;
  }

  /* Smaller buttons on very small screens */
  .btn-xs {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }

  /* Tighter spacing */
  .space-y-6 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }

  /* Extra small mobile poster adjustments */
  .content-page-poster {
    width: min(25vw, 100px) !important;
    height: min(38vw, 155px) !important;
    max-width: calc(100vw - 1.5rem) !important;
  }

  /* Extra small mobile hero carousel adjustments */
  .hero-carousel-content {
    max-width: 90% !important;
    padding-left: 3.5rem !important; /* Increased from 1rem to prevent overlap with nav button */
    padding-right: 1.5rem !important;
  }

  .mobile-hero-title {
    font-size: clamp(1.25rem, 4vw, 1.75rem) !important;
    line-height: 1.1 !important;
    margin-bottom: 0.5rem !important;
  }

  .mobile-hero-description {
    font-size: clamp(0.7rem, 2.5vw, 0.8rem) !important;
    line-height: 1.2rem !important;
    margin-bottom: 0.5rem !important;
  }
}

@media (min-width: 375px) and (max-width: 639px) {
  /* Standard mobile devices */
  .mobile-hero-title {
    font-size: clamp(1.75rem, 5vw, 2.25rem) !important;
  }

  .mobile-hero-description {
    font-size: clamp(0.8rem, 3vw, 0.9rem) !important;
  }

  /* Standard mobile poster sizing */
  .content-page-poster {
    width: min(28vw, 110px) !important;
    height: min(43vw, 170px) !important;
    max-width: calc(100vw - 1.5rem) !important;
  }

  /* Standard mobile hero carousel adjustments */
  .hero-carousel-content {
    max-width: 85% !important;
    padding-left: 3.75rem !important; /* Increased from 1.25rem to prevent overlap with nav button */
    padding-right: 1.75rem !important;
  }

  .mobile-hero-title {
    font-size: clamp(1.5rem, 4.5vw, 2rem) !important;
    line-height: 1.1 !important;
    margin-bottom: 0.75rem !important;
  }

  .mobile-hero-description {
    font-size: clamp(0.75rem, 2.8vw, 0.875rem) !important;
    line-height: 1.25rem !important;
    margin-bottom: 0.75rem !important;
  }
}

@media (min-width: 640px) and (max-width: 767px) {
  /* Large mobile / small tablet */
  .card-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .mobile-hero-title {
    font-size: 2.5rem !important;
  }

  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .tablet-text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  /* Large mobile poster sizing - transition to tablet */
  .content-page-poster {
    width: min(32vw, 130px) !important;
    height: min(49vw, 200px) !important;
    max-width: calc(100vw - 2rem) !important;
  }

  /* Large mobile hero carousel adjustments */
  .hero-carousel-content {
    max-width: 80% !important;
    padding-left: 4rem !important; /* Increased from 1.5rem to prevent overlap with nav button */
    padding-right: 2rem !important;
  }

  .mobile-hero-title {
    font-size: clamp(1.75rem, 5vw, 2.25rem) !important;
    line-height: 1.1 !important;
    margin-bottom: 0.75rem !important;
  }

  .mobile-hero-description {
    font-size: clamp(0.8rem, 3vw, 0.9rem) !important;
    line-height: 1.3rem !important;
    margin-bottom: 0.75rem !important;
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  /* Laptop/Standard Desktop */
  .content-section {
    padding: 1.5rem 0;
  }

  /* Laptop-specific optimizations */
  .admin-auth-card {
    min-width: 220px;
    /* Ensure proper positioning on laptop screens */
    margin-top: 0;
    margin-bottom: 1rem;
  }

  /* Admin panel desktop layout improvements */
  .admin-panel-container {
    padding-top: 1.5rem;
  }

  .content-page-hero {
    padding: 2rem 0;
  }

  /* Card grid optimizations for laptop screens */
  .card-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
  }

  /* Hero carousel content sizing */
  .hero-carousel-content {
    max-width: 65%;
  }

  /* Ensure proper container width */
  .container {
    max-width: 1200px;
  }
}

@media (min-width: 1280px) {
  /* Large desktop */
  .content-section {
    padding: 2rem 0;
  }

  /* Large desktop optimizations */
  .admin-auth-card {
    min-width: 250px;
    /* Enhanced spacing for large screens */
    margin-top: 0;
    margin-bottom: 1.5rem;
  }

  /* Admin panel large desktop layout */
  .admin-panel-container {
    padding-top: 2rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .content-page-hero {
    padding: 3rem 0;
  }

  /* Ensure proper spacing on large screens */
  .container {
    max-width: 1400px;
  }

  /* Hero carousel optimizations for large screens */
  .hero-carousel-content {
    max-width: 60%;
  }
}

/* Additional mobile improvements */
@media (max-width: 640px) {
  /* Ensure all interactive elements are touch-friendly */
  button, a, input, select, textarea {
    min-height: 44px;
  }

  /* Improve card spacing on mobile */
  .card-grid-item {
    margin-bottom: 0.75rem;
  }

  /* Better mobile navigation */
  .mobile-nav {
    padding: 0.75rem;
  }

  /* Responsive text scaling */
  .responsive-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .responsive-text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  /* Mobile-specific button improvements */
  .mobile-button {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 0.5rem;
  }

  /* Prevent text from being too small */
  .min-text-size {
    font-size: 14px !important;
  }

  /* Content page mobile improvements */
  .content-page-hero {
    padding: 1rem;
  }

  .mobile-hero-description {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  /* Video player mobile optimizations */
  .video-player-container {
    margin: 0.5rem 0;
  }

  /* Card content mobile spacing */
  .content-page-card .card-content {
    padding: 1rem !important;
  }

  /* Responsive badges and buttons */
  .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  /* Mobile-friendly action buttons */
  .action-button {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

/* Video player iframe enhancements */
.video-player-iframe {
  border: none;
  border-radius: inherit;
  background: #000;
  transition: opacity 0.3s ease-in-out;
}

/* Z-index management to prevent overlaps */
.header-nav {
  z-index: 50;
}

.admin-auth-card {
  z-index: 15;
}

.admin-panel-container {
  z-index: 5;
}

.content-page-poster {
  z-index: 5;
}

.mobile-menu {
  z-index: 40;
}

/* Ad Blocker Modal - Highest priority overlay */
.ad-blocker-modal-backdrop {
  z-index: 9998 !important;
}

.ad-blocker-modal-container {
  z-index: 9999 !important;
}

/* Ad Blocker Modal Global Positioning */
.ad-blocker-modal-global {
  /* Ensure modal always appears above all content */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;

  /* Prevent any parent container from affecting positioning */
  transform: none !important;
  margin: 0 !important;
  padding: 0 !important;

  /* Ensure proper stacking context */
  isolation: isolate;
}

.ad-blocker-modal-backdrop-global {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9998 !important;

  /* Prevent interference from parent containers */
  transform: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure modal content is properly positioned */
.ad-blocker-modal-content {
  position: relative;
  z-index: 1;
  max-height: 90vh;
  overflow-y: auto;
}

/* Mobile-specific modal adjustments */
@media (max-width: 768px) {
  .ad-blocker-modal-content {
    max-height: 85vh;
    margin: 1rem;
  }
}

/* Ensure admin panel elements don't overlap */
@media (min-width: 1024px) {
  .admin-auth-card {
    z-index: 20;
    position: relative;
  }
}

/* Prevent layout shifts during responsive transitions */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* Ensure no horizontal scrolling */
html, body {
  overflow-x: hidden;
  width: 100%;
}

/* Smooth transitions between breakpoints */
@media (prefers-reduced-motion: no-preference) {
  * {
    transition: font-size 0.2s ease, padding 0.2s ease, margin 0.2s ease;
  }
}

/* Critical User Flow Optimizations */
/* Navigation accessibility */
.main-nav-link:focus,
.mobile-menu a:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Admin panel accessibility */
.admin-auth-card:focus-within {
  ring: 2px solid hsl(var(--primary));
}

/* Video player accessibility */
.video-player-container:focus-within {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 4px;
}

/* Content viewing optimizations */
.content-page-hero:focus-within {
  outline: none; /* Remove default outline for hero sections */
}

/* Form accessibility improvements */
.mobile-form input:focus,
.mobile-form select:focus,
.mobile-form textarea:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Touch target improvements for critical actions */
.critical-action-button {
  min-height: 48px;
  min-width: 48px;
  padding: 0.75rem 1rem;
}

/* Ensure proper contrast for all interactive elements */
button:hover,
a:hover {
  background-color: hsl(var(--primary) / 0.1);
}

/* Loading state improvements */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Performance Optimizations */
/* Reduce paint and layout thrashing */
.card-grid-item {
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

.hero-carousel {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize animations for mobile */
@media (max-width: 768px) {
  .card-grid-item:hover {
    transform: scale(1.02); /* Reduced scale for mobile performance */
  }

  .content-page-hero {
    animation-duration: 0.4s; /* Faster animations on mobile */
  }
}

/* Mobile landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .hero-carousel-content {
    padding-left: 4.5rem !important; /* Extra padding for landscape mode */
    max-width: 75% !important; /* Slightly reduced width for landscape */
  }

  .mobile-hero-title {
    font-size: clamp(1.25rem, 3.5vw, 1.75rem) !important;
    margin-bottom: 0.5rem !important;
  }

  .mobile-hero-description {
    font-size: clamp(0.7rem, 2vw, 0.8rem) !important;
    margin-bottom: 0.5rem !important;
  }
}

/* Accessibility Improvements */
/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-grid-item {
    border-width: 2px;
  }

  button, a {
    border: 2px solid currentColor;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .card-grid-item {
    transition: none;
  }

  .hero-carousel {
    animation: none;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus management for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  padding: 8px;
  text-decoration: none;
  z-index: 100;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.video-player-iframe:not([src]) {
  opacity: 0.5;
}

/* Platform-specific styling adjustments */
.video-player-youtube {
  /* YouTube-specific optimizations */
}

.video-player-vimeo {
  /* Vimeo-specific optimizations */
}

.video-player-unknown {
  /* Generic player styling */
  border: 1px solid hsl(var(--border));
}

