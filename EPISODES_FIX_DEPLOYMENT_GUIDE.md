# 🚀 Episodes Fix Deployment Guide

## 📋 Issue Summary
The "All Web-Series" tab in the Admin Panel was failing to add seasons and episodes due to:
1. **Validation errors** - Backend validation was too strict for optional fields
2. **CSP header issues** - Google Fonts were being blocked
3. **Frontend data handling** - Empty strings were being sent instead of proper null values

## 🔧 Files Fixed

### Backend Files
1. **server/routes/episodes.js**
   - Fixed season validation for `posterUrl` (allow empty strings)
   - Fixed episode validation for `airDate` and `thumbnailUrl` (allow empty strings)
   - Added detailed error logging for debugging
   - Improved validation error responses

2. **server/index.js**
   - Updated CSP headers to properly allow Google Fonts
   - Added support for Kaspersky antivirus domains (ff.kis.v2.scr.kaspersky-labs.com)

### Frontend Files
3. **src/components/admin/EpisodeManager.tsx**
   - Fixed data sanitization for season creation
   - Fixed data sanitization for episode creation
   - Ensured empty strings are properly handled

## 🚀 Manual Deployment Steps

Since SSH authentication is challenging, here are the manual steps to deploy the fixes:

### Step 1: Connect to Production Server
```bash
ssh root@45.93.8.197
# Enter password: Streamdb@2024
```

### Step 2: Navigate to Project Directory
```bash
cd /var/www/streamdb_root/data/www/streamdb.online
```

### Step 3: Backup Current Files
```bash
# Create backup directory
mkdir -p /var/backups/streamdb-episodes-fix-$(date +%Y%m%d)

# Backup current files
cp server/routes/episodes.js /var/backups/streamdb-episodes-fix-$(date +%Y%m%d)/
cp server/index.js /var/backups/streamdb-episodes-fix-$(date +%Y%m%d)/
cp -r src/components/admin/ /var/backups/streamdb-episodes-fix-$(date +%Y%m%d)/
```

### Step 4: Update Backend Files

#### Update episodes.js
```bash
nano server/routes/episodes.js
```

**Key changes to make:**
1. Replace the `posterUrl` validation (around line 16):
```javascript
body('posterUrl').optional().custom((value) => {
  if (!value || value.trim() === '') return true; // Allow empty values
  try {
    new URL(value);
    return true;
  } catch {
    throw new Error('Poster URL must be a valid URL');
  }
}),
```

2. Replace the `thumbnailUrl` validation (around line 45):
```javascript
body('thumbnailUrl').optional().custom((value) => {
  if (!value || value.trim() === '') return true; // Allow empty values
  try {
    new URL(value);
    return true;
  } catch {
    throw new Error('Thumbnail URL must be a valid URL');
  }
}),
```

3. Add detailed logging in season creation (around line 101):
```javascript
console.log('Season creation request received:', {
  contentId: req.params.contentId,
  body: req.body,
  user: req.user?.username
});
```

#### Update server/index.js
```bash
nano server/index.js
```

**Key change to make (around line 40):**
```javascript
"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://ff.kis.v2.scr.kaspersky-labs.com https://gc.kis.v2.scr.kaspersky-labs.com; " +
"style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://ff.kis.v2.scr.kaspersky-labs.com https://gc.kis.v2.scr.kaspersky-labs.com; " +
```

### Step 5: Update Frontend Files

#### Update EpisodeManager.tsx
```bash
nano src/components/admin/EpisodeManager.tsx
```

**Key changes to make:**

1. Season data sanitization (around line 236):
```javascript
const seasonData = {
  seasonNumber: seasonForm.seasonNumber,
  title: seasonForm.title?.trim() || `Season ${seasonForm.seasonNumber}`,
  description: seasonForm.description?.trim() || "",
  posterUrl: seasonForm.posterUrl?.trim() || ""
};
```

2. Episode data sanitization (around line 171):
```javascript
const episodeData = {
  episodeNumber: episodeForm.episode,
  title: episodeForm.title?.trim(),
  description: episodeForm.description?.trim() || "",
  secureVideoLinks: episodeForm.secureVideoLinks?.trim() || "",
  runtime: episodeForm.runtime?.trim() || "",
  airDate: episodeForm.airDate?.trim() || "",
  thumbnailUrl: episodeForm.thumbnailUrl?.trim() || ""
};
```

### Step 6: Rebuild Frontend
```bash
# Install dependencies if needed
npm install

# Build the frontend
npm run build
```

### Step 7: Restart Services
```bash
# Restart PM2 process
pm2 restart index

# If PM2 restart fails, try:
pm2 delete index
pm2 start server/index.js --name index

# Check PM2 status
pm2 list

# Reload Nginx
systemctl reload nginx
```

### Step 8: Test the Fix
```bash
# Test server response
curl -s -o /dev/null -w '%{http_code}' http://localhost:3001/api/health

# Check if episodes endpoint is working
curl -I http://localhost:3001/api/episodes
```

## 🧪 Testing Instructions

After deployment, test the following:

1. **Access Admin Panel**
   - Go to https://streamdb.online/admin
   - Login with your admin credentials

2. **Test All Web-Series Tab**
   - Click on "All Web-Series" tab
   - Select an existing web series
   - Click "Manage Episodes"

3. **Test Season Creation**
   - Click "Add Season" button
   - Enter season number (e.g., 1)
   - Leave title, description, and poster URL empty or fill them
   - Click "Add Season"
   - Should succeed without errors

4. **Test Episode Creation**
   - Select the newly created season
   - Click "Add Episode" button
   - Fill in episode title and number
   - Leave optional fields empty or fill them
   - Click "Add Episode"
   - Should succeed without errors

5. **Check Browser Console**
   - Open browser developer tools (F12)
   - Check console for any remaining errors
   - Google Fonts should load without CSP errors

## 🔍 Troubleshooting

### If Season Creation Still Fails:
1. Check server logs: `pm2 logs index`
2. Look for validation errors in the logs
3. Verify the request payload in browser network tab

### If CSP Errors Persist:
1. Check if Nginx is overriding CSP headers
2. Verify the CSP header in browser network tab
3. Clear browser cache and try again

### If PM2 Won't Start:
1. Check for syntax errors: `node server/index.js`
2. Check PM2 logs: `pm2 logs`
3. Restart PM2 daemon: `pm2 kill && pm2 resurrect`

## 📝 Rollback Instructions

If something goes wrong, restore from backup:
```bash
# Stop services
pm2 stop index

# Restore files
cp /var/backups/streamdb-episodes-fix-*/episodes.js server/routes/
cp /var/backups/streamdb-episodes-fix-*/index.js server/
cp -r /var/backups/streamdb-episodes-fix-*/admin/ src/components/

# Rebuild and restart
npm run build
pm2 start server/index.js --name index
```

## ✅ Success Indicators

- ✅ No validation errors when creating seasons
- ✅ No validation errors when creating episodes  
- ✅ Google Fonts load without CSP errors
- ✅ Browser console shows no critical errors
- ✅ PM2 process runs without crashes
- ✅ Admin panel "All Web-Series" tab works correctly
