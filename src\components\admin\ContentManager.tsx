
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Search, Edit, Trash2, Eye, Filter, MoreHorizontal, Download, Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Mock data for demonstration
const initialMockContent = [
  {
    id: 1,
    title: "Avengers: Endgame",
    type: "movie",
    year: 2019,
    genre: ["Action", "Sci-Fi"],
    status: "Published",
    featured: true,
    carousel: false,
    imdbRating: "8.4",
    runtime: "181",
    studio: "Marvel Studios",
  },
  {
    id: 2,
    title: "Breaking Bad",
    type: "series",
    year: 2008,
    genre: ["Drama", "Crime"],
    status: "Published",
    featured: false,
    carousel: true,
    imdbRating: "9.5",
    runtime: "47",
    studio: "Sony Pictures",
  },
  {
    id: 3,
    title: "The Dark Knight",
    type: "movie",
    year: 2008,
    genre: ["Action", "Crime"],
    status: "Draft",
    featured: false,
    carousel: false,
    imdbRating: "9.0",
    runtime: "152",
    studio: "Warner Bros",
  },
  {
    id: 4,
    title: "Stranger Things",
    type: "series",
    year: 2016,
    genre: ["Sci-Fi", "Horror", "Drama"],
    status: "Published",
    featured: true,
    carousel: false,
    imdbRating: "8.7",
    runtime: "50",
    studio: "Netflix",
  },
  {
    id: 5,
    title: "Inception",
    type: "movie",
    year: 2010,
    genre: ["Sci-Fi", "Thriller"],
    status: "Published",
    featured: false,
    carousel: true,
    imdbRating: "8.8",
    runtime: "148",
    studio: "Warner Bros",
  },
];

interface ContentItem {
  id: number;
  title: string;
  type: string;
  year: number;
  genre: string[];
  status: string;
  featured: boolean;
  carousel: boolean;
  imdbRating: string;
  runtime: string;
  studio: string;
}

export default function ContentManager() {
  const { toast } = useToast();
  const [content, setContent] = useState<ContentItem[]>(initialMockContent);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("All");
  const [filterStatus, setFilterStatus] = useState("All");
  const [selectedItem, setSelectedItem] = useState<ContentItem | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [sortBy, setSortBy] = useState<keyof ContentItem>("title");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  const filteredContent = content.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.studio.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.genre.some(g => g.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesType = filterType === "All" || item.type === filterType;
    const matchesStatus = filterStatus === "All" || item.status === filterStatus;
    return matchesSearch && matchesType && matchesStatus;
  }).sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
    return sortOrder === "asc" ? comparison : -comparison;
  });

  const handleView = (item: ContentItem) => {
    setSelectedItem(item);
    setIsViewDialogOpen(true);
  };

  const handleEdit = (item: ContentItem) => {
    setSelectedItem(item);
    setIsEditDialogOpen(true);
  };

  const handleDelete = (id: number) => {
    const item = content.find(c => c.id === id);
    if (window.confirm(`Are you sure you want to delete "${item?.title}"? This action cannot be undone.`)) {
      setContent(prev => prev.filter(c => c.id !== id));
      toast({
        title: "Content deleted",
        description: `"${item?.title}" has been deleted successfully`,
      });
    }
  };

  const handleBulkDelete = () => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select items to delete",
        variant: "destructive",
      });
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected items? This action cannot be undone.`)) {
      setContent(prev => prev.filter(c => !selectedItems.includes(c.id)));
      setSelectedItems([]);
      toast({
        title: "Bulk delete completed",
        description: `${selectedItems.length} items have been deleted successfully`,
      });
    }
  };

  const toggleItemSelection = (id: number) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const toggleSelectAll = () => {
    if (selectedItems.length === filteredContent.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredContent.map(item => item.id));
    }
  };

  const handleStatusChange = (id: number, newStatus: string) => {
    setContent(prev => prev.map(item => 
      item.id === id ? { ...item, status: newStatus } : item
    ));
    toast({
      title: "Status updated",
      description: `Content status changed to ${newStatus}`,
    });
  };

  const handleToggleFeature = (id: number, field: 'featured' | 'carousel') => {
    setContent(prev => prev.map(item => 
      item.id === id ? { ...item, [field]: !item[field] } : item
    ));
    const item = content.find(c => c.id === id);
    toast({
      title: "Feature updated",
      description: `${field} ${item?.[field] ? 'disabled' : 'enabled'} for "${item?.title}"`,
    });
  };

  // Helper function to escape CSV values and handle special characters
  const escapeCsvValue = (value: any): string => {
    if (value === null || value === undefined) return '';
    const stringValue = String(value);
    // Escape quotes by doubling them and wrap in quotes if contains comma, quote, or newline
    if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('\r')) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    return stringValue;
  };

  const exportData = (format: 'csv' | 'json') => {
    const dataToExport = selectedItems.length > 0
      ? content.filter(item => selectedItems.includes(item.id))
      : filteredContent;

    if (format === 'csv') {
      // Comprehensive CSV headers including all content fields
      const csvHeaders = [
        'Title', 'Description', 'Type', 'Year', 'Genres', 'Status',
        'Featured', 'Carousel', 'IMDb Rating', 'Runtime', 'Studio'
      ];

      const csvData = dataToExport.map(item => {
        return [
          escapeCsvValue(item.title),
          escapeCsvValue(''), // Description not available in this component's data structure
          escapeCsvValue(item.type),
          escapeCsvValue(item.year),
          escapeCsvValue(item.genre?.join('; ') || ''),
          escapeCsvValue(item.status),
          escapeCsvValue(item.featured ? 'Yes' : 'No'),
          escapeCsvValue(item.carousel ? 'Yes' : 'No'),
          escapeCsvValue(item.imdbRating || ''),
          escapeCsvValue(item.runtime || ''),
          escapeCsvValue(item.studio || '')
        ].join(',');
      });

      const csvContent = [csvHeaders.join(','), ...csvData].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'content-export.csv';
      a.click();
      window.URL.revokeObjectURL(url);
    } else {
      const jsonContent = JSON.stringify(dataToExport, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'content-export.json';
      a.click();
      window.URL.revokeObjectURL(url);
    }

    toast({
      title: "Export completed",
      description: `Data exported as ${format.toUpperCase()} successfully`,
    });
  };

  const saveItemChanges = () => {
    if (selectedItem) {
      setContent(prev => prev.map(item => 
        item.id === selectedItem.id ? selectedItem : item
      ));
      setIsEditDialogOpen(false);
      toast({
        title: "Changes saved",
        description: `"${selectedItem.title}" has been updated successfully`,
      });
    }
  };

  const handleSort = (field: keyof ContentItem) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
  };

  return (
    <div className="w-full bg-card/90 rounded-2xl shadow-xl border border-border px-6 py-8 space-y-6">
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <h2 className="text-2xl font-semibold text-primary">Manage Content</h2>
        
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search titles, studios, genres..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full sm:w-64"
            />
          </div>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="bg-background border border-border rounded-md px-3 py-2 h-10"
          >
            <option value="All">All Types</option>
            <option value="movie">Movies</option>
            <option value="series">Web Series</option>
          </select>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="bg-background border border-border rounded-md px-3 py-2 h-10"
          >
            <option value="All">All Status</option>
            <option value="Published">Published</option>
            <option value="Draft">Draft</option>
          </select>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="flex items-center gap-3 p-3 bg-accent rounded-lg">
          <span className="text-sm font-medium">{selectedItems.length} items selected</span>
          <Button variant="destructive" size="sm" onClick={handleBulkDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Selected
          </Button>
          <Button variant="outline" size="sm" onClick={() => setSelectedItems([])}>
            Clear Selection
          </Button>
        </div>
      )}

      {/* Export Options */}
      <div className="flex gap-2 flex-wrap">
        <Button variant="outline" size="sm" onClick={() => exportData('csv')}>
          <Download className="h-4 w-4 mr-2" />
          Export CSV
        </Button>
        <Button variant="outline" size="sm" onClick={() => exportData('json')}>
          <Download className="h-4 w-4 mr-2" />
          Export JSON
        </Button>
        <Button variant="outline" size="sm" onClick={() => navigator.clipboard.writeText(JSON.stringify(filteredContent))}>
          <Copy className="h-4 w-4 mr-2" />
          Copy Data
        </Button>
      </div>

      {/* Content Table */}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input
                  type="checkbox"
                  checked={selectedItems.length === filteredContent.length && filteredContent.length > 0}
                  onChange={toggleSelectAll}
                  className="rounded"
                />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('title')}>
                Title {sortBy === 'title' && (sortOrder === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('type')}>
                Type {sortBy === 'type' && (sortOrder === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('year')}>
                Year {sortBy === 'year' && (sortOrder === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead>Genre</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Featured</TableHead>
              <TableHead>Carousel</TableHead>
              <TableHead>Rating</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredContent.map((item) => (
              <TableRow key={item.id}>
                <TableCell>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={() => toggleItemSelection(item.id)}
                    className="rounded"
                  />
                </TableCell>
                <TableCell className="font-medium">{item.title}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {item.type === 'movie' ? 'Movie' : item.type === 'series' ? 'Web Series' : item.type}
                  </Badge>
                </TableCell>
                <TableCell>{item.year}</TableCell>
                <TableCell>
                  <div className="flex gap-1 flex-wrap">
                    {item.genre.slice(0, 2).map((g) => (
                      <Badge key={g} variant="secondary" className="text-xs">
                        {g}
                      </Badge>
                    ))}
                    {item.genre.length > 2 && (
                      <Badge variant="secondary" className="text-xs">
                        +{item.genre.length - 2}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <select
                    value={item.status}
                    onChange={(e) => handleStatusChange(item.id, e.target.value)}
                    className="bg-transparent border-0 text-sm"
                  >
                    <option value="Published">Published</option>
                    <option value="Draft">Draft</option>
                    <option value="Archived">Archived</option>
                  </select>
                </TableCell>
                <TableCell>
                  <button
                    onClick={() => handleToggleFeature(item.id, 'featured')}
                    className="text-sm"
                  >
                    {item.featured ? (
                      <Badge className="bg-yellow-500/20 text-yellow-700 dark:text-yellow-300">Yes</Badge>
                    ) : (
                      <span className="text-muted-foreground cursor-pointer hover:text-foreground">No</span>
                    )}
                  </button>
                </TableCell>
                <TableCell>
                  <button
                    onClick={() => handleToggleFeature(item.id, 'carousel')}
                    className="text-sm"
                  >
                    {item.carousel ? (
                      <Badge className="bg-blue-500/20 text-blue-700 dark:text-blue-300">Yes</Badge>
                    ) : (
                      <span className="text-muted-foreground cursor-pointer hover:text-foreground">No</span>
                    )}
                  </button>
                </TableCell>
                <TableCell>
                  <span className="text-sm">{item.imdbRating}</span>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2 justify-end">
                    <Button variant="ghost" size="sm" onClick={() => handleView(item)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleEdit(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-destructive hover:text-destructive"
                      onClick={() => handleDelete(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {filteredContent.length === 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No content found matching your criteria.</p>
          <Button variant="outline" className="mt-4" onClick={() => {
            setSearchTerm("");
            setFilterType("All");
            setFilterStatus("All");
          }}>
            Clear Filters
          </Button>
        </div>
      )}

      {/* Summary Stats */}
      <div className="flex flex-wrap gap-4 pt-4 border-t border-border">
        <div className="text-sm text-muted-foreground">
          Total: <span className="font-medium text-foreground">{content.length}</span>
        </div>
        <div className="text-sm text-muted-foreground">
          Filtered: <span className="font-medium text-foreground">{filteredContent.length}</span>
        </div>
        <div className="text-sm text-muted-foreground">
          Published: <span className="font-medium text-foreground">{content.filter(i => i.status === "Published").length}</span>
        </div>
        <div className="text-sm text-muted-foreground">
          Featured: <span className="font-medium text-foreground">{content.filter(i => i.featured).length}</span>
        </div>
        <div className="text-sm text-muted-foreground">
          In Carousel: <span className="font-medium text-foreground">{content.filter(i => i.carousel).length}</span>
        </div>
      </div>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Content Details</DialogTitle>
          </DialogHeader>
          {selectedItem && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">Title:</Label>
                  <p>{selectedItem.title}</p>
                </div>
                <div>
                  <Label className="font-medium">Type:</Label>
                  <p>{selectedItem.type}</p>
                </div>
                <div>
                  <Label className="font-medium">Year:</Label>
                  <p>{selectedItem.year}</p>
                </div>
                <div>
                  <Label className="font-medium">Studio:</Label>
                  <p>{selectedItem.studio}</p>
                </div>
                <div>
                  <Label className="font-medium">Runtime:</Label>
                  <p>{selectedItem.runtime} minutes</p>
                </div>
                <div>
                  <Label className="font-medium">IMDb Rating:</Label>
                  <p>{selectedItem.imdbRating}</p>
                </div>
              </div>
              <div>
                <Label className="font-medium">Genres:</Label>
                <div className="flex gap-2 mt-1">
                  {selectedItem.genre.map(g => (
                    <Badge key={g} variant="secondary">{g}</Badge>
                  ))}
                </div>
              </div>
              <div className="flex gap-4">
                <div>
                  <Label className="font-medium">Status:</Label>
                  <Badge variant={selectedItem.status === "Published" ? "default" : "secondary"}>
                    {selectedItem.status}
                  </Badge>
                </div>
                <div>
                  <Label className="font-medium">Featured:</Label>
                  <Badge variant={selectedItem.featured ? "default" : "secondary"}>
                    {selectedItem.featured ? "Yes" : "No"}
                  </Badge>
                </div>
                <div>
                  <Label className="font-medium">In Carousel:</Label>
                  <Badge variant={selectedItem.carousel ? "default" : "secondary"}>
                    {selectedItem.carousel ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Content</DialogTitle>
          </DialogHeader>
          {selectedItem && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Title</Label>
                  <Input
                    value={selectedItem.title}
                    onChange={(e) => setSelectedItem({...selectedItem, title: e.target.value})}
                  />
                </div>
                <div>
                  <Label>Type</Label>
                  <select
                    value={selectedItem.type}
                    onChange={(e) => setSelectedItem({...selectedItem, type: e.target.value})}
                    className="w-full bg-background border border-border rounded-md px-3 py-2 h-10"
                  >
                    <option value="movie">Movie</option>
                    <option value="series">Web Series</option>
                  </select>
                </div>
                <div>
                  <Label>Year</Label>
                  <Input
                    type="number"
                    value={selectedItem.year}
                    onChange={(e) => setSelectedItem({...selectedItem, year: parseInt(e.target.value)})}
                  />
                </div>
                <div>
                  <Label>Studio</Label>
                  <Input
                    value={selectedItem.studio}
                    onChange={(e) => setSelectedItem({...selectedItem, studio: e.target.value})}
                  />
                </div>
                <div>
                  <Label>Runtime (minutes)</Label>
                  <Input
                    value={selectedItem.runtime}
                    onChange={(e) => setSelectedItem({...selectedItem, runtime: e.target.value})}
                  />
                </div>
                <div>
                  <Label>IMDb Rating</Label>
                  <Input
                    value={selectedItem.imdbRating}
                    onChange={(e) => setSelectedItem({...selectedItem, imdbRating: e.target.value})}
                  />
                </div>
              </div>
              <div>
                <Label>Status</Label>
                <select
                  value={selectedItem.status}
                  onChange={(e) => setSelectedItem({...selectedItem, status: e.target.value})}
                  className="w-full bg-background border border-border rounded-md px-3 py-2 h-10"
                >
                  <option value="Published">Published</option>
                  <option value="Draft">Draft</option>
                  <option value="Archived">Archived</option>
                </select>
              </div>
              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedItem.featured}
                    onChange={(e) => setSelectedItem({...selectedItem, featured: e.target.checked})}
                  />
                  Featured
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedItem.carousel}
                    onChange={(e) => setSelectedItem({...selectedItem, carousel: e.target.checked})}
                  />
                  In Carousel
                </label>
              </div>
              <div className="flex gap-2 pt-4">
                <Button onClick={saveItemChanges}>Save Changes</Button>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
