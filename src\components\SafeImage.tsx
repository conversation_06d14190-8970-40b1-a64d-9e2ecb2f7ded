import React, { useState, useRef, useEffect } from 'react';

interface SafeImageProps {
  src?: string;
  alt?: string;
  fallbackSrc?: string;
  placeholder?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
  [key: string]: any;
}

const SafeImage: React.FC<SafeImageProps> = ({
  src,
  alt = 'Image',
  fallbackSrc = '/placeholder-image.jpg',
  placeholder,
  className = '',
  style,
  onLoad,
  onError,
  ...props
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    
    // Set the image source, with fallbacks
    const imageToLoad = src || fallbackSrc;
    
    if (!imageToLoad) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    // Create a new image to test loading
    const img = new Image();
    
    img.onload = () => {
      setImageSrc(imageToLoad);
      setIsLoading(false);
      setHasError(false);
      onLoad?.();
    };
    
    img.onerror = () => {
      // Try fallback if original src failed
      if (imageToLoad !== fallbackSrc && fallbackSrc) {
        const fallbackImg = new Image();
        
        fallbackImg.onload = () => {
          setImageSrc(fallbackSrc);
          setIsLoading(false);
          setHasError(false);
        };
        
        fallbackImg.onerror = () => {
          setHasError(true);
          setIsLoading(false);
          onError?.();
        };
        
        fallbackImg.src = fallbackSrc;
      } else {
        setHasError(true);
        setIsLoading(false);
        onError?.();
      }
    };
    
    img.src = imageToLoad;
  }, [src, fallbackSrc, onLoad, onError]);

  // Show loading placeholder
  if (isLoading) {
    return (
      <div 
        className={`flex items-center justify-center bg-muted animate-pulse ${className}`}
        style={style}
        {...props}
      >
        {placeholder || (
          <div className="text-center text-muted-foreground">
            <div className="text-2xl mb-2">🖼️</div>
            <div className="text-sm">Loading...</div>
          </div>
        )}
      </div>
    );
  }

  // Show error placeholder
  if (hasError || !imageSrc) {
    return (
      <div 
        className={`flex items-center justify-center bg-muted ${className}`}
        style={style}
        {...props}
      >
        {placeholder || (
          <div className="text-center text-muted-foreground">
            <div className="text-2xl mb-2">🚫</div>
            <div className="text-sm">Image unavailable</div>
          </div>
        )}
      </div>
    );
  }

  // Render the actual image
  return (
    <img
      ref={imgRef}
      src={imageSrc}
      alt={alt}
      className={className}
      style={style}
      onLoad={onLoad}
      onError={() => {
        setHasError(true);
        onError?.();
      }}
      {...props}
    />
  );
};

export default SafeImage;
