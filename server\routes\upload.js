const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken, requireModerator } = require('../middleware/auth');

const router = express.Router();

// Create upload directories if they don't exist
const createUploadDirs = async () => {
  const dirs = ['uploads', 'uploads/images', 'uploads/videos', 'uploads/subtitles', 'uploads/temp'];
  for (const dir of dirs) {
    try {
      await fs.mkdir(dir, { recursive: true });
    } catch (error) {
      console.error(`Error creating directory ${dir}:`, error);
    }
  }
};

createUploadDirs();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads/temp';
    
    if (file.fieldname === 'image' || file.fieldname === 'poster' || file.fieldname === 'thumbnail') {
      uploadPath = 'uploads/images';
    } else if (file.fieldname === 'video') {
      uploadPath = 'uploads/videos';
    } else if (file.fieldname === 'subtitle') {
      uploadPath = 'uploads/subtitles';
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  const allowedImageTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg'];
  const allowedSubtitleTypes = ['text/plain', 'application/x-subrip'];
  
  if (file.fieldname === 'image' || file.fieldname === 'poster' || file.fieldname === 'thumbnail') {
    if (allowedImageTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid image file type. Only JPEG, PNG, WebP, and GIF are allowed.'), false);
    }
  } else if (file.fieldname === 'video') {
    if (allowedVideoTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid video file type. Only MP4, WebM, and OGG are allowed.'), false);
    }
  } else if (file.fieldname === 'subtitle') {
    if (allowedSubtitleTypes.includes(file.mimetype) || file.originalname.endsWith('.srt') || file.originalname.endsWith('.vtt')) {
      cb(null, true);
    } else {
      cb(new Error('Invalid subtitle file type. Only SRT and VTT files are allowed.'), false);
    }
  } else {
    cb(new Error('Invalid field name'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
    files: 5 // Maximum 5 files per request
  }
});

// Upload single image
router.post('/image', authenticateToken, requireModerator, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'No image file provided'
      });
    }

    const { optimize = 'true', width, height } = req.query;
    let filePath = req.file.path;
    let fileName = req.file.filename;

    // Optimize image if requested
    if (optimize === 'true') {
      const optimizedFileName = `optimized_${fileName}`;
      const optimizedPath = path.join('uploads/images', optimizedFileName);
      
      let sharpInstance = sharp(filePath);
      
      // Resize if dimensions provided
      if (width || height) {
        sharpInstance = sharpInstance.resize(
          width ? parseInt(width) : null,
          height ? parseInt(height) : null,
          { fit: 'inside', withoutEnlargement: true }
        );
      }
      
      // Optimize and save
      await sharpInstance
        .jpeg({ quality: 85, progressive: true })
        .png({ quality: 85, compressionLevel: 8 })
        .webp({ quality: 85 })
        .toFile(optimizedPath);
      
      // Remove original file
      await fs.unlink(filePath);
      
      filePath = optimizedPath;
      fileName = optimizedFileName;
    }

    const fileUrl = `/uploads/images/${fileName}`;

    res.json({
      success: true,
      message: 'Image uploaded successfully',
      data: {
        filename: fileName,
        originalName: req.file.originalname,
        url: fileUrl,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });

  } catch (error) {
    console.error('Error uploading image:', error);
    
    // Clean up file if it exists
    if (req.file && req.file.path) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Error cleaning up file:', unlinkError);
      }
    }
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to upload image'
    });
  }
});

// Upload multiple images
router.post('/images', authenticateToken, requireModerator, upload.array('images', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'No image files provided'
      });
    }

    const uploadedFiles = [];
    const { optimize = 'true' } = req.query;

    for (const file of req.files) {
      let filePath = file.path;
      let fileName = file.filename;

      // Optimize image if requested
      if (optimize === 'true') {
        const optimizedFileName = `optimized_${fileName}`;
        const optimizedPath = path.join('uploads/images', optimizedFileName);
        
        await sharp(filePath)
          .resize(1920, 1080, { fit: 'inside', withoutEnlargement: true })
          .jpeg({ quality: 85, progressive: true })
          .png({ quality: 85, compressionLevel: 8 })
          .webp({ quality: 85 })
          .toFile(optimizedPath);
        
        // Remove original file
        await fs.unlink(filePath);
        
        filePath = optimizedPath;
        fileName = optimizedFileName;
      }

      uploadedFiles.push({
        filename: fileName,
        originalName: file.originalname,
        url: `/uploads/images/${fileName}`,
        size: file.size,
        mimetype: file.mimetype
      });
    }

    res.json({
      success: true,
      message: 'Images uploaded successfully',
      data: uploadedFiles
    });

  } catch (error) {
    console.error('Error uploading images:', error);
    
    // Clean up files if they exist
    if (req.files) {
      for (const file of req.files) {
        try {
          await fs.unlink(file.path);
        } catch (unlinkError) {
          console.error('Error cleaning up file:', unlinkError);
        }
      }
    }
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to upload images'
    });
  }
});

// Upload subtitle file
router.post('/subtitle', authenticateToken, requireModerator, upload.single('subtitle'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'No subtitle file provided'
      });
    }

    const fileUrl = `/uploads/subtitles/${req.file.filename}`;

    res.json({
      success: true,
      message: 'Subtitle uploaded successfully',
      data: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        url: fileUrl,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });

  } catch (error) {
    console.error('Error uploading subtitle:', error);
    
    // Clean up file if it exists
    if (req.file && req.file.path) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Error cleaning up file:', unlinkError);
      }
    }
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to upload subtitle'
    });
  }
});

// Delete uploaded file
router.delete('/:type/:filename', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { type, filename } = req.params;
    
    if (!['images', 'videos', 'subtitles'].includes(type)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid file type'
      });
    }

    const filePath = path.join('uploads', type, filename);
    
    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
      
      res.json({
        success: true,
        message: 'File deleted successfully',
        data: {
          filename,
          type
        }
      });
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({
          error: 'Not Found',
          message: 'File not found'
        });
      }
      throw error;
    }

  } catch (error) {
    console.error('Error deleting file:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete file'
    });
  }
});

// Get file info
router.get('/:type/:filename/info', async (req, res) => {
  try {
    const { type, filename } = req.params;
    
    if (!['images', 'videos', 'subtitles'].includes(type)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid file type'
      });
    }

    const filePath = path.join('uploads', type, filename);
    
    try {
      const stats = await fs.stat(filePath);
      
      res.json({
        success: true,
        data: {
          filename,
          type,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          url: `/uploads/${type}/${filename}`
        }
      });
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({
          error: 'Not Found',
          message: 'File not found'
        });
      }
      throw error;
    }

  } catch (error) {
    console.error('Error getting file info:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get file info'
    });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(413).json({
        error: 'File Too Large',
        message: 'File size exceeds the maximum allowed limit'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(413).json({
        error: 'Too Many Files',
        message: 'Too many files uploaded'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: 'Unexpected File',
        message: 'Unexpected file field'
      });
    }
  }
  
  if (error.message.includes('Invalid')) {
    return res.status(400).json({
      error: 'Invalid File',
      message: error.message
    });
  }
  
  next(error);
});

module.exports = router;
