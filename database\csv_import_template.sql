-- CSV Import Template for Streaming Database
-- This template shows how to import your CSV data into the database

-- IMPORTANT: Before running this script:
-- 1. Upload your CSV file to the server
-- 2. Ensure CSV columns match the expected format
-- 3. Update file paths and column mappings as needed

-- Expected CSV format (adjust column names as needed):
-- id,title,description,year,type,category,genres,languages,image,cover_image,tmdb_id,imdb_rating,runtime,studio,tags,video_links,quality,audio_tracks,is_published,is_featured,add_to_carousel

-- Step 1: Create temporary table for CSV import
CREATE TEMPORARY TABLE temp_csv_import (
    id VARCHAR(50),
    title VARCHAR(255),
    description TEXT,
    year INT,
    type VARCHAR(20),
    category VARCHAR(100),
    genres TEXT, -- Comma-separated genres
    languages TEXT, -- Comma-separated languages
    image VARCHAR(500),
    cover_image VARCHAR(500),
    tmdb_id VARCHAR(20),
    imdb_rating DECIMAL(3,1),
    runtime VARCHAR(20),
    studio VARCHAR(255),
    tags TEXT,
    video_links TEXT,
    secure_video_links TEXT,
    quality TEXT, -- Comma-separated quality options
    audio_tracks TEXT, -- Comma-separated audio tracks
    is_published BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    add_to_carousel BOOLEAN DEFAULT FALSE,
    trailer VARCHAR(500),
    subtitle_url VARCHAR(500),
    poster_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    total_seasons INT DEFAULT 0,
    total_episodes INT DEFAULT 0
);

-- Step 2: Load CSV data into temporary table
-- IMPORTANT: Update the file path to match your CSV file location
-- For phpMyAdmin, you can use the Import feature instead of LOAD DATA INFILE

/*
LOAD DATA INFILE '/path/to/your/content.csv'
INTO TABLE temp_csv_import
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS; -- Skip header row
*/

-- Alternative: Manual INSERT statements for small datasets
-- Replace these with your actual CSV data:

/*
INSERT INTO temp_csv_import (
    id, title, description, year, type, category, genres, languages,
    image, cover_image, tmdb_id, imdb_rating, runtime, studio, tags,
    video_links, quality, audio_tracks, is_published, is_featured, add_to_carousel
) VALUES
('movie-1', 'Sample Movie 1', 'Description of movie 1', 2024, 'movie', 'English Movies', 'Action,Thriller', 'English', 
 'https://example.com/poster1.jpg', 'https://example.com/cover1.jpg', '12345', 8.5, '120 min', 'Studio A', 'action,thriller',
 'https://example.com/video1.mp4', '1080p,720p', 'English', TRUE, TRUE, FALSE),
('series-1', 'Sample Series 1', 'Description of series 1', 2023, 'series', 'English Web Series', 'Drama,Crime', 'English',
 'https://example.com/poster2.jpg', 'https://example.com/cover2.jpg', '67890', 9.0, '45 min', 'Studio B', 'drama,crime',
 'https://example.com/video2.mp4', '4K,1080p', 'English', TRUE, FALSE, TRUE);
*/

-- Step 3: Import data from temporary table to main tables

-- Function to get or create category ID
DELIMITER //
CREATE FUNCTION GetOrCreateCategory(cat_name VARCHAR(100), content_type VARCHAR(20)) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE cat_id INT DEFAULT NULL;
    
    -- Try to find existing category
    SELECT id INTO cat_id FROM categories WHERE name = cat_name LIMIT 1;
    
    -- If not found, create new category
    IF cat_id IS NULL THEN
        INSERT INTO categories (name, type, slug) VALUES 
        (cat_name, 
         CASE WHEN content_type = 'movie' THEN 'movie' 
              WHEN content_type = 'series' THEN 'series' 
              ELSE 'both' END,
         LOWER(REPLACE(REPLACE(cat_name, ' ', '-'), '&', 'and')));
        SET cat_id = LAST_INSERT_ID();
    END IF;
    
    RETURN cat_id;
END//
DELIMITER ;

-- Import main content data
INSERT INTO content (
    id, title, description, year, type, category_id, image, cover_image,
    tmdb_id, imdb_rating, runtime, studio, tags, video_links, secure_video_links,
    is_published, is_featured, add_to_carousel, poster_url, thumbnail_url,
    trailer, subtitle_url, total_seasons, total_episodes, created_at
)
SELECT 
    id,
    title,
    description,
    year,
    type,
    GetOrCreateCategory(category, type) as category_id,
    image,
    cover_image,
    tmdb_id,
    imdb_rating,
    runtime,
    studio,
    tags,
    video_links,
    secure_video_links,
    COALESCE(is_published, TRUE),
    COALESCE(is_featured, FALSE),
    COALESCE(add_to_carousel, FALSE),
    poster_url,
    thumbnail_url,
    trailer,
    subtitle_url,
    COALESCE(total_seasons, 0),
    COALESCE(total_episodes, 0),
    NOW()
FROM temp_csv_import;

-- Import genres (handle comma-separated values)
INSERT INTO content_genres (content_id, genre_id)
SELECT DISTINCT
    t.id,
    g.id
FROM temp_csv_import t
CROSS JOIN genres g
WHERE FIND_IN_SET(g.name, REPLACE(t.genres, ', ', ',')) > 0
AND t.genres IS NOT NULL
AND t.genres != '';

-- Import languages (handle comma-separated values)
INSERT INTO content_languages (content_id, language_id)
SELECT DISTINCT
    t.id,
    l.id
FROM temp_csv_import t
CROSS JOIN languages l
WHERE FIND_IN_SET(l.name, REPLACE(t.languages, ', ', ',')) > 0
AND t.languages IS NOT NULL
AND t.languages != '';

-- Import quality options (handle comma-separated values)
INSERT INTO content_quality (content_id, quality_id)
SELECT DISTINCT
    t.id,
    q.id
FROM temp_csv_import t
CROSS JOIN quality_options q
WHERE FIND_IN_SET(q.name, REPLACE(t.quality, ', ', ',')) > 0
AND t.quality IS NOT NULL
AND t.quality != '';

-- Import audio tracks (handle comma-separated values)
INSERT INTO content_audio (content_id, audio_id)
SELECT DISTINCT
    t.id,
    a.id
FROM temp_csv_import t
CROSS JOIN audio_tracks a
WHERE FIND_IN_SET(a.name, REPLACE(t.audio_tracks, ', ', ',')) > 0
AND t.audio_tracks IS NOT NULL
AND t.audio_tracks != '';

-- Clean up
DROP FUNCTION IF EXISTS GetOrCreateCategory;
DROP TEMPORARY TABLE temp_csv_import;

-- Verify import
SELECT 
    'Content imported' as status,
    COUNT(*) as total_content,
    SUM(CASE WHEN type = 'movie' THEN 1 ELSE 0 END) as movies,
    SUM(CASE WHEN type = 'series' THEN 1 ELSE 0 END) as series
FROM content;

SELECT 
    'Relationships created' as status,
    (SELECT COUNT(*) FROM content_genres) as genre_links,
    (SELECT COUNT(*) FROM content_languages) as language_links,
    (SELECT COUNT(*) FROM content_quality) as quality_links,
    (SELECT COUNT(*) FROM content_audio) as audio_links;

-- Show sample of imported data
SELECT 
    c.id,
    c.title,
    c.year,
    c.type,
    cat.name as category,
    GROUP_CONCAT(DISTINCT g.name) as genres,
    GROUP_CONCAT(DISTINCT l.name) as languages
FROM content c
LEFT JOIN categories cat ON c.category_id = cat.id
LEFT JOIN content_genres cg ON c.id = cg.content_id
LEFT JOIN genres g ON cg.genre_id = g.id
LEFT JOIN content_languages cl ON c.id = cl.content_id
LEFT JOIN languages l ON cl.language_id = l.id
GROUP BY c.id, c.title, c.year, c.type, cat.name
LIMIT 10;
