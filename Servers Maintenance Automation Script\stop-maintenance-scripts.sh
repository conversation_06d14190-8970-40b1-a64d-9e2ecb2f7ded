#!/bin/bash

# ============================================================================
# StreamDB Maintenance Scripts Stop/Disable Script
# Purpose: Stop running maintenance and disable automated scheduling
# Usage: ./stop-maintenance-scripts.sh [--disable-permanently]
# ============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_SCRIPT="/usr/local/bin/backend-maintenance.sh"
PROXY_SCRIPT="/usr/local/bin/reverse-proxy-maintenance.sh"
VALIDATION_SCRIPT="/usr/local/bin/validation-scripts.sh"
CRON_CONFIG_SCRIPT="/usr/local/bin/cron-configuration.sh"

# Lock files
BACKEND_LOCK="/var/run/streamdb-backend-maintenance.lock"
PROXY_LOCK="/var/run/streamdb-proxy-maintenance.lock"

# Cron files
CRON_ENV_FILE="/etc/cron.d/streamdb-maintenance-env"

print_header() {
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

detect_server_type() {
    local hostname=$(hostname)
    local ip=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")
    
    if [[ "$hostname" == "backend1maindb" ]] || [[ "$ip" == "***********" ]]; then
        echo "backend"
    elif [[ "$hostname" == "backend2ndrevproxy" ]] || [[ "$ip" == "*************" ]]; then
        echo "proxy"
    else
        echo "unknown"
    fi
}

# ============================================================================
# STOP RUNNING MAINTENANCE
# ============================================================================

stop_running_maintenance() {
    print_section "Stopping Running Maintenance Scripts"
    
    local stopped_processes=0
    
    # Find and stop maintenance processes
    local maintenance_pids=$(pgrep -f "maintenance.sh" 2>/dev/null || true)
    
    if [[ -n "$maintenance_pids" ]]; then
        log_warn "Found running maintenance processes: $maintenance_pids"
        
        for pid in $maintenance_pids; do
            local process_info=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
            log_info "Stopping maintenance process: PID $pid ($process_info)"
            
            # Try graceful termination first
            if kill -TERM "$pid" 2>/dev/null; then
                log_info "Sent SIGTERM to process $pid"
                sleep 5
                
                # Check if process is still running
                if kill -0 "$pid" 2>/dev/null; then
                    log_warn "Process $pid still running, sending SIGKILL"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
                
                stopped_processes=$((stopped_processes + 1))
            else
                log_warn "Could not stop process $pid (may have already stopped)"
            fi
        done
    else
        log_info "No running maintenance processes found"
    fi
    
    # Remove lock files
    for lock_file in "$BACKEND_LOCK" "$PROXY_LOCK"; do
        if [[ -f "$lock_file" ]]; then
            local lock_pid=$(cat "$lock_file" 2>/dev/null || echo "unknown")
            log_info "Removing lock file: $lock_file (PID: $lock_pid)"
            rm -f "$lock_file"
        fi
    done
    
    if [[ $stopped_processes -gt 0 ]]; then
        log_success "Stopped $stopped_processes maintenance process(es)"
    else
        log_info "No maintenance processes were running"
    fi
}

# ============================================================================
# DISABLE CRON JOBS
# ============================================================================

disable_cron_jobs() {
    print_section "Disabling Cron Jobs"
    
    local disabled_jobs=0
    
    # Remove from root crontab
    local current_crontab=$(crontab -l 2>/dev/null || echo "")
    
    if [[ -n "$current_crontab" ]]; then
        # Remove maintenance-related cron jobs
        local new_crontab=$(echo "$current_crontab" | grep -v "maintenance.sh" || true)
        
        if [[ "$current_crontab" != "$new_crontab" ]]; then
            echo "$new_crontab" | crontab -
            log_info "Removed maintenance jobs from root crontab"
            disabled_jobs=$((disabled_jobs + 1))
        else
            log_info "No maintenance jobs found in root crontab"
        fi
    else
        log_info "No crontab found for root user"
    fi
    
    # Remove cron.d file
    if [[ -f "$CRON_ENV_FILE" ]]; then
        log_info "Removing cron environment file: $CRON_ENV_FILE"
        rm -f "$CRON_ENV_FILE"
        disabled_jobs=$((disabled_jobs + 1))
    else
        log_info "Cron environment file not found: $CRON_ENV_FILE"
    fi
    
    # Restart cron service to apply changes
    if systemctl restart cron; then
        log_info "Cron service restarted"
    else
        log_warn "Failed to restart cron service"
    fi
    
    if [[ $disabled_jobs -gt 0 ]]; then
        log_success "Disabled $disabled_jobs cron job configuration(s)"
    else
        log_info "No cron jobs were configured"
    fi
}

# ============================================================================
# BACKUP AND REMOVE SCRIPTS
# ============================================================================

backup_and_remove_scripts() {
    print_section "Backing Up and Removing Scripts"
    
    local backup_dir="/var/backups/streamdb-maintenance-disabled-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    local backed_up_files=0
    local removed_files=0
    
    # List of scripts to handle
    local scripts=("$BACKEND_SCRIPT" "$PROXY_SCRIPT" "$VALIDATION_SCRIPT" "$CRON_CONFIG_SCRIPT")
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            local script_name=$(basename "$script")
            
            # Create backup
            if cp "$script" "$backup_dir/$script_name"; then
                log_info "Backed up: $script → $backup_dir/$script_name"
                backed_up_files=$((backed_up_files + 1))
                
                # Remove original
                if rm -f "$script"; then
                    log_info "Removed: $script"
                    removed_files=$((removed_files + 1))
                else
                    log_error "Failed to remove: $script"
                fi
            else
                log_error "Failed to backup: $script"
            fi
        else
            log_info "Script not found: $script"
        fi
    done
    
    if [[ $backed_up_files -gt 0 ]]; then
        log_success "Backed up $backed_up_files script(s) to: $backup_dir"
        log_success "Removed $removed_files script(s) from system"
        
        # Create restoration instructions
        cat > "$backup_dir/RESTORE_INSTRUCTIONS.txt" << EOF
StreamDB Maintenance Scripts Backup
Created: $(date)
Server: $(hostname) ($(hostname -I | awk '{print $1}'))

To restore these scripts:
1. Copy scripts back to /usr/local/bin/
2. Set executable permissions: chmod +x /usr/local/bin/*.sh
3. Run cron configuration: /usr/local/bin/cron-configuration.sh

Scripts backed up:
$(ls -la "$backup_dir"/*.sh 2>/dev/null || echo "No scripts found")

Cron jobs that were disabled:
$(grep -r "maintenance" /var/spool/cron/ /etc/cron.d/ 2>/dev/null || echo "No cron jobs found")
EOF
        
        log_info "Created restoration instructions: $backup_dir/RESTORE_INSTRUCTIONS.txt"
    else
        log_info "No scripts were found to backup"
        rmdir "$backup_dir" 2>/dev/null || true
    fi
}

# ============================================================================
# TEMPORARY DISABLE (KEEP SCRIPTS)
# ============================================================================

temporary_disable() {
    print_section "Temporarily Disabling Maintenance (Scripts Preserved)"
    
    # Stop running maintenance
    stop_running_maintenance
    
    # Disable cron jobs but keep scripts
    disable_cron_jobs
    
    # Create disabled marker file
    local marker_file="/var/lib/streamdb-maintenance-disabled"
    echo "Maintenance disabled on $(date) by $(whoami)" > "$marker_file"
    echo "Server: $(hostname) ($(hostname -I | awk '{print $1}'))" >> "$marker_file"
    echo "Scripts preserved in /usr/local/bin/" >> "$marker_file"
    
    log_success "Maintenance temporarily disabled"
    log_info "Scripts preserved for future re-enabling"
    log_info "Marker file created: $marker_file"
    
    print_section "Re-enabling Instructions"
    echo -e "${BLUE}To re-enable maintenance:${NC}"
    echo "1. Run: /usr/local/bin/cron-configuration.sh"
    echo "2. Verify: crontab -l"
    echo "3. Remove marker: rm -f $marker_file"
}

# ============================================================================
# PERMANENT DISABLE (REMOVE SCRIPTS)
# ============================================================================

permanent_disable() {
    print_section "Permanently Disabling Maintenance (Scripts Removed)"
    
    # Stop running maintenance
    stop_running_maintenance
    
    # Disable cron jobs
    disable_cron_jobs
    
    # Backup and remove scripts
    backup_and_remove_scripts
    
    # Remove maintenance directories (but preserve logs)
    log_info "Preserving logs in /var/log/streamdb-maintenance/"
    log_info "Preserving backups in /var/backups/streamdb-maintenance/"
    
    # Create permanent disable marker
    local marker_file="/var/lib/streamdb-maintenance-permanently-disabled"
    echo "Maintenance permanently disabled on $(date) by $(whoami)" > "$marker_file"
    echo "Server: $(hostname) ($(hostname -I | awk '{print $1}'))" >> "$marker_file"
    echo "Scripts backed up and removed" >> "$marker_file"
    
    log_success "Maintenance permanently disabled"
    log_info "All scripts backed up and removed"
    log_info "Marker file created: $marker_file"
}

# ============================================================================
# STATUS CHECK
# ============================================================================

check_maintenance_status() {
    print_section "Maintenance Status Check"
    
    local server_type=$(detect_server_type)
    log_info "Server type: $server_type"
    
    # Check for running processes
    local running_processes=$(pgrep -f "maintenance.sh" 2>/dev/null || true)
    if [[ -n "$running_processes" ]]; then
        log_warn "Maintenance processes currently running: $running_processes"
    else
        log_info "No maintenance processes running"
    fi
    
    # Check lock files
    for lock_file in "$BACKEND_LOCK" "$PROXY_LOCK"; do
        if [[ -f "$lock_file" ]]; then
            local lock_pid=$(cat "$lock_file" 2>/dev/null || echo "unknown")
            log_warn "Lock file exists: $lock_file (PID: $lock_pid)"
        fi
    done
    
    # Check cron jobs
    local cron_jobs=$(crontab -l 2>/dev/null | grep -c "maintenance.sh" || echo "0")
    if [[ $cron_jobs -gt 0 ]]; then
        log_info "Active cron jobs: $cron_jobs"
        crontab -l | grep "maintenance.sh" || true
    else
        log_info "No maintenance cron jobs found"
    fi
    
    # Check scripts
    local scripts=("$BACKEND_SCRIPT" "$PROXY_SCRIPT" "$VALIDATION_SCRIPT" "$CRON_CONFIG_SCRIPT")
    local script_count=0
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            script_count=$((script_count + 1))
            log_info "Script exists: $script"
        fi
    done
    
    if [[ $script_count -eq 0 ]]; then
        log_info "No maintenance scripts found on system"
    else
        log_info "Found $script_count maintenance script(s)"
    fi
    
    # Check disable markers
    if [[ -f "/var/lib/streamdb-maintenance-disabled" ]]; then
        log_info "Temporary disable marker found"
        cat /var/lib/streamdb-maintenance-disabled
    elif [[ -f "/var/lib/streamdb-maintenance-permanently-disabled" ]]; then
        log_info "Permanent disable marker found"
        cat /var/lib/streamdb-maintenance-permanently-disabled
    else
        log_info "No disable markers found"
    fi
}

# ============================================================================
# EMERGENCY STOP
# ============================================================================

emergency_stop() {
    print_section "Emergency Stop - Immediate Termination"
    
    log_warn "Performing emergency stop of all maintenance activities"
    
    # Kill all maintenance processes immediately
    pkill -KILL -f "maintenance.sh" 2>/dev/null || true
    pkill -KILL -f "validation-scripts.sh" 2>/dev/null || true
    
    # Remove all lock files
    rm -f /var/run/streamdb-*-maintenance.lock 2>/dev/null || true
    
    # Disable all cron jobs immediately
    crontab -r 2>/dev/null || true
    rm -f "$CRON_ENV_FILE" 2>/dev/null || true
    
    # Restart cron service
    systemctl restart cron
    
    log_success "Emergency stop completed"
    log_warn "All maintenance processes terminated"
    log_warn "All cron jobs removed"
    log_warn "You may need to manually restart services if they were affected"
}

# ============================================================================
# HELP FUNCTION
# ============================================================================

show_help() {
    print_header "StreamDB Maintenance Scripts Stop/Disable Tool"
    
    echo -e "${BLUE}Usage:${NC}"
    echo "  $0 [OPTION]"
    echo ""
    echo -e "${BLUE}Options:${NC}"
    echo "  --status              Check current maintenance status"
    echo "  --stop                Stop running maintenance (keep cron jobs)"
    echo "  --disable             Temporarily disable (stop + disable cron)"
    echo "  --disable-permanently Permanently disable (remove scripts)"
    echo "  --emergency           Emergency stop (immediate termination)"
    echo "  --help               Show this help message"
    echo ""
    echo -e "${BLUE}Examples:${NC}"
    echo "  $0 --status              # Check what's currently running"
    echo "  $0 --stop               # Stop current maintenance only"
    echo "  $0 --disable            # Disable but keep scripts for re-enabling"
    echo "  $0 --disable-permanently # Remove everything permanently"
    echo "  $0 --emergency          # Emergency stop everything immediately"
    echo ""
    echo -e "${BLUE}Safety Notes:${NC}"
    echo "  • --disable preserves scripts for easy re-enabling"
    echo "  • --disable-permanently backs up scripts before removal"
    echo "  • --emergency should only be used if maintenance is stuck"
    echo "  • Always check --status first to understand current state"
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    check_root
    
    case "${1:-}" in
        "--status")
            print_header "Maintenance Status Check"
            check_maintenance_status
            ;;
        "--stop")
            print_header "Stopping Running Maintenance"
            stop_running_maintenance
            ;;
        "--disable")
            print_header "Temporarily Disabling Maintenance"
            temporary_disable
            ;;
        "--disable-permanently")
            print_header "Permanently Disabling Maintenance"
            echo -e "${RED}WARNING: This will permanently remove all maintenance scripts!${NC}"
            read -p "Are you sure you want to continue? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                permanent_disable
            else
                log_info "Operation cancelled"
            fi
            ;;
        "--emergency")
            print_header "Emergency Stop"
            echo -e "${RED}WARNING: This will immediately terminate all maintenance!${NC}"
            read -p "Are you sure you want to continue? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                emergency_stop
            else
                log_info "Operation cancelled"
            fi
            ;;
        "--help"|"-h"|"")
            show_help
            ;;
        *)
            log_error "Unknown option: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"
