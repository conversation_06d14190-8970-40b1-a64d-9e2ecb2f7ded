/// <reference types="vite/client" />

interface ImportMetaEnv {
  // TMDB Configuration
  readonly VITE_TMDB_API_KEY: string
  readonly VITE_TMDB_BASE_URL: string
  readonly VITE_TMDB_IMAGE_BASE_URL: string

  // Authentication Configuration
  readonly VITE_AUTH_ENCRYPTION_KEY: string
  readonly VITE_AUTH_SESSION_TIMEOUT: string
  readonly VITE_AUTH_MAX_LOGIN_ATTEMPTS: string
  readonly VITE_AUTH_LOCKOUT_DURATION: string

  // Security Configuration
  readonly VITE_ENABLE_SECURITY_LOGGING: string
  readonly VITE_ENABLE_BRUTE_FORCE_PROTECTION: string

  // Development Settings
  readonly VITE_DEV_MODE: string
  readonly VITE_ENABLE_DEMO_CREDENTIALS: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
