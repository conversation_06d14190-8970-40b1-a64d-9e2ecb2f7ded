/**
 * Session Security Utilities
 * Advanced session management with security features
 */

import { SessionData, AuthEventLog } from '@/types/auth';
import { AUTH_CONFIG, SECURITY_CONFIG } from '@/config/auth';
import { SecurityLogger } from '@/utils/authUtils';

/**
 * Session fingerprinting for additional security
 */
export class SessionFingerprint {
  /**
   * Generate a browser fingerprint for session validation
   */
  static generate(): string {
    const components = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      screen.colorDepth,
      new Date().getTimezoneOffset(),
      navigator.platform,
      navigator.cookieEnabled ? '1' : '0',
    ];

    // Simple hash function for fingerprint
    let hash = 0;
    const combined = components.join('|');
    
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(16);
  }

  /**
   * Validate session fingerprint
   */
  static validate(storedFingerprint: string): boolean {
    const currentFingerprint = this.generate();
    return storedFingerprint === currentFingerprint;
  }
}

// Production mode check
const IS_PRODUCTION = process.env.NODE_ENV === 'production' || !import.meta.env.DEV;

// API Configuration
const API_BASE_URL = IS_PRODUCTION ? '/api' : 'http://localhost:3001/api';

/**
 * Production-Ready Session Manager
 * Uses database sessions in production, localStorage only in development
 */
export class SecureSessionManager {
  private static readonly SESSION_KEY = 'streamdb_auth_session';
  private static readonly FINGERPRINT_KEY = 'streamdb_session_fingerprint';
  private static readonly ACTIVITY_KEY = 'streamdb_session_activity';

  /**
   * Database API for session management
   */
  private static async databaseAPI(endpoint: string, method: 'GET' | 'POST' | 'DELETE' = 'GET', data?: any): Promise<any> {
    try {
      const options: RequestInit = {
        method,
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (data && method !== 'GET') {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(`${API_BASE_URL}${endpoint}`, options);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Database API error:', error);
      throw error;
    }
  }

  /**
   * Create a new secure session
   * Production: Database-backed sessions, Development: localStorage with database sync
   */
  static async createSession(sessionData: SessionData): Promise<boolean> {
    try {
      // Generate session fingerprint
      const fingerprint = SessionFingerprint.generate();

      // Enhanced session data with security metadata
      const enhancedSessionData = {
        ...sessionData,
        fingerprint,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        ipAddress: this.getClientIP(),
        userAgent: navigator.userAgent,
      };

      if (IS_PRODUCTION) {
        // Production: Store session in database only
        try {
          const response = await this.databaseAPI('/auth/sessions', 'POST', {
            sessionData: enhancedSessionData,
            expiresAt: new Date(sessionData.expiresAt).toISOString()
          });

          if (!response || !response.success) {
            throw new Error('Database session creation failed');
          }

          // Store minimal session identifier in sessionStorage for client-side checks
          sessionStorage.setItem(this.SESSION_KEY, JSON.stringify({
            sessionId: response.sessionId,
            userId: sessionData.user.id,
            expiresAt: sessionData.expiresAt
          }));

          SecurityLogger.logEvent('SESSION_CREATED', {
            userId: sessionData.user.id,
            sessionId: response.sessionId,
            expiresAt: sessionData.expiresAt,
          }, 'low');

          return true;
        } catch (error) {
          console.error('Critical: Database session creation failed in production:', error);
          SecurityLogger.logEvent('SECURITY_VIOLATION', {
            reason: 'Database session creation failed',
            error: error instanceof Error ? error.message : 'Unknown error',
          }, 'critical');
          return false;
        }
      } else {
        // Development: Store in localStorage and sync to database
        try {
          // Try to store in database first
          await this.databaseAPI('/auth/sessions', 'POST', {
            sessionData: enhancedSessionData,
            expiresAt: new Date(sessionData.expiresAt).toISOString()
          });
        } catch (error) {
          console.warn('Database unavailable in development, using localStorage only:', error);
        }

        // Store session data locally for development
        const encryptedData = this.encryptSessionData(enhancedSessionData);
        sessionStorage.setItem(this.SESSION_KEY, encryptedData);
        sessionStorage.setItem(this.FINGERPRINT_KEY, fingerprint);
        this.updateActivity();

        SecurityLogger.logEvent('SESSION_CREATED', {
          userId: sessionData.user.id,
          fingerprint: fingerprint.substring(0, 8) + '...',
          expiresAt: sessionData.expiresAt,
        }, 'low');

        return true;
      }
    } catch (error) {
      console.error('Failed to create session:', error);
      SecurityLogger.logEvent('SECURITY_VIOLATION', {
        reason: 'Session creation failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, 'high');
      return false;
    }
  }

  /**
   * Retrieve and validate session
   * Production: Database-backed validation, Development: localStorage with database sync
   */
  static async getSession(): Promise<SessionData | null> {
    try {
      const sessionInfo = sessionStorage.getItem(this.SESSION_KEY);

      if (!sessionInfo) {
        return null;
      }

      if (IS_PRODUCTION) {
        // Production: Validate session with database
        try {
          const sessionData = JSON.parse(sessionInfo);

          if (!sessionData.sessionId || !sessionData.userId) {
            console.warn('Invalid session data in production');
            this.destroySession();
            return null;
          }

          // Check if session is expired locally first
          if (Date.now() > sessionData.expiresAt) {
            console.log('Session expired locally');
            this.destroySession();
            return null;
          }

          // Validate with database
          const response = await this.databaseAPI(`/auth/sessions/${sessionData.sessionId}`);

          if (!response || !response.success || !response.sessionData) {
            console.warn('Session not found in database or expired');
            this.destroySession();
            return null;
          }

          // Update activity
          this.updateActivity();

          return response.sessionData;
        } catch (error) {
          console.error('Session validation failed in production:', error);
          this.destroySession();
          return null;
        }
      } else {
        // Development: Use localStorage with fingerprint validation
        const storedFingerprint = sessionStorage.getItem(this.FINGERPRINT_KEY);

        if (!storedFingerprint) {
          return null;
        }

        // Validate fingerprint
        if (!SessionFingerprint.validate(storedFingerprint)) {
          SecurityLogger.logEvent('SECURITY_VIOLATION', {
            reason: 'Session fingerprint mismatch',
            storedFingerprint: storedFingerprint.substring(0, 8) + '...',
            currentFingerprint: SessionFingerprint.generate().substring(0, 8) + '...',
          }, 'critical');

          this.destroySession();
          return null;
        }

        // Decrypt and parse session data for development
        const sessionData = this.decryptSessionData(sessionInfo);
        if (!sessionData) {
          return null;
        }

        // Check session expiry
        if (Date.now() > sessionData.expiresAt) {
          SecurityLogger.logEvent('SESSION_EXPIRED', {
            userId: sessionData.user?.id,
            expiredAt: sessionData.expiresAt,
          }, 'medium');

          this.destroySession();
          return null;
        }

        // Check for session inactivity
        if (this.isSessionInactive(sessionData)) {
          SecurityLogger.logEvent('SESSION_EXPIRED', {
            userId: sessionData.user?.id,
            reason: 'Inactivity timeout',
            lastActivity: sessionData.lastActivity,
          }, 'medium');

          this.destroySession();
          return null;
        }

        // Update activity timestamp
        this.updateActivity();

        return sessionData;
      }
    } catch (error) {
      console.error('Failed to retrieve session:', error);
      this.destroySession();
      return null;
    }
  }

  /**
   * Store session (alias for createSession for backward compatibility)
   */
  static async storeSession(sessionData: SessionData): Promise<boolean> {
    return await this.createSession(sessionData);
  }

  /**
   * Update session activity timestamp
   */
  static updateActivity(): void {
    try {
      const now = Date.now();
      sessionStorage.setItem(this.ACTIVITY_KEY, now.toString());

      // Update session data with new activity timestamp
      const sessionData = this.getSessionWithoutValidation();
      if (sessionData) {
        sessionData.lastActivity = now;
        const encryptedData = this.encryptSessionData(sessionData);
        sessionStorage.setItem(this.SESSION_KEY, encryptedData);
      }
    } catch (error) {
      console.error('Failed to update activity:', error);
    }
  }

  /**
   * Destroy session and clear all related data
   * Production: Database session cleanup, Development: localStorage cleanup
   */
  static async destroySession(): Promise<void> {
    try {
      // Get session data for logging before destruction
      const sessionInfo = sessionStorage.getItem(this.SESSION_KEY);
      let userId: string | undefined;

      if (sessionInfo) {
        try {
          if (IS_PRODUCTION) {
            const sessionData = JSON.parse(sessionInfo);
            userId = sessionData.userId;

            // Delete session from database
            if (sessionData.sessionId) {
              try {
                await this.databaseAPI(`/auth/sessions/${sessionData.sessionId}`, 'DELETE');
              } catch (error) {
                console.error('Failed to delete session from database:', error);
              }
            }
          } else {
            const sessionData = this.getSessionWithoutValidation();
            userId = sessionData?.user?.id;
          }
        } catch (error) {
          console.error('Error parsing session data during destruction:', error);
        }
      }

      // Clear all session-related storage
      sessionStorage.removeItem(this.SESSION_KEY);
      sessionStorage.removeItem(this.FINGERPRINT_KEY);
      sessionStorage.removeItem(this.ACTIVITY_KEY);

      SecurityLogger.logEvent('SESSION_DESTROYED', {
        userId,
        reason: 'Manual logout or security violation',
      }, 'low');
    } catch (error) {
      console.error('Failed to destroy session:', error);
    }
  }

  /**
   * Check if session is inactive based on timeout
   */
  private static isSessionInactive(sessionData: any): boolean {
    if (!SECURITY_CONFIG.enableSessionTimeout) {
      return false;
    }

    const lastActivity = sessionData.lastActivity || sessionData.createdAt;
    const inactivityTimeout = 2 * 60 * 60 * 1000; // 2 hours of inactivity
    
    return (Date.now() - lastActivity) > inactivityTimeout;
  }

  /**
   * Get session data without validation (for internal use)
   */
  private static getSessionWithoutValidation(): any {
    try {
      const encryptedData = sessionStorage.getItem(this.SESSION_KEY);
      return encryptedData ? this.decryptSessionData(encryptedData) : null;
    } catch {
      return null;
    }
  }

  /**
   * Encrypt session data
   */
  private static encryptSessionData(data: any): string {
    try {
      const jsonString = JSON.stringify(data);
      // Simple XOR encryption (in production, use proper encryption)
      let encrypted = '';
      const key = 'StreamDB_Session_Key_2024';
      
      for (let i = 0; i < jsonString.length; i++) {
        encrypted += String.fromCharCode(
          jsonString.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      
      return btoa(encrypted);
    } catch (error) {
      throw new Error('Failed to encrypt session data');
    }
  }

  /**
   * Decrypt session data
   */
  private static decryptSessionData(encryptedData: string): any {
    try {
      const encrypted = atob(encryptedData);
      let decrypted = '';
      const key = 'StreamDB_Session_Key_2024';
      
      for (let i = 0; i < encrypted.length; i++) {
        decrypted += String.fromCharCode(
          encrypted.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      
      return JSON.parse(decrypted);
    } catch (error) {
      throw new Error('Failed to decrypt session data');
    }
  }

  /**
   * Get client IP address (best effort)
   */
  private static getClientIP(): string {
    // This is limited in browser environment
    // In production, get from server-side headers
    return 'client-side-unknown';
  }

  /**
   * Validate session integrity
   */
  static validateSessionIntegrity(): boolean {
    const sessionData = this.getSession();
    if (!sessionData) return false;

    // Check for tampering indicators
    const requiredFields = ['user', 'token', 'expiresAt', 'createdAt'];
    for (const field of requiredFields) {
      if (!(field in sessionData)) {
        SecurityLogger.logEvent('SECURITY_VIOLATION', {
          reason: 'Session data tampering detected',
          missingField: field,
        }, 'critical');
        
        this.destroySession();
        return false;
      }
    }

    return true;
  }

  /**
   * Get session statistics for monitoring
   */
  static getSessionStats(): {
    isActive: boolean;
    timeRemaining: number;
    lastActivity: number;
    fingerprint: string;
  } {
    const sessionData = this.getSessionWithoutValidation();
    const fingerprint = sessionStorage.getItem(this.FINGERPRINT_KEY) || '';
    
    if (!sessionData) {
      return {
        isActive: false,
        timeRemaining: 0,
        lastActivity: 0,
        fingerprint: '',
      };
    }

    return {
      isActive: Date.now() < sessionData.expiresAt,
      timeRemaining: Math.max(0, sessionData.expiresAt - Date.now()),
      lastActivity: sessionData.lastActivity || sessionData.createdAt,
      fingerprint: fingerprint.substring(0, 8) + '...',
    };
  }
}

/**
 * Session activity monitor
 */
export class SessionActivityMonitor {
  private static activityTimer: NodeJS.Timeout | null = null;
  private static isMonitoring = false;

  /**
   * Start monitoring user activity
   */
  static startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Monitor user interactions
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const activityHandler = () => {
      SecureSessionManager.updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, activityHandler, { passive: true });
    });

    // Periodic session validation
    this.activityTimer = setInterval(() => {
      SecureSessionManager.validateSessionIntegrity();
    }, 60000); // Check every minute

    SecurityLogger.logEvent('SESSION_MONITORING_STARTED', {}, 'low');
  }

  /**
   * Stop monitoring user activity
   */
  static stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    if (this.activityTimer) {
      clearInterval(this.activityTimer);
      this.activityTimer = null;
    }

    SecurityLogger.logEvent('SESSION_MONITORING_STOPPED', {}, 'low');
  }
}

/**
 * Initialize session security when module is loaded
 */
if (typeof window !== 'undefined') {
  // Start activity monitoring when the module loads
  SessionActivityMonitor.startMonitoring();

  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    SessionActivityMonitor.stopMonitoring();
  });
}
