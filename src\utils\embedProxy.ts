/**
 * Embed Link Proxy System
 * Provides server-side protection for embed links to prevent visibility in browser source code
 */

// Simple token-based system for embed link protection
const EMBED_TOKENS = new Map<string, { url: string; expires: number; used: boolean }>();
const TOKEN_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes

/**
 * Generate a secure token for an embed URL
 * @param embedUrl - The embed URL to protect
 * @returns Secure token that can be used to retrieve the URL
 */
export function generateEmbedToken(embedUrl: string): string {
  const token = generateSecureToken();
  const expires = Date.now() + TOKEN_EXPIRY_TIME;
  
  EMBED_TOKENS.set(token, {
    url: embedUrl,
    expires,
    used: false
  });
  
  // Clean up expired tokens
  cleanupExpiredTokens();
  
  return token;
}

/**
 * Retrieve embed URL from token (single use)
 * @param token - The secure token
 * @returns Embed URL if token is valid, null otherwise
 */
export function getEmbedUrlFromToken(token: string): string | null {
  const tokenData = EMBED_TOKENS.get(token);
  
  if (!tokenData) {
    return null;
  }
  
  if (tokenData.used || Date.now() > tokenData.expires) {
    EMBED_TOKENS.delete(token);
    return null;
  }
  
  // Mark as used for single-use security
  tokenData.used = true;
  
  return tokenData.url;
}

/**
 * Generate a cryptographically secure token
 * @returns Random secure token
 */
function generateSecureToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Clean up expired tokens to prevent memory leaks
 */
function cleanupExpiredTokens(): void {
  const now = Date.now();
  for (const [token, data] of EMBED_TOKENS.entries()) {
    if (data.expires < now || data.used) {
      EMBED_TOKENS.delete(token);
    }
  }
}

/**
 * Enhanced embed link protection using multiple layers
 */
export class EmbedLinkProtector {
  private static instance: EmbedLinkProtector;
  private protectedLinks = new Map<string, string>();
  
  static getInstance(): EmbedLinkProtector {
    if (!EmbedLinkProtector.instance) {
      EmbedLinkProtector.instance = new EmbedLinkProtector();
    }
    return EmbedLinkProtector.instance;
  }
  
  /**
   * Protect an embed link with multiple security layers
   * @param embedUrl - Original embed URL
   * @param contentId - Unique content identifier
   * @returns Protected URL that can be used in iframe
   */
  protectEmbedLink(embedUrl: string, contentId: string): string {
    // For now, return the original URL since we don't have a backend server
    // In a full implementation, this would:
    // 1. Store the embed URL on the server with a unique ID
    // 2. Return a proxy URL like `/api/embed/${uniqueId}`
    // 3. The proxy endpoint would validate the request and return the actual embed URL
    
    console.log(`🔒 Protecting embed link for content ${contentId}:`, embedUrl.substring(0, 50) + '...');
    
    // Store for future server implementation
    this.protectedLinks.set(contentId, embedUrl);
    
    return embedUrl;
  }
  
  /**
   * Get protected embed link
   * @param contentId - Content identifier
   * @returns Protected embed URL
   */
  getProtectedLink(contentId: string): string | null {
    return this.protectedLinks.get(contentId) || null;
  }
  
  /**
   * Remove protection for a content item
   * @param contentId - Content identifier
   */
  removeProtection(contentId: string): void {
    this.protectedLinks.delete(contentId);
  }
}

/**
 * Client-side embed URL obfuscation
 * Provides basic protection against casual inspection
 */
export class ClientSideEmbedProtection {
  private static readonly OBFUSCATION_KEY = 'StreamDB_2024_Secure';
  
  /**
   * Obfuscate an embed URL for client-side storage
   * @param url - Original embed URL
   * @returns Obfuscated URL string
   */
  static obfuscateUrl(url: string): string {
    try {
      const encoded = btoa(url);
      return this.simpleXOR(encoded, this.OBFUSCATION_KEY);
    } catch (error) {
      console.error('Error obfuscating URL:', error);
      return url;
    }
  }
  
  /**
   * Deobfuscate an embed URL
   * @param obfuscatedUrl - Obfuscated URL string
   * @returns Original embed URL
   */
  static deobfuscateUrl(obfuscatedUrl: string): string {
    try {
      const decoded = this.simpleXOR(obfuscatedUrl, this.OBFUSCATION_KEY);
      return atob(decoded);
    } catch (error) {
      console.error('Error deobfuscating URL:', error);
      return obfuscatedUrl;
    }
  }
  
  /**
   * Simple XOR cipher for basic obfuscation
   * @param text - Text to encode/decode
   * @param key - Encryption key
   * @returns Encoded/decoded text
   */
  private static simpleXOR(text: string, key: string): string {
    let result = '';
    for (let i = 0; i < text.length; i++) {
      result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return result;
  }
}

/**
 * Future server-side implementation guide
 * 
 * When you connect to a backend database server, implement these endpoints:
 * 
 * 1. POST /api/embed/protect
 *    - Body: { embedUrl: string, contentId: string }
 *    - Returns: { proxyUrl: string, token: string }
 * 
 * 2. GET /api/embed/:token
 *    - Headers: Referer validation, rate limiting
 *    - Returns: Redirect to actual embed URL or iframe content
 * 
 * 3. Security measures:
 *    - Rate limiting per IP
 *    - Referer header validation
 *    - Token expiration (5-10 minutes)
 *    - Single-use tokens
 *    - IP-based access control
 *    - Request logging for abuse detection
 * 
 * Example implementation:
 * ```typescript
 * app.get('/api/embed/:token', async (req, res) => {
 *   const { token } = req.params;
 *   const referer = req.get('Referer');
 *   
 *   // Validate referer
 *   if (!referer || !referer.includes(process.env.ALLOWED_DOMAIN)) {
 *     return res.status(403).json({ error: 'Invalid referer' });
 *   }
 *   
 *   // Get embed URL from token
 *   const embedUrl = await getEmbedUrlFromToken(token);
 *   if (!embedUrl) {
 *     return res.status(404).json({ error: 'Token not found or expired' });
 *   }
 *   
 *   // Redirect to actual embed URL
 *   res.redirect(embedUrl);
 * });
 * ```
 */
