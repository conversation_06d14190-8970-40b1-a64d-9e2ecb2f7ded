#!/usr/bin/env node

/**
 * StreamDB Database Cleanup and Setup Script
 * 
 * This script will:
 * 1. Clean up old database entries safely
 * 2. Create/update database schema
 * 3. Set up admin authentication with credentials from .env
 * 4. Verify the setup
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🚀 StreamDB Database Cleanup and Setup Script');
console.log('==============================================');

async function main() {
  let connection;
  
  try {
    // Database configuration from .env
    const dbConfig = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      charset: 'utf8mb4',
      timezone: '+00:00'
    };

    console.log(`\n🔗 Connecting to database: ${dbConfig.database}@${dbConfig.host}`);
    console.log(`    User: ${dbConfig.user}`);
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection successful');

    // Step 1: Check current database state
    await checkDatabaseState(connection);
    
    // Step 2: Clean up old data safely
    await cleanupOldData(connection);
    
    // Step 3: Create/update schema
    await setupSchema(connection);
    
    // Step 4: Setup admin user from .env
    await setupAdminUser(connection);
    
    // Step 5: Verify setup
    await verifySetup(connection);
    
    console.log('\n✅ Database setup completed successfully!');
    console.log('\n🎯 Next Steps:');
    console.log('   1. Start your Node.js server');
    console.log('   2. Access your admin panel');
    console.log('   3. Test login functionality');
    console.log('   4. Add your content via admin panel');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

async function checkDatabaseState(connection) {
  console.log('\n🔍 Checking current database state...');
  
  try {
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 Found ${tables.length} existing tables`);
    
    if (tables.length > 0) {
      console.log('   Existing tables:');
      tables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`   - ${tableName}`);
      });
    } else {
      console.log('   Database is empty');
    }
  } catch (error) {
    console.log(`⚠️  Could not check database state: ${error.message}`);
  }
}

async function cleanupOldData(connection) {
  console.log('\n🧹 Cleaning up old data safely...');
  
  try {
    // Only clean up session-related data that can be regenerated
    const cleanupQueries = [
      'DELETE FROM user_sessions WHERE expires_at < NOW()',
      'DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)',
      'DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)',
      'DELETE FROM auth_tokens WHERE expires_at < NOW()',
      'DELETE FROM ad_blocker_tracking WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)'
    ];
    
    for (const query of cleanupQueries) {
      try {
        const [result] = await connection.execute(query);
        console.log(`   Cleaned: ${result.affectedRows} rows from ${query.split(' ')[2]}`);
      } catch (error) {
        // Table might not exist yet, that's okay
        console.log(`   Skipping: ${query.split(' ')[2]} (table might not exist)`);
      }
    }
    
    console.log('✅ Cleanup completed');
  } catch (error) {
    console.log(`⚠️  Cleanup warning: ${error.message}`);
  }
}

async function setupSchema(connection) {
  console.log('\n📝 Setting up database schema...');
  
  try {
    // Read and execute the complete schema
    const schemaPath = path.join(__dirname, 'database', 'complete_schema.sql');
    
    if (fs.existsSync(schemaPath)) {
      console.log('   Using complete_schema.sql');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      // Split schema into individual statements and execute
      const statements = schema
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('CREATE DATABASE'));
      
      console.log(`   Executing ${statements.length} schema statements...`);
      
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await connection.execute(statement);
          } catch (error) {
            // Some statements might fail if objects already exist
            if (!error.message.includes('already exists') && !error.message.includes('duplicate')) {
              console.warn(`   Warning: ${error.message.split('\n')[0]}`);
            }
          }
        }
      }
    } else {
      // Create essential tables manually if schema file not found
      console.log('   Creating essential tables manually...');
      await createEssentialTables(connection);
    }
    
    console.log('✅ Schema setup completed');
  } catch (error) {
    console.error(`❌ Schema setup failed: ${error.message}`);
    throw error;
  }
}

async function createEssentialTables(connection) {
  const tables = [
    // Admin users table
    `CREATE TABLE IF NOT EXISTS admin_users (
      id INT PRIMARY KEY AUTO_INCREMENT,
      username VARCHAR(50) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      email VARCHAR(100) UNIQUE,
      role ENUM('admin', 'moderator') DEFAULT 'admin',
      permissions JSON,
      is_active BOOLEAN DEFAULT TRUE,
      last_login TIMESTAMP NULL,
      failed_login_attempts INT DEFAULT 0,
      locked_until TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_username (username),
      INDEX idx_email (email),
      INDEX idx_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
    
    // Admin sessions table
    `CREATE TABLE IF NOT EXISTS admin_sessions (
      id VARCHAR(128) PRIMARY KEY,
      user_id INT NOT NULL,
      ip_address VARCHAR(45),
      user_agent TEXT,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
      INDEX idx_user_id (user_id),
      INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
    
    // Security logs table
    `CREATE TABLE IF NOT EXISTS admin_security_logs (
      id INT PRIMARY KEY AUTO_INCREMENT,
      user_id INT,
      action VARCHAR(50) NOT NULL,
      ip_address VARCHAR(45),
      user_agent TEXT,
      details JSON,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL,
      INDEX idx_action (action),
      INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
    
    // Categories table
    `CREATE TABLE IF NOT EXISTS categories (
      id INT PRIMARY KEY AUTO_INCREMENT,
      name VARCHAR(100) NOT NULL UNIQUE,
      type ENUM('movie', 'series', 'both') DEFAULT 'both',
      slug VARCHAR(100) NOT NULL UNIQUE,
      description TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_type (type),
      INDEX idx_active (is_active),
      INDEX idx_slug (slug)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
    
    // Content table
    `CREATE TABLE IF NOT EXISTS content (
      id VARCHAR(50) PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      year INT NOT NULL,
      type ENUM('movie', 'series', 'requested') NOT NULL,
      category VARCHAR(100),
      image VARCHAR(500),
      cover_image VARCHAR(500),
      tmdb_id VARCHAR(20),
      poster_url VARCHAR(500),
      thumbnail_url VARCHAR(500),
      secure_video_links TEXT,
      imdb_rating DECIMAL(3,1),
      runtime VARCHAR(20),
      studio VARCHAR(255),
      tags TEXT,
      trailer VARCHAR(500),
      subtitle_url VARCHAR(500),
      is_published BOOLEAN DEFAULT FALSE,
      is_featured BOOLEAN DEFAULT FALSE,
      add_to_carousel BOOLEAN DEFAULT FALSE,
      total_seasons INT DEFAULT 0,
      total_episodes INT DEFAULT 0,
      languages JSON,
      genres JSON,
      quality JSON,
      audio_tracks JSON,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_type (type),
      INDEX idx_year (year),
      INDEX idx_published (is_published),
      INDEX idx_featured (is_featured),
      INDEX idx_carousel (add_to_carousel),
      INDEX idx_created_at (created_at),
      INDEX idx_tmdb_id (tmdb_id),
      INDEX idx_category (category),
      FULLTEXT KEY ft_search (title, description, tags)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
  ];
  
  for (const table of tables) {
    await connection.execute(table);
  }
}

async function setupAdminUser(connection) {
  console.log('\n👤 Setting up admin user...');
  
  try {
    // Get admin credentials from .env
    const adminUsername = process.env.ADMIN_USERNAME || 'streamdb_admin';
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'Ohdamn@Ufoundme2';
    
    console.log(`   Username: ${adminUsername}`);
    console.log(`   Email: ${adminEmail}`);
    
    // Check if admin user already exists
    const [existingUsers] = await connection.execute(
      'SELECT id, username FROM admin_users WHERE username = ?',
      [adminUsername]
    );
    
    if (existingUsers.length > 0) {
      console.log('   Admin user already exists, updating password...');
      
      // Update existing user with new password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(adminPassword, saltRounds);
      
      await connection.execute(
        `UPDATE admin_users SET 
         password_hash = ?, 
         email = ?, 
         failed_login_attempts = 0, 
         locked_until = NULL,
         is_active = TRUE,
         updated_at = NOW()
         WHERE username = ?`,
        [passwordHash, adminEmail, adminUsername]
      );
      
      console.log('✅ Admin user updated successfully');
    } else {
      console.log('   Creating new admin user...');
      
      // Create new admin user
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(adminPassword, saltRounds);
      
      // Define permissions
      const permissions = [
        'admin_panel_access',
        'content_create',
        'content_edit',
        'content_delete',
        'user_management',
        'system_settings',
        'bulk_operations',
        'export_data'
      ];
      
      await connection.execute(
        `INSERT INTO admin_users (username, password_hash, email, role, permissions, is_active)
         VALUES (?, ?, ?, 'admin', ?, TRUE)`,
        [adminUsername, passwordHash, adminEmail, JSON.stringify(permissions)]
      );
      
      console.log('✅ Admin user created successfully');
    }
    
    console.log('\n🔑 Admin Credentials:');
    console.log(`   Username: ${adminUsername}`);
    console.log(`   Password: ${adminPassword}`);
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Role: admin`);
    
  } catch (error) {
    console.error(`❌ Admin user setup failed: ${error.message}`);
    throw error;
  }
}

async function verifySetup(connection) {
  console.log('\n🔍 Verifying database setup...');
  
  try {
    // Check admin users
    const [users] = await connection.execute(
      'SELECT id, username, email, role, is_active, created_at FROM admin_users'
    );
    
    console.log(`✅ Admin users (${users.length}):`);
    users.forEach(user => {
      console.log(`   - ${user.username} (${user.role}) - ${user.is_active ? 'Active' : 'Inactive'}`);
    });
    
    // Check content table
    try {
      const [content] = await connection.execute('SELECT COUNT(*) as count FROM content');
      console.log(`📝 Content entries: ${content[0].count}`);
    } catch (error) {
      console.log('📝 Content table: Ready for data');
    }
    
    // Check categories
    try {
      const [categories] = await connection.execute('SELECT COUNT(*) as count FROM categories');
      console.log(`🏷️  Categories: ${categories[0].count}`);
    } catch (error) {
      console.log('🏷️  Categories table: Ready for data');
    }
    
    // Test database connection for app
    console.log('\n🧪 Testing application database connection...');
    const testQuery = 'SELECT 1 as test';
    const [testResult] = await connection.execute(testQuery);
    
    if (testResult[0].test === 1) {
      console.log('✅ Database connection test passed');
    } else {
      console.log('⚠️  Database connection test failed');
    }
    
  } catch (error) {
    console.error(`❌ Verification failed: ${error.message}`);
    throw error;
  }
}

// Run the script
main().catch(console.error);
