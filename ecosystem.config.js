module.exports = {
  apps: [
    // Main application server
    {
      name: 'streamdb-main',
      script: './server/index.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: './logs/app-error.log',
      out_file: './logs/app-out.log',
      log_file: './logs/app-combined.log',
      time: true,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 5000
    },
    
    // MySQL health check service
    {
      name: 'mysql-health',
      script: './server/services/mysql-health.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production'
      },
      error_file: './logs/mysql-health-error.log',
      out_file: './logs/mysql-health-out.log',
      log_file: './logs/mysql-health-combined.log',
      time: true,
      autorestart: true,
      watch: false,
      max_memory_restart: '256M',
      restart_delay: 10000
    },


  ]
};
