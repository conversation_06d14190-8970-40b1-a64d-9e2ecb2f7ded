# Streaming Database Backend API

A comprehensive Node.js/Express backend API for the Streaming Database website with MySQL integration, authentication, file uploads, and admin panel support.

## Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Content Management**: Full CRUD operations for movies and web series
- **Category System**: Hierarchical content categorization
- **File Uploads**: Image, video, and subtitle file handling with optimization
- **Security**: Rate limiting, input validation, SQL injection protection
- **Admin Panel**: Dashboard, user management, security logs, data export
- **Database Integration**: MySQL with connection pooling and transactions
- **API Documentation**: RESTful API with comprehensive error handling

## Prerequisites

- Node.js 16+ 
- MySQL 5.7+ or MariaDB 10.3+
- Alexhost VPS with FastPanel access

## Installation

### 1. Install Dependencies

```bash
cd server
npm install
```

### 2. Environment Configuration

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your Alexhost database credentials:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=streamdb_database
DB_USER=streamdb_opl_user
DB_PASSWORD=your_actual_database_password

# Security
JWT_SECRET=your_very_long_random_jwt_secret_key_here
SESSION_SECRET=your_very_long_random_session_secret_key_here

# Server
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://your-domain.com
```

### 3. Database Setup

1. **Create database schema** (run in phpMyAdmin):
   ```sql
   -- Copy and paste contents of ../database/schema.sql
   ```

2. **Insert initial data**:
   ```sql
   -- Copy and paste contents of ../database/initial_data.sql
   ```

3. **Import your CSV data** (optional):
   ```sql
   -- Use ../database/csv_import_template.sql as reference
   ```

### 4. Create Upload Directories

```bash
mkdir -p uploads/images uploads/videos uploads/subtitles uploads/temp
```

### 5. Start the Server

**Development:**
```bash
npm run dev
```

**Production:**
```bash
npm start
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - Admin login
- `POST /api/auth/logout` - Logout
- `GET /api/auth/verify` - Verify token
- `POST /api/auth/register` - Create new admin user (admin only)
- `POST /api/auth/change-password` - Change password

### Content Management
- `GET /api/content` - Get all content (with filtering)
- `GET /api/content/:id` - Get single content item
- `POST /api/content` - Create new content (admin/moderator)
- `PUT /api/content/:id` - Update content (admin/moderator)
- `DELETE /api/content/:id` - Delete content (admin/moderator)
- `POST /api/content/bulk` - Bulk operations (admin/moderator)

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/:id` - Get single category
- `POST /api/categories` - Create category (admin/moderator)
- `PUT /api/categories/:id` - Update category (admin/moderator)
- `DELETE /api/categories/:id` - Delete category (admin/moderator)
- `GET /api/categories/:slug/content` - Get content by category

### File Uploads
- `POST /api/upload/image` - Upload single image
- `POST /api/upload/images` - Upload multiple images
- `POST /api/upload/subtitle` - Upload subtitle file
- `DELETE /api/upload/:type/:filename` - Delete uploaded file
- `GET /api/upload/:type/:filename/info` - Get file info

### Admin Panel
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/users` - Get all users (admin only)
- `PATCH /api/admin/users/:id/status` - Update user status (admin only)
- `GET /api/admin/security-logs` - Get security logs (admin only)
- `GET /api/admin/export/content` - Export content to CSV
- `GET /api/admin/health` - System health check

## Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Default Admin Account

- **Username**: `admin`
- **Password**: `admin123`

**⚠️ IMPORTANT**: Change this password immediately after setup!

## Error Handling

All API responses follow this format:

**Success Response:**
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... }
}
```

**Error Response:**
```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "details": [ ... ] // For validation errors
}
```

## Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Authentication Rate Limiting**: 5 auth attempts per 15 minutes per IP
- **Input Validation**: All inputs validated and sanitized
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Helmet.js security headers
- **CORS**: Configurable cross-origin resource sharing
- **Session Management**: Secure session handling
- **Password Hashing**: bcrypt with 12 rounds
- **Account Lockout**: Automatic lockout after failed attempts

## File Upload Configuration

- **Max File Size**: 10MB (configurable)
- **Allowed Image Types**: JPEG, PNG, WebP, GIF
- **Allowed Video Types**: MP4, WebM, OGG
- **Allowed Subtitle Types**: SRT, VTT
- **Image Optimization**: Automatic compression and resizing

## Database Schema

The database includes these main tables:
- `content` - Movies and web series
- `categories` - Content categories
- `genres` - Content genres
- `languages` - Available languages
- `quality_options` - Video quality options
- `audio_tracks` - Audio track options
- `seasons` - Web series seasons
- `episodes` - Web series episodes
- `admin_users` - Admin user accounts
- `admin_sessions` - User sessions
- `admin_security_logs` - Security audit logs

## Deployment on Alexhost VPS

### 1. Upload Files
Upload the entire `server` directory to your VPS.

### 2. Install Node.js
```bash
# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 3. Install Dependencies
```bash
cd /path/to/your/server
npm install --production
```

### 4. Configure Environment
```bash
cp .env.example .env
nano .env  # Edit with your actual values
```

### 5. Set Up Process Manager
```bash
# Install PM2
npm install -g pm2

# Start the application
pm2 start index.js --name "streaming-db-api"

# Save PM2 configuration
pm2 save
pm2 startup
```

### 6. Configure Nginx (if needed)
```nginx
server {
    listen 80;
    server_name your-api-domain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in `.env`
   - Verify database server is running
   - Check firewall settings

2. **Permission Denied on File Upload**
   - Check upload directory permissions: `chmod 755 uploads`
   - Ensure Node.js process has write access

3. **JWT Token Invalid**
   - Check JWT_SECRET in `.env`
   - Verify token hasn't expired

4. **CORS Errors**
   - Update FRONTEND_URL in `.env`
   - Check CORS_ORIGIN configuration

### Logs

Check application logs:
```bash
# PM2 logs
pm2 logs streaming-db-api

# Application logs
tail -f logs/server.log
```

## Support

For issues specific to:
- **Database**: Check Alexhost documentation
- **Server**: Contact Alexhost support
- **Application**: Check error logs and API responses
