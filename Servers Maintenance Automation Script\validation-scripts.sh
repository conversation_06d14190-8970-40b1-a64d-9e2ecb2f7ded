#!/bin/bash

# ============================================================================
# StreamDB Infrastructure Validation Scripts
# Purpose: Test and validate maintenance scripts before deployment
# ============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_SCRIPT="/usr/local/bin/backend-maintenance.sh"
PROXY_SCRIPT="/usr/local/bin/reverse-proxy-maintenance.sh"
TEST_LOG="/tmp/streamdb-validation-$(date +%Y%m%d-%H%M%S).log"

print_header() {
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$TEST_LOG"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$TEST_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$TEST_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$TEST_LOG"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

detect_server_type() {
    local hostname=$(hostname)
    local ip=$(hostname -I | awk '{print $1}')
    
    if [[ "$hostname" == "backend1maindb" ]] || [[ "$ip" == "***********" ]]; then
        echo "backend"
    elif [[ "$hostname" == "backend2ndrevproxy" ]] || [[ "$ip" == "*************" ]]; then
        echo "proxy"
    else
        echo "unknown"
    fi
}

# ============================================================================
# PRE-MAINTENANCE VALIDATION
# ============================================================================

validate_prerequisites() {
    print_section "Validating Prerequisites"
    
    local validation_passed=true
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        log_error "Must run as root"
        validation_passed=false
    else
        log_info "Running as root: ✓"
    fi
    
    # Check required commands
    local required_commands=("curl" "systemctl" "apt" "nginx" "mysql" "pm2")
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            log_info "Command '$cmd' available: ✓"
        else
            log_error "Command '$cmd' not found"
            validation_passed=false
        fi
    done
    
    # Check disk space (need at least 2GB free)
    local free_space=$(df / | awk 'NR==2 {print $4}')
    local free_space_gb=$((free_space / 1024 / 1024))
    if [[ $free_space_gb -ge 2 ]]; then
        log_info "Sufficient disk space: ${free_space_gb}GB free ✓"
    else
        log_error "Insufficient disk space: only ${free_space_gb}GB free"
        validation_passed=false
    fi
    
    # Check memory (need at least 512MB free)
    local free_mem=$(free -m | awk 'NR==2{print $7}')
    if [[ $free_mem -ge 512 ]]; then
        log_info "Sufficient memory: ${free_mem}MB free ✓"
    else
        log_warn "Low memory: only ${free_mem}MB free"
    fi
    
    if [[ "$validation_passed" == true ]]; then
        log_success "All prerequisites validated"
        return 0
    else
        log_error "Prerequisites validation failed"
        return 1
    fi
}

capture_service_baseline() {
    print_section "Capturing Service Baseline"
    
    # Capture current service states
    declare -gA BASELINE_SERVICES
    BASELINE_SERVICES[nginx]=$(systemctl is-active nginx 2>/dev/null || echo "inactive")
    BASELINE_SERVICES[mysql]=$(systemctl is-active mysql 2>/dev/null || echo "inactive")
    BASELINE_SERVICES[fastpanel2]=$(systemctl is-active fastpanel2 2>/dev/null || echo "inactive")
    
    # Capture PM2 status
    if command -v pm2 >/dev/null 2>&1; then
        BASELINE_SERVICES[pm2]=$(pm2 list | grep -q "streamdb-online.*online" && echo "active" || echo "inactive")
    else
        BASELINE_SERVICES[pm2]="not_installed"
    fi
    
    log_info "Service baseline captured:"
    for service in "${!BASELINE_SERVICES[@]}"; do
        log_info "  $service: ${BASELINE_SERVICES[$service]}"
    done
}

test_website_connectivity() {
    print_section "Testing Website Connectivity"
    
    local connectivity_passed=true
    
    # Test local connectivity
    if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200\|301\|302"; then
        log_info "Local website connectivity: ✓"
    else
        log_error "Local website connectivity failed"
        connectivity_passed=false
    fi
    
    # Test API endpoint (if backend server)
    local server_type=$(detect_server_type)
    if [[ "$server_type" == "backend" ]]; then
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/health | grep -q "200"; then
            log_info "API endpoint connectivity: ✓"
        else
            log_error "API endpoint connectivity failed"
            connectivity_passed=false
        fi
    fi
    
    # Test external connectivity (if proxy server)
    if [[ "$server_type" == "proxy" ]]; then
        for domain in "streamdb.online" "fastpanel.streamdb.online"; do
            if curl -s -o /dev/null -w "%{http_code}" "https://$domain" | grep -q "200\|301\|302"; then
                log_info "External connectivity to $domain: ✓"
            else
                log_error "External connectivity to $domain failed"
                connectivity_passed=false
            fi
        done
    fi
    
    if [[ "$connectivity_passed" == true ]]; then
        log_success "Website connectivity tests passed"
        return 0
    else
        log_error "Website connectivity tests failed"
        return 1
    fi
}

# ============================================================================
# MAINTENANCE SCRIPT VALIDATION
# ============================================================================

validate_script_syntax() {
    print_section "Validating Script Syntax"
    
    local server_type=$(detect_server_type)
    local script_to_test=""
    
    case "$server_type" in
        "backend")
            script_to_test="$BACKEND_SCRIPT"
            ;;
        "proxy")
            script_to_test="$PROXY_SCRIPT"
            ;;
        *)
            log_error "Unknown server type - cannot determine which script to test"
            return 1
            ;;
    esac
    
    if [[ ! -f "$script_to_test" ]]; then
        log_error "Maintenance script not found: $script_to_test"
        return 1
    fi
    
    # Check script syntax
    if bash -n "$script_to_test"; then
        log_info "Script syntax validation: ✓"
    else
        log_error "Script syntax validation failed"
        return 1
    fi
    
    # Check script permissions
    if [[ -x "$script_to_test" ]]; then
        log_info "Script permissions: ✓"
    else
        log_error "Script is not executable"
        return 1
    fi
    
    log_success "Script validation passed"
    return 0
}

dry_run_maintenance() {
    print_section "Performing Dry Run (Simulation)"
    
    local server_type=$(detect_server_type)
    
    log_info "Simulating maintenance operations..."
    
    # Simulate package list update
    if apt list --upgradable >/dev/null 2>&1; then
        log_info "Package list update simulation: ✓"
    else
        log_error "Package list update simulation failed"
        return 1
    fi
    
    # Simulate configuration backup
    local temp_backup="/tmp/test-backup-$(date +%s).tar.gz"
    if tar -czf "$temp_backup" /etc/nginx/nginx.conf >/dev/null 2>&1; then
        log_info "Configuration backup simulation: ✓"
        rm -f "$temp_backup"
    else
        log_error "Configuration backup simulation failed"
        return 1
    fi
    
    # Test service status checks
    for service in nginx mysql; do
        if systemctl status "$service" >/dev/null 2>&1; then
            log_info "Service status check for $service: ✓"
        else
            log_warn "Service status check for $service: service not running"
        fi
    done
    
    log_success "Dry run completed successfully"
    return 0
}

# ============================================================================
# POST-MAINTENANCE VALIDATION
# ============================================================================

validate_services_after_maintenance() {
    print_section "Validating Services After Maintenance"
    
    local validation_passed=true
    
    # Check if services are in expected states
    for service in "${!BASELINE_SERVICES[@]}"; do
        local baseline_state="${BASELINE_SERVICES[$service]}"
        local current_state=""
        
        case "$service" in
            "pm2")
                if command -v pm2 >/dev/null 2>&1; then
                    current_state=$(pm2 list | grep -q "streamdb-online.*online" && echo "active" || echo "inactive")
                else
                    current_state="not_installed"
                fi
                ;;
            *)
                current_state=$(systemctl is-active "$service" 2>/dev/null || echo "inactive")
                ;;
        esac
        
        if [[ "$baseline_state" == "$current_state" ]]; then
            log_info "Service $service state maintained: $current_state ✓"
        else
            log_error "Service $service state changed: $baseline_state → $current_state"
            validation_passed=false
        fi
    done
    
    if [[ "$validation_passed" == true ]]; then
        log_success "Service state validation passed"
        return 0
    else
        log_error "Service state validation failed"
        return 1
    fi
}

validate_website_after_maintenance() {
    print_section "Validating Website After Maintenance"
    
    # Wait a moment for services to stabilize
    sleep 5
    
    # Re-run connectivity tests
    test_website_connectivity
}

# ============================================================================
# COMPREHENSIVE TEST SUITE
# ============================================================================

run_comprehensive_test() {
    print_section "Running Comprehensive Test Suite"
    
    local test_passed=true
    
    # Pre-maintenance validation
    if ! validate_prerequisites; then
        test_passed=false
    fi
    
    if ! capture_service_baseline; then
        test_passed=false
    fi
    
    if ! test_website_connectivity; then
        test_passed=false
    fi
    
    if ! validate_script_syntax; then
        test_passed=false
    fi
    
    if ! dry_run_maintenance; then
        test_passed=false
    fi
    
    if [[ "$test_passed" == true ]]; then
        log_success "All validation tests passed"
        return 0
    else
        log_error "Some validation tests failed"
        return 1
    fi
}

run_actual_maintenance_test() {
    print_section "Running Actual Maintenance Test"
    
    local server_type=$(detect_server_type)
    local script_to_run=""
    
    case "$server_type" in
        "backend")
            script_to_run="$BACKEND_SCRIPT"
            ;;
        "proxy")
            script_to_run="$PROXY_SCRIPT"
            ;;
        *)
            log_error "Unknown server type - cannot run maintenance test"
            return 1
            ;;
    esac
    
    log_warn "This will run the actual maintenance script!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Maintenance test cancelled by user"
        return 0
    fi
    
    log_info "Running maintenance script: $script_to_run"
    
    # Capture baseline before running
    capture_service_baseline
    
    # Run the maintenance script
    if "$script_to_run"; then
        log_success "Maintenance script completed successfully"
        
        # Validate after maintenance
        validate_services_after_maintenance
        validate_website_after_maintenance
        
        return 0
    else
        log_error "Maintenance script failed"
        return 1
    fi
}

show_validation_summary() {
    print_section "Validation Summary"
    
    echo -e "${BLUE}Validation log saved to: $TEST_LOG${NC}"
    echo ""
    echo -e "${BLUE}To view the full log:${NC}"
    echo "  cat $TEST_LOG"
    echo ""
    echo -e "${BLUE}To run individual tests:${NC}"
    echo "  $0 --prerequisites"
    echo "  $0 --connectivity"
    echo "  $0 --syntax"
    echo "  $0 --dry-run"
    echo "  $0 --full-test"
    echo ""
    echo -e "${BLUE}To run actual maintenance (use with caution):${NC}"
    echo "  $0 --run-maintenance"
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    check_root
    
    print_header "StreamDB Infrastructure Validation"
    
    local server_type=$(detect_server_type)
    log_info "Detected server type: $server_type"
    log_info "Validation log: $TEST_LOG"
    
    case "${1:-}" in
        "--prerequisites")
            validate_prerequisites
            ;;
        "--connectivity")
            test_website_connectivity
            ;;
        "--syntax")
            validate_script_syntax
            ;;
        "--dry-run")
            dry_run_maintenance
            ;;
        "--full-test")
            run_comprehensive_test
            ;;
        "--run-maintenance")
            run_actual_maintenance_test
            ;;
        *)
            log_info "Running comprehensive validation suite..."
            run_comprehensive_test
            show_validation_summary
            ;;
    esac
}

# Execute main function
main "$@"
