/**
 * Aspect Ratio Detection and Management Utilities
 * 
 * This module provides utilities for detecting optimal aspect ratios
 * for video embeds based on platform-specific characteristics and
 * responsive design requirements.
 */

import { detectVideoPlatform } from './videoSecurity';

/**
 * Standard aspect ratio definitions
 */
export interface AspectRatio {
  ratio: number;
  width: number;
  height: number;
  className: string;
  description: string;
}

/**
 * Predefined aspect ratios for different content types
 */
export const ASPECT_RATIOS: Record<string, AspectRatio> = {
  // Standard video formats
  '16:9': {
    ratio: 16 / 9,
    width: 16,
    height: 9,
    className: 'aspect-video',
    description: 'Standard widescreen (16:9)'
  },
  '4:3': {
    ratio: 4 / 3,
    width: 4,
    height: 3,
    className: 'aspect-[4/3]',
    description: 'Traditional TV (4:3)'
  },
  '21:9': {
    ratio: 21 / 9,
    width: 21,
    height: 9,
    className: 'aspect-[21/9]',
    description: 'Ultra-wide (21:9)'
  },
  '1:1': {
    ratio: 1,
    width: 1,
    height: 1,
    className: 'aspect-square',
    description: 'Square (1:1)'
  },
  '9:16': {
    ratio: 9 / 16,
    width: 9,
    height: 16,
    className: 'aspect-[9/16]',
    description: 'Vertical/Mobile (9:16)'
  },
  '3:2': {
    ratio: 3 / 2,
    width: 3,
    height: 2,
    className: 'aspect-[3/2]',
    description: 'Classic photo (3:2)'
  }
};

/**
 * Platform-specific aspect ratio preferences
 */
export const PLATFORM_ASPECT_RATIOS: Record<string, string> = {
  youtube: '16:9',
  vimeo: '16:9',
  dailymotion: '16:9',
  twitch: '16:9',
  wistia: '16:9',
  jwplayer: '16:9',
  streamable: '16:9',
  '2embed': '16:9',
  bitchute: '16:9',
  rumble: '16:9',
  odysee: '16:9',
  unknown: '16:9' // Default fallback
};

/**
 * Detect optimal aspect ratio for a video URL
 * @param url - Video embed URL
 * @returns AspectRatio object with optimal settings
 */
export function detectOptimalAspectRatio(url: string): AspectRatio {
  if (!url) {
    return ASPECT_RATIOS['16:9']; // Default fallback
  }

  // Detect platform
  const platform = detectVideoPlatform(url);
  
  // Get platform-specific aspect ratio preference
  const preferredRatio = PLATFORM_ASPECT_RATIOS[platform] || '16:9';
  
  // Check for specific URL patterns that might indicate different ratios
  const aspectRatio = analyzeUrlForAspectRatio(url, preferredRatio);
  
  return ASPECT_RATIOS[aspectRatio] || ASPECT_RATIOS['16:9'];
}

/**
 * Analyze URL for aspect ratio hints
 * @param url - Video URL to analyze
 * @param defaultRatio - Default ratio to use if no hints found
 * @returns Aspect ratio string
 */
function analyzeUrlForAspectRatio(url: string, defaultRatio: string): string {
  const lowerUrl = url.toLowerCase();
  
  // Check for vertical video indicators (mobile/short-form content)
  if (lowerUrl.includes('shorts') || lowerUrl.includes('vertical') || lowerUrl.includes('mobile')) {
    return '9:16';
  }
  
  // Check for ultra-wide indicators
  if (lowerUrl.includes('ultrawide') || lowerUrl.includes('cinema')) {
    return '21:9';
  }
  
  // Check for square video indicators
  if (lowerUrl.includes('square') || lowerUrl.includes('1x1')) {
    return '1:1';
  }
  
  // Check for traditional TV format indicators
  if (lowerUrl.includes('4x3') || lowerUrl.includes('traditional')) {
    return '4:3';
  }
  
  return defaultRatio;
}

/**
 * Get responsive aspect ratio classes for different screen sizes
 * @param baseRatio - Base aspect ratio to use
 * @returns Object with responsive classes
 */
export function getResponsiveAspectRatio(baseRatio: AspectRatio): {
  mobile: string;
  tablet: string;
  desktop: string;
  combined: string;
} {
  // For mobile, we might want to adjust certain ratios for better viewing
  let mobileRatio = baseRatio;
  
  // Ultra-wide content should be 16:9 on mobile for better usability
  if (baseRatio.className === 'aspect-[21/9]') {
    mobileRatio = ASPECT_RATIOS['16:9'];
  }
  
  // Vertical content stays vertical on mobile
  if (baseRatio.className === 'aspect-[9/16]') {
    mobileRatio = baseRatio;
  }
  
  return {
    mobile: mobileRatio.className,
    tablet: baseRatio.className,
    desktop: baseRatio.className,
    combined: `${mobileRatio.className} md:${baseRatio.className}`
  };
}

/**
 * Calculate dynamic padding-bottom for aspect ratio (CSS fallback)
 * @param aspectRatio - AspectRatio object
 * @returns Padding-bottom percentage string
 */
export function calculatePaddingBottom(aspectRatio: AspectRatio): string {
  const percentage = (aspectRatio.height / aspectRatio.width) * 100;
  return `${percentage.toFixed(2)}%`;
}

/**
 * Check if aspect ratio is suitable for mobile viewing
 * @param aspectRatio - AspectRatio to check
 * @returns True if mobile-friendly
 */
export function isMobileFriendly(aspectRatio: AspectRatio): boolean {
  // Ratios that work well on mobile screens
  const mobileFriendlyRatios = ['16:9', '4:3', '9:16', '1:1', '3:2'];
  
  return Object.entries(ASPECT_RATIOS).some(([key, ratio]) => 
    mobileFriendlyRatios.includes(key) && ratio.ratio === aspectRatio.ratio
  );
}

/**
 * Get aspect ratio information from URL with fallback options
 * @param url - Video embed URL
 * @param options - Configuration options
 * @returns Complete aspect ratio configuration
 */
export interface AspectRatioOptions {
  forceMobileFriendly?: boolean;
  preferredRatio?: string;
  enableResponsive?: boolean;
}

export function getAspectRatioConfig(
  url: string, 
  options: AspectRatioOptions = {}
): {
  aspectRatio: AspectRatio;
  responsive: ReturnType<typeof getResponsiveAspectRatio>;
  paddingBottom: string;
  isMobileFriendly: boolean;
  platform: string;
} {
  // Use preferred ratio if specified, otherwise detect
  let aspectRatio: AspectRatio;
  
  if (options.preferredRatio && ASPECT_RATIOS[options.preferredRatio]) {
    aspectRatio = ASPECT_RATIOS[options.preferredRatio];
  } else {
    aspectRatio = detectOptimalAspectRatio(url);
  }
  
  // Force mobile-friendly ratio if requested and current ratio isn't suitable
  if (options.forceMobileFriendly && !isMobileFriendly(aspectRatio)) {
    aspectRatio = ASPECT_RATIOS['16:9']; // Safe fallback
  }
  
  const responsive = getResponsiveAspectRatio(aspectRatio);
  const paddingBottom = calculatePaddingBottom(aspectRatio);
  const mobileFriendly = isMobileFriendly(aspectRatio);
  const platform = detectVideoPlatform(url);
  
  return {
    aspectRatio,
    responsive,
    paddingBottom,
    isMobileFriendly: mobileFriendly,
    platform
  };
}
