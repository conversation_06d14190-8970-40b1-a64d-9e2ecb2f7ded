/**
 * StreamDB MySQL Database Diagnostic and Monitoring System
 * Comprehensive diagnostic tools for database health, schema validation, and performance monitoring
 * 
 * Features:
 * - Database schema validation for Admin Panel compatibility
 * - Connection health monitoring and error detection
 * - Performance monitoring and slow query detection
 * - Automated error classification with actionable solutions
 * - Integration with existing error handling systems
 * 
 * Safety: Read-only operations, non-intrusive monitoring, graceful degradation
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

class DatabaseDiagnostic {
  constructor() {
    this.dbConfig = this.buildDatabaseConfig();
    this.diagnosticResults = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      tests: [],
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0,
        critical: 0
      },
      recommendations: []
    };
    
    // Required tables and their essential fields for Admin Panel functionality
    this.requiredSchema = {
      'content': {
        required_fields: [
          'id', 'title', 'description', 'year', 'type', 'category', 
          'image', 'cover_image', 'tmdb_id', 'poster_url', 'thumbnail_url',
          'secure_video_links', 'imdb_rating', 'runtime', 'studio', 'tags',
          'trailer', 'subtitle_url', 'is_published', 'is_featured', 
          'add_to_carousel', 'total_seasons', 'total_episodes', 'languages',
          'genres', 'quality', 'audio_tracks', 'created_at', 'updated_at'
        ],
        indexes: ['PRIMARY', 'idx_type', 'idx_category', 'idx_published', 'idx_featured', 'ft_search'],
        description: 'Main content table for movies and web series'
      },
      'admin_users': {
        required_fields: [
          'id', 'username', 'password_hash', 'email', 'role', 'permissions',
          'is_active', 'last_login', 'created_at', 'updated_at'
        ],
        indexes: ['PRIMARY', 'idx_username', 'idx_email', 'idx_active'],
        description: 'Admin authentication and user management'
      },
      'categories': {
        required_fields: [
          'id', 'name', 'slug', 'description', 'icon', 'color',
          'display_order', 'is_active', 'created_at', 'updated_at'
        ],
        indexes: ['PRIMARY', 'idx_slug', 'idx_active', 'idx_display_order'],
        description: 'Content categories for organization'
      },
      'seasons': {
        required_fields: [
          'id', 'content_id', 'season_number', 'title', 'description',
          'poster_url', 'air_date', 'episode_count', 'created_at', 'updated_at'
        ],
        indexes: ['PRIMARY', 'idx_content_id', 'idx_season_number'],
        description: 'Web series seasons management'
      },
      'episodes': {
        required_fields: [
          'id', 'season_id', 'episode_number', 'title', 'description',
          'runtime', 'air_date', 'video_links', 'thumbnail_url',
          'created_at', 'updated_at'
        ],
        indexes: ['PRIMARY', 'idx_season_id', 'idx_episode_number'],
        description: 'Individual episodes for web series'
      }
    };
  }

  buildDatabaseConfig() {
    const config = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4',
      timezone: '+00:00',
      connectTimeout: 30000,
      acquireTimeout: 30000,
      timeout: 30000,
      ssl: false,
      multipleStatements: false
    };

    // Use socket connection for production (most secure)
    if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET && process.env.DB_REMOTE_ACCESS !== 'true') {
      config.socketPath = process.env.DB_SOCKET;
      this.log('INFO', 'Database Config', `Using MySQL socket connection: ${process.env.DB_SOCKET}`);
    } else {
      config.host = process.env.DB_HOST || 'localhost';
      config.port = process.env.DB_PORT || 3306;
      this.log('INFO', 'Database Config', `Using MySQL TCP connection: ${config.host}:${config.port}`);
    }

    return config;
  }

  log(level, test, message, details = {}) {
    const result = {
      level,
      test,
      message,
      details,
      timestamp: new Date().toISOString()
    };

    this.diagnosticResults.tests.push(result);
    
    // Update summary counters
    switch(level) {
      case 'PASS': this.diagnosticResults.summary.passed++; break;
      case 'FAIL': this.diagnosticResults.summary.failed++; break;
      case 'WARN': this.diagnosticResults.summary.warnings++; break;
      case 'CRITICAL': this.diagnosticResults.summary.critical++; break;
    }

    // Console output with colors
    const colors = {
      PASS: '\x1b[32m✅',
      FAIL: '\x1b[31m❌',
      WARN: '\x1b[33m⚠️',
      CRITICAL: '\x1b[35m🚨',
      INFO: '\x1b[34mℹ️'
    };

    console.log(`${colors[level] || colors.INFO} ${test}: ${message}\x1b[0m`);
    if (Object.keys(details).length > 0) {
      console.log(`   Details:`, details);
    }
  }

  addRecommendation(priority, category, issue, solution, impact = 'medium') {
    this.diagnosticResults.recommendations.push({
      priority,
      category,
      issue,
      solution,
      impact,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Test basic database connectivity
   */
  async testDatabaseConnection() {
    try {
      this.log('INFO', 'Connection Test', 'Testing database connectivity...');
      
      const connection = await mysql.createConnection(this.dbConfig);
      const [rows] = await connection.execute('SELECT 1 as test, NOW() as server_time, VERSION() as mysql_version');
      await connection.end();

      if (rows && rows[0]) {
        this.log('PASS', 'Database Connection', 'Successfully connected to MySQL database', {
          mysql_version: rows[0].mysql_version,
          server_time: rows[0].server_time,
          connection_method: this.dbConfig.socketPath ? 'socket' : 'tcp'
        });
        return true;
      } else {
        this.log('FAIL', 'Database Connection', 'Connection test returned unexpected result');
        return false;
      }
    } catch (error) {
      this.log('CRITICAL', 'Database Connection', 'Failed to connect to database', {
        error: error.message,
        code: error.code,
        errno: error.errno,
        sqlState: error.sqlState
      });
      
      this.addRecommendation('HIGH', 'Connection', 
        'Database connection failed', 
        this.getDatabaseConnectionSolution(error),
        'high'
      );
      return false;
    }
  }

  /**
   * Validate database schema for Admin Panel compatibility
   */
  async validateDatabaseSchema() {
    try {
      this.log('INFO', 'Schema Validation', 'Validating database schema for Admin Panel compatibility...');
      
      const connection = await mysql.createConnection(this.dbConfig);
      let allTablesValid = true;
      const schemaIssues = [];

      for (const [tableName, tableSchema] of Object.entries(this.requiredSchema)) {
        try {
          // Check if table exists
          const [tableExists] = await connection.execute(
            'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
            [this.dbConfig.database, tableName]
          );

          if (tableExists[0].count === 0) {
            this.log('FAIL', 'Schema Validation', `Required table '${tableName}' does not exist`, {
              table: tableName,
              description: tableSchema.description
            });
            schemaIssues.push(`Missing table: ${tableName}`);
            allTablesValid = false;
            continue;
          }

          // Check table structure
          const [columns] = await connection.execute('DESCRIBE ??', [tableName]);
          const existingFields = columns.map(col => col.Field);
          const missingFields = tableSchema.required_fields.filter(field => !existingFields.includes(field));

          if (missingFields.length > 0) {
            this.log('FAIL', 'Schema Validation', `Table '${tableName}' missing required fields`, {
              table: tableName,
              missing_fields: missingFields,
              existing_fields: existingFields.length
            });
            schemaIssues.push(`${tableName}: Missing fields - ${missingFields.join(', ')}`);
            allTablesValid = false;
          } else {
            this.log('PASS', 'Schema Validation', `Table '${tableName}' has all required fields`, {
              table: tableName,
              field_count: existingFields.length
            });
          }

          // Check indexes
          const [indexes] = await connection.execute('SHOW INDEX FROM ??', [tableName]);
          const existingIndexes = [...new Set(indexes.map(idx => idx.Key_name))];
          const missingIndexes = tableSchema.indexes.filter(idx => !existingIndexes.includes(idx));

          if (missingIndexes.length > 0) {
            this.log('WARN', 'Schema Validation', `Table '${tableName}' missing recommended indexes`, {
              table: tableName,
              missing_indexes: missingIndexes,
              existing_indexes: existingIndexes
            });
            this.addRecommendation('MEDIUM', 'Performance', 
              `Missing indexes on table ${tableName}`, 
              `Add missing indexes: ${missingIndexes.join(', ')} for better query performance`,
              'medium'
            );
          }

        } catch (tableError) {
          this.log('FAIL', 'Schema Validation', `Error validating table '${tableName}'`, {
            table: tableName,
            error: tableError.message
          });
          schemaIssues.push(`${tableName}: Validation error - ${tableError.message}`);
          allTablesValid = false;
        }
      }

      await connection.end();

      if (allTablesValid) {
        this.log('PASS', 'Schema Validation', 'All required tables and fields exist for Admin Panel functionality');
        return true;
      } else {
        this.addRecommendation('HIGH', 'Schema', 
          'Database schema issues detected', 
          `Fix schema issues: ${schemaIssues.join('; ')}`,
          'high'
        );
        return false;
      }

    } catch (error) {
      this.log('CRITICAL', 'Schema Validation', 'Failed to validate database schema', {
        error: error.message,
        code: error.code
      });
      return false;
    }
  }
  }

  /**
   * Monitor connection pool health and performance
   */
  async monitorConnectionPool() {
    try {
      this.log('INFO', 'Connection Pool', 'Monitoring connection pool health...');

      const pool = mysql.createPool(this.dbConfig);
      const startTime = Date.now();

      // Test multiple concurrent connections
      const connectionPromises = [];
      for (let i = 0; i < 5; i++) {
        connectionPromises.push(this.testPoolConnection(pool, i));
      }

      const results = await Promise.allSettled(connectionPromises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      const totalTime = Date.now() - startTime;

      await pool.end();

      if (successful === 5) {
        this.log('PASS', 'Connection Pool', 'Connection pool is healthy', {
          successful_connections: successful,
          failed_connections: failed,
          total_time_ms: totalTime,
          avg_time_per_connection: Math.round(totalTime / 5)
        });
        return true;
      } else {
        this.log('WARN', 'Connection Pool', 'Connection pool has issues', {
          successful_connections: successful,
          failed_connections: failed,
          total_time_ms: totalTime
        });

        if (failed > 2) {
          this.addRecommendation('HIGH', 'Connection Pool',
            'Multiple connection failures detected',
            'Check database server load, increase connection limits, or review connection timeout settings',
            'high'
          );
        }
        return false;
      }

    } catch (error) {
      this.log('FAIL', 'Connection Pool', 'Connection pool monitoring failed', {
        error: error.message
      });
      return false;
    }
  }

  async testPoolConnection(pool, connectionId) {
    const connection = await pool.getConnection();
    try {
      const [rows] = await connection.execute('SELECT ? as connection_id, CONNECTION_ID() as mysql_connection_id', [connectionId]);
      return rows[0];
    } finally {
      connection.release();
    }
  }

  /**
   * Check database performance metrics
   */
  async checkDatabasePerformance() {
    try {
      this.log('INFO', 'Performance Check', 'Analyzing database performance metrics...');

      const connection = await mysql.createConnection(this.dbConfig);

      // Check slow query log status
      const [slowQueryStatus] = await connection.execute('SHOW VARIABLES LIKE "slow_query_log"');
      const [longQueryTime] = await connection.execute('SHOW VARIABLES LIKE "long_query_time"');

      // Check connection statistics
      const [maxConnections] = await connection.execute('SHOW VARIABLES LIKE "max_connections"');
      const [threadsConnected] = await connection.execute('SHOW STATUS LIKE "Threads_connected"');
      const [threadsRunning] = await connection.execute('SHOW STATUS LIKE "Threads_running"');

      // Check table statistics for main tables
      const tableStats = {};
      for (const tableName of Object.keys(this.requiredSchema)) {
        try {
          const [stats] = await connection.execute(
            'SELECT table_rows, data_length, index_length FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
            [this.dbConfig.database, tableName]
          );
          if (stats[0]) {
            tableStats[tableName] = {
              rows: stats[0].table_rows,
              data_size_mb: Math.round(stats[0].data_length / 1024 / 1024 * 100) / 100,
              index_size_mb: Math.round(stats[0].index_length / 1024 / 1024 * 100) / 100
            };
          }
        } catch (tableError) {
          // Skip if table doesn't exist
        }
      }

      await connection.end();

      // Analyze results
      const connectionUsage = parseInt(threadsConnected[0].Value) / parseInt(maxConnections[0].Value) * 100;
      const performanceIssues = [];

      if (connectionUsage > 80) {
        performanceIssues.push('High connection usage');
        this.addRecommendation('MEDIUM', 'Performance',
          'High database connection usage detected',
          'Consider increasing max_connections or optimizing connection pooling',
          'medium'
        );
      }

      if (parseInt(threadsRunning[0].Value) > 10) {
        performanceIssues.push('High concurrent query load');
        this.addRecommendation('MEDIUM', 'Performance',
          'High number of running threads detected',
          'Review slow queries and optimize database performance',
          'medium'
        );
      }

      if (performanceIssues.length === 0) {
        this.log('PASS', 'Performance Check', 'Database performance metrics are healthy', {
          connection_usage_percent: Math.round(connectionUsage),
          max_connections: maxConnections[0].Value,
          threads_connected: threadsConnected[0].Value,
          threads_running: threadsRunning[0].Value,
          slow_query_log: slowQueryStatus[0].Value,
          long_query_time: longQueryTime[0].Value,
          table_statistics: tableStats
        });
        return true;
      } else {
        this.log('WARN', 'Performance Check', 'Performance issues detected', {
          issues: performanceIssues,
          connection_usage_percent: Math.round(connectionUsage),
          threads_running: threadsRunning[0].Value,
          table_statistics: tableStats
        });
        return false;
      }

    } catch (error) {
      this.log('FAIL', 'Performance Check', 'Failed to check database performance', {
        error: error.message
      });
      return false;
    }
  }

  /**
   * Detect and classify MySQL errors with solutions
   */
  getDatabaseConnectionSolution(error) {
    const solutions = {
      'ECONNREFUSED': 'MySQL server is not running. Start MySQL service: sudo systemctl start mysql',
      'ENOTFOUND': 'Database host not found. Check DB_HOST in .env file',
      'ER_ACCESS_DENIED_ERROR': 'Invalid database credentials. Verify DB_USER and DB_PASSWORD in .env file',
      'ER_BAD_DB_ERROR': 'Database does not exist. Create database or check DB_NAME in .env file',
      'ENOENT': 'Socket file not found. Check if MySQL socket path exists: /var/run/mysqld/mysqld.sock',
      'ETIMEDOUT': 'Connection timeout. Check network connectivity or increase connectTimeout',
      'ER_TOO_MANY_CONNECTIONS': 'Too many database connections. Increase max_connections or optimize connection pooling'
    };

    const errorCode = error.code || error.errno;
    return solutions[errorCode] || `Unknown database error (${errorCode}). Check MySQL server status and configuration.`;
  }

  /**
   * Run comprehensive database diagnostic
   */
  async runComprehensiveDiagnostic() {
    console.log('\n🔍 StreamDB MySQL Database Diagnostic Starting...\n');
    console.log('🏗️ Infrastructure: Two-tier offshore VPS with MySQL socket connection');
    console.log('🔒 Security: Local socket connection for maximum security');
    console.log('=' .repeat(70));

    const tests = [
      { name: 'Database Connection', method: 'testDatabaseConnection' },
      { name: 'Schema Validation', method: 'validateDatabaseSchema' },
      { name: 'Connection Pool Health', method: 'monitorConnectionPool' },
      { name: 'Performance Metrics', method: 'checkDatabasePerformance' }
    ];

    for (const test of tests) {
      try {
        await this[test.method]();
      } catch (error) {
        this.log('CRITICAL', test.name, `Diagnostic test failed: ${error.message}`, {
          error: error.message,
          stack: error.stack
        });
      }
      console.log(''); // Add spacing between tests
    }

    // Generate summary report
    await this.generateDiagnosticReport();

    return this.diagnosticResults;
  }

  /**
   * Generate comprehensive diagnostic report
   */
  async generateDiagnosticReport() {
    console.log('\n📊 DIAGNOSTIC SUMMARY REPORT');
    console.log('=' .repeat(50));

    const summary = this.diagnosticResults.summary;
    console.log(`✅ Tests Passed: ${summary.passed}`);
    console.log(`❌ Tests Failed: ${summary.failed}`);
    console.log(`⚠️  Warnings: ${summary.warnings}`);
    console.log(`🚨 Critical Issues: ${summary.critical}`);

    if (this.diagnosticResults.recommendations.length > 0) {
      console.log('\n🔧 RECOMMENDATIONS:');
      console.log('-' .repeat(30));

      this.diagnosticResults.recommendations
        .sort((a, b) => {
          const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        })
        .forEach((rec, index) => {
          console.log(`${index + 1}. [${rec.priority}] ${rec.issue}`);
          console.log(`   Solution: ${rec.solution}`);
          console.log(`   Impact: ${rec.impact}`);
          console.log('');
        });
    }

    // Save detailed report to file
    try {
      const reportPath = path.join(__dirname, '../logs/database-diagnostic-report.json');
      await fs.mkdir(path.dirname(reportPath), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(this.diagnosticResults, null, 2));
      console.log(`📄 Detailed report saved to: ${reportPath}`);
    } catch (error) {
      console.log(`⚠️  Could not save report file: ${error.message}`);
    }

    console.log('\n🎯 NEXT STEPS:');
    console.log('-' .repeat(20));
    if (summary.critical > 0) {
      console.log('🚨 URGENT: Address critical issues immediately');
    } else if (summary.failed > 0) {
      console.log('❌ Fix failed tests before proceeding');
    } else if (summary.warnings > 0) {
      console.log('⚠️  Review warnings for optimization opportunities');
    } else {
      console.log('✅ Database is healthy and ready for production');
    }
  }
}

module.exports = DatabaseDiagnostic;
