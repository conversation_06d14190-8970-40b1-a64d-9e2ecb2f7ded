-- Streaming Database Schema for Alexhost VPS MySQL
-- This schema matches your current MediaItem interface and supports CSV data import

-- Create database (run this first in phpMyAdmin)
-- CREATE DATABASE IF NOT EXISTS streamdb_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE streamdb_database;

-- Categories table for content classification
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('movie', 'series', 'both') DEFAULT 'both',
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_active (is_active),
    INDEX idx_slug (slug)
);

-- Main content table (movies and web series)
CREATE TABLE content (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    year INT NOT NULL,
    type ENUM('movie', 'series', 'requested') NOT NULL,
    category_id INT,
    image VARCHAR(500),
    cover_image VARCHAR(500),
    
    -- Enhanced metadata fields
    tmdb_id VARCHAR(20),
    poster_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    video_links TEXT, -- DEPRECATED: Use secure_video_links instead
    secure_video_links TEXT, -- Encrypted/encoded video links for security
    imdb_rating DECIMAL(3,1),
    runtime VARCHAR(20),
    studio VARCHAR(255),
    tags TEXT,
    trailer VARCHAR(500),
    subtitle_url VARCHAR(500),
    
    -- Publishing and feature flags
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    add_to_carousel BOOLEAN DEFAULT FALSE,
    
    -- Web series specific fields
    total_seasons INT DEFAULT 0,
    total_episodes INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_type (type),
    INDEX idx_year (year),
    INDEX idx_published (is_published),
    INDEX idx_featured (is_featured),
    INDEX idx_carousel (add_to_carousel),
    INDEX idx_created_at (created_at),
    INDEX idx_tmdb_id (tmdb_id),
    INDEX idx_category (category_id),
    
    -- Full-text search index
    FULLTEXT KEY ft_search (title, description, tags)
);

-- Genres table for normalized genre storage
CREATE TABLE genres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug)
);

-- Content-Genre junction table (many-to-many)
CREATE TABLE content_genres (
    content_id VARCHAR(50),
    genre_id INT,
    PRIMARY KEY (content_id, genre_id),
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (genre_id) REFERENCES genres(id) ON DELETE CASCADE
);

-- Languages table for normalized language storage
CREATE TABLE languages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    code VARCHAR(10) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_code (code)
);

-- Content-Language junction table (many-to-many)
CREATE TABLE content_languages (
    content_id VARCHAR(50),
    language_id INT,
    PRIMARY KEY (content_id, language_id),
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (language_id) REFERENCES languages(id) ON DELETE CASCADE
);

-- Quality options table
CREATE TABLE quality_options (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(20) NOT NULL UNIQUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_sort_order (sort_order)
);

-- Content-Quality junction table (many-to-many)
CREATE TABLE content_quality (
    content_id VARCHAR(50),
    quality_id INT,
    PRIMARY KEY (content_id, quality_id),
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (quality_id) REFERENCES quality_options(id) ON DELETE CASCADE
);

-- Audio tracks table
CREATE TABLE audio_tracks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    code VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Content-Audio junction table (many-to-many)
CREATE TABLE content_audio (
    content_id VARCHAR(50),
    audio_id INT,
    PRIMARY KEY (content_id, audio_id),
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (audio_id) REFERENCES audio_tracks(id) ON DELETE CASCADE
);

-- Seasons table for web series
CREATE TABLE seasons (
    id VARCHAR(50) PRIMARY KEY,
    content_id VARCHAR(50) NOT NULL,
    season_number INT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    poster_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_season (content_id, season_number),
    INDEX idx_content_season (content_id, season_number)
);

-- Episodes table for web series
CREATE TABLE episodes (
    id VARCHAR(50) PRIMARY KEY,
    season_id VARCHAR(50) NOT NULL,
    content_id VARCHAR(50) NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_link TEXT, -- DEPRECATED: Use secure_video_links instead
    secure_video_links TEXT, -- Encrypted/encoded video links for security
    runtime VARCHAR(20),
    air_date DATE,
    thumbnail_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (season_id) REFERENCES seasons(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (season_id, episode_number),
    INDEX idx_season_episode (season_id, episode_number),
    INDEX idx_content_episode (content_id, episode_number)
);

-- Admin users table for authentication
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role ENUM('admin', 'moderator') DEFAULT 'admin',
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- Admin sessions table for session management
CREATE TABLE admin_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    session_data JSON,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- Security logs table for audit trail
CREATE TABLE admin_security_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);
