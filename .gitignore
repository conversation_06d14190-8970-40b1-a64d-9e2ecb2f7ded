# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
server.log

# Dependencies
node_modules
server/node_modules

# Build outputs
dist
dist-ssr
build
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
server/.env

# Backup directories (CRITICAL - prevent bloat)
backup_*
backup_deploy_*
*_backup
*.backup

# Temporary files
*.tmp
*.temp
.cache
.temp

# Database files
*.sqlite
*.db

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Security
*.pem
*.key
*.cert
*.crt

# Test and debug files (may contain secrets)
test-*.cjs
test-*.js
debug-*.html
banner-*-test.html
dimension-*-test.html
cross-device-*-test.html
test.html
