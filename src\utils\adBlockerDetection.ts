// This file has been removed as part of adblocker detection system cleanup
// All adblocker detection functionality has been removed from the codebase

// Placeholder interfaces for backward compatibility during cleanup
export interface AdBlockerInfo {
  name: string;
  type: 'extension' | 'browser' | 'antivirus' | 'unknown';
  detected: boolean;
  confidence: number;
  detectionMethod: string;
  disableInstructions: string[];
  whitelistInstructions: string[];
}

export interface AdBlockerDetectionResult {
  hasAdBlocker: boolean;
  detectedBlockers: AdBlockerInfo[];
  totalConfidence: number;
  detectionTimestamp: number;
}

// Placeholder functions for backward compatibility during cleanup
export async function detectAdBlockers(): Promise<AdBlockerDetectionResult> {
  return {
    hasAdBlocker: false,
    detectedBlockers: [],
    totalConfidence: 0,
    detectionTimestamp: Date.now()
  };
}

export async function quickAdBlockerCheck(): Promise<boolean> {
  return false;
}

export async function recheckAdBlockers(): Promise<boolean> {
  return true;
}

export async function attemptOneClickDisable(): Promise<string> {
  return 'Adblocker detection has been disabled';
}

export async function testAdvancedAdBlockerDetection(): Promise<boolean> {
  return false;
}
