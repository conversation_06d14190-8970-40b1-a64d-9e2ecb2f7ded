import { useState, useEffect } from 'react';

interface SetupStatus {
  needsSetup: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useAdminSetup = () => {
  const [setupStatus, setSetupStatus] = useState<SetupStatus>({
    needsSetup: false,
    isLoading: true,
    error: null
  });

  const checkSetupStatus = async () => {
    try {
      setSetupStatus(prev => ({ ...prev, isLoading: true, error: null }));
      
      const response = await fetch('/api/auth/setup-status');
      const data = await response.json();

      if (response.ok) {
        setSetupStatus({
          needsSetup: data.needsSetup,
          isLoading: false,
          error: null
        });
      } else {
        setSetupStatus({
          needsSetup: false,
          isLoading: false,
          error: data.message || 'Failed to check setup status'
        });
      }
    } catch (error) {
      console.error('Setup status check error:', error);
      setSetupStatus({
        needsSetup: false,
        isLoading: false,
        error: 'Network error while checking setup status'
      });
    }
  };

  useEffect(() => {
    checkSetupStatus();
  }, []);

  return {
    ...setupStatus,
    refetchSetupStatus: checkSetupStatus
  };
};

export default useAdminSetup;
