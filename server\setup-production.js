#!/usr/bin/env node

/**
 * StreamDB Online - Production Environment Setup Script
 * 
 * This script helps set up the production environment securely:
 * 1. Generates secure random keys for JWT and sessions
 * 2. Creates secure admin user credentials
 * 3. Sets up proper file permissions
 * 4. Validates database connection
 * 5. Creates necessary directories
 * 
 * Usage: node setup-production.js
 */

const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function generateSecureKey(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

function generateSecurePassword(length = 32) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

function createInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

function question(rl, query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function checkFileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function createDirectories() {
  const dirs = [
    'uploads',
    'uploads/images',
    'uploads/videos', 
    'uploads/subtitles',
    'uploads/temp',
    'logs'
  ];

  log('\n📁 Creating necessary directories...', 'cyan');
  
  for (const dir of dirs) {
    try {
      await fs.mkdir(dir, { recursive: true });
      log(`✅ Created directory: ${dir}`, 'green');
    } catch (error) {
      log(`❌ Failed to create directory ${dir}: ${error.message}`, 'red');
    }
  }
}

async function setupEnvironment() {
  log('\n🚀 StreamDB Online - Production Setup', 'bright');
  log('=====================================', 'bright');
  
  const rl = createInterface();
  
  try {
    // Check if .env already exists
    const envExists = await checkFileExists('.env');
    if (envExists) {
      const overwrite = await question(rl, 
        `${colors.yellow}⚠️  .env file already exists. Overwrite? (y/N): ${colors.reset}`
      );
      if (overwrite.toLowerCase() !== 'y') {
        log('Setup cancelled.', 'yellow');
        return;
      }
    }

    log('\n🔐 Generating secure credentials...', 'cyan');
    
    // Generate secure keys
    const jwtSecret = generateSecureKey(64);
    const sessionSecret = generateSecureKey(64);
    const webhookSecret = generateSecureKey(32);
    
    log('✅ JWT Secret generated', 'green');
    log('✅ Session Secret generated', 'green');
    log('✅ Webhook Secret generated', 'green');

    // Get database configuration
    log('\n💾 Database Configuration', 'cyan');
    const dbName = await question(rl, 'Database name [streamdb_database]: ') || 'streamdb_database';
    const dbUser = await question(rl, 'Database user [dbadmin_streamdb]: ') || 'dbadmin_streamdb';
    const dbPassword = await question(rl, 'Database password: ');
    
    if (!dbPassword) {
      log('❌ Database password is required!', 'red');
      process.exit(1);
    }

    // Get admin configuration
    log('\n👤 Admin User Configuration', 'cyan');
    const adminUsername = await question(rl, 'Admin username [streamdb_admin]: ') || 'streamdb_admin';
    const adminEmail = await question(rl, 'Admin email [<EMAIL>]: ') || '<EMAIL>';
    const adminPassword = generateSecurePassword(24);
    
    log(`✅ Generated secure admin password: ${colors.bright}${adminPassword}${colors.reset}`, 'green');
    log(`${colors.yellow}⚠️  SAVE THIS PASSWORD SECURELY - IT WON'T BE SHOWN AGAIN!${colors.reset}`, 'yellow');

    // Get domain configuration
    log('\n🌐 Domain Configuration', 'cyan');
    const domain = await question(rl, 'Your domain [streamdb.online]: ') || 'streamdb.online';
    const useHttps = await question(rl, 'Use HTTPS? (Y/n): ') || 'y';
    const protocol = useHttps.toLowerCase() === 'n' ? 'http' : 'https';
    const frontendUrl = `${protocol}://${domain}`;

    // Create .env file content
    const envContent = `# StreamDB Online - Production Environment Configuration
# Generated on ${new Date().toISOString()}
# KEEP THIS FILE SECURE - NEVER COMMIT TO VERSION CONTROL

# Database Configuration for Alexhost VPS - LOCAL CONNECTION (ULTRA SECURE)
DB_HOST=localhost
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=${dbName}
DB_USER=${dbUser}
DB_PASSWORD=${dbPassword}

# JWT Configuration - SECURE KEYS GENERATED
JWT_SECRET=${jwtSecret}
JWT_EXPIRES_IN=24h

# Session Configuration - SECURE KEYS GENERATED
SESSION_SECRET=${sessionSecret}
SESSION_TIMEOUT=86400000

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# CORS Configuration
FRONTEND_URL=${frontendUrl}
CORS_ORIGIN=${frontendUrl},http://localhost:5173

# Server Configuration
PORT=3001
NODE_ENV=production

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp,image/gif
ALLOWED_VIDEO_TYPES=video/mp4,video/webm,video/ogg

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# Logging
LOG_LEVEL=info
LOG_FILE=logs/server.log

# Webhook Configuration for Auto-Deployment
WEBHOOK_SECRET=${webhookSecret}
WEBHOOK_PORT=9000

# Admin User Configuration (for initial setup)
ADMIN_USERNAME=${adminUsername}
ADMIN_EMAIL=${adminEmail}
ADMIN_PASSWORD=${adminPassword}
`;

    // Write .env file
    await fs.writeFile('.env', envContent);
    log('\n✅ .env file created successfully!', 'green');

    // Set secure file permissions
    try {
      await fs.chmod('.env', 0o600);
      log('✅ Set secure file permissions (600) on .env', 'green');
    } catch (error) {
      log(`⚠️  Could not set file permissions: ${error.message}`, 'yellow');
    }

    // Create directories
    await createDirectories();

    // Display summary
    log('\n🎉 Production Setup Complete!', 'bright');
    log('===============================', 'bright');
    log(`📧 Admin Email: ${adminEmail}`, 'cyan');
    log(`👤 Admin Username: ${adminUsername}`, 'cyan');
    log(`🔑 Admin Password: ${colors.bright}${adminPassword}${colors.reset}`, 'green');
    log(`🌐 Frontend URL: ${frontendUrl}`, 'cyan');
    log(`🔗 Webhook Secret: ${webhookSecret.substring(0, 8)}...`, 'cyan');
    
    log('\n📋 Next Steps:', 'yellow');
    log('1. Save the admin password in a secure location', 'yellow');
    log('2. Run: npm install', 'yellow');
    log('3. Set up your database using database/schema.sql', 'yellow');
    log('4. Run: npm start', 'yellow');
    log('5. Configure GitHub webhook with the generated secret', 'yellow');
    
    log('\n🔒 Security Notes:', 'red');
    log('- Never commit the .env file to version control', 'red');
    log('- Regularly rotate your secrets', 'red');
    log('- Monitor your security logs', 'red');
    log('- Use HTTPS in production', 'red');

  } catch (error) {
    log(`\n❌ Setup failed: ${error.message}`, 'red');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run setup if called directly
if (require.main === module) {
  setupEnvironment().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = { setupEnvironment, generateSecureKey, generateSecurePassword };
